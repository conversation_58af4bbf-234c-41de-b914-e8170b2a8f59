# WheelBot Agent - macOS Version

🎰 **Java Agent để tự động hóa chức năng Quay Bách Bảo Rương trong game Naruto Ninja Saga trên macOS**

## 📋 Yêu Cầu Hệ Thống

### ✅ **Hệ Điều Hành:**
- **macOS 10.14** (Mojave) hoặc cao hơn
- **Apple Silicon (M1/M2)** hoặc **Intel Mac** đều được hỗ trợ

### ☕ **Java:**
- **Java 8** hoặc cao hơn
- **JDK** (không chỉ JRE) để compile code
- Khuyến nghị: **OpenJDK 17** cho Apple Silicon

### 🎮 **Game:**
- File **core.jar** (game Naruto Ninja Saga)
- Game phải có **Main-Class**: `com.donglh.narutoninjasaga.desktop.DesktopLauncher`

## 🚀 Cài Đặt Nhanh

### **Bước 1: Setup Tự Động**
```bash
# Tải về và chạy setup
chmod +x setup-macos.sh
./setup-macos.sh
```

### **Bước 2: Chạy Game**
```bash
# Launcher tương tác
./launcher-macos.sh

# Hoặc chạy trực tiếp
./build-and-run.sh
```

## 📦 Cài Đặt Java trên macOS

### **Cách 1: Oracle JDK (Khuyến nghị)**
```bash
# Tải từ: https://www.oracle.com/java/technologies/downloads/
# Chọn macOS version phù hợp với chip của bạn
```

### **Cách 2: Homebrew**
```bash
# Cài Homebrew nếu chưa có
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Cài Java
brew install openjdk@17

# Cho Apple Silicon
brew install openjdk@17
sudo ln -sfn /opt/homebrew/opt/openjdk@17/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk-17.jdk
```

### **Cách 3: SDKMAN**
```bash
# Cài SDKMAN
curl -s "https://get.sdkman.io" | bash

# Cài Java
sdk install java 17.0.7-oracle
```

## 🎯 Cách Sử Dụng

### **1. Launcher Tương Tác (Khuyến nghị)**
```bash
./launcher-macos.sh
```

**Menu options:**
- **1.** 🚀 Launch with Auto Mode (tự động quay)
- **2.** 🎮 Launch with Manual Mode (quay thủ công)
- **3.** 🐛 Launch with Debug Mode (debug chi tiết)
- **4.** ⚙️ Custom Configuration (cấu hình tùy chỉnh)
- **5.** 🔍 Check System & Files (kiểm tra hệ thống)

### **2. Command Line**
```bash
# Auto mode
./launcher-macos.sh --auto

# Manual mode  
./launcher-macos.sh --manual

# Debug mode
./launcher-macos.sh --debug

# Build và run trực tiếp
./build-and-run.sh

# Với tham số tùy chỉnh
./build-and-run.sh --debug --delay 2000 --auto
```

### **3. Runtime Commands**
Khi game đang chạy, bạn có thể gõ các lệnh:
```
start   - Bật auto mode
stop    - Tắt auto mode  
spin    - Quay thủ công
status  - Xem trạng thái
verify  - Kiểm tra hook status
force   - Force spin (bypass checks)
memory  - Dump game memory
inject  - Can thiệp trực tiếp
help    - Hiện danh sách lệnh
exit    - Thoát game
```

## ⚙️ Cấu Hình

### **Tham Số Agent:**
- **debug**: `true/false` - Bật/tắt debug mode
- **delay**: `1000-10000` - Delay giữa các lần quay (ms)
- **auto**: `true/false` - Bật/tắt auto mode

### **Ví Dụ:**
```bash
java -javaagent:wheel-bot-agent.jar=debug=true,delay=3000,auto=true -cp core.jar com.donglh.narutoninjasaga.desktop.DesktopLauncher
```

## 🔧 Troubleshooting

### **❌ "Java not found"**
```bash
# Kiểm tra Java
java -version
javac -version

# Cài Java nếu chưa có
brew install openjdk@17
```

### **❌ "Permission denied"**
```bash
# Make scripts executable
chmod +x *.sh

# Hoặc run với bash
bash launcher-macos.sh
```

### **❌ "Cannot be opened because it is from an unidentified developer"**
```bash
# Method 1: System Preferences
# Go to System Preferences > Security & Privacy > Allow

# Method 2: Remove quarantine
xattr -d com.apple.quarantine wheel-bot-agent.jar

# Method 3: Right-click > Open
```

### **❌ "Main-Class not found"**
```bash
# Kiểm tra MANIFEST.MF trong core.jar
unzip -p core.jar META-INF/MANIFEST.MF | grep Main-Class

# Đảm bảo có: Main-Class: com.donglh.narutoninjasaga.desktop.DesktopLauncher
```

### **❌ Game không hook được**
```bash
# Chạy với debug mode
./launcher-macos.sh --debug

# Kiểm tra hook status trong game
verify

# Force injection
inject
```

## 🍎 Tối Ưu Cho Apple Silicon

### **JVM Options được áp dụng tự động:**
```bash
-Djava.awt.headless=false
-Xdock:name=WheelBot
```

### **Kiểm tra architecture:**
```bash
uname -m
# arm64 = Apple Silicon
# x86_64 = Intel Mac
```

## 📁 Cấu Trúc File

```
wheel-bot-agent/
├── WheelBotAgent.java          # Main agent class
├── NetworkHook.java            # Network hooking
├── WheelBotLauncher.java       # Java launcher
├── HookVerifier.java           # Hook verification
├── MANIFEST.MF                 # Agent manifest
├── build-and-run.sh           # Build & run script
├── launcher-macos.sh          # Interactive launcher
├── setup-macos.sh             # Setup script
├── README-macOS.md            # This file
└── core.jar                   # Game JAR (you provide)
```

## 🔒 Bảo Mật

### **Gatekeeper:**
- macOS có thể block unsigned JARs
- Sử dụng `xattr -d com.apple.quarantine` để remove quarantine
- Hoặc allow trong System Preferences > Security & Privacy

### **SIP (System Integrity Protection):**
- Agent không cần disable SIP
- Chỉ hook vào game process, không modify system

### **Permissions:**
- Không cần sudo để chạy
- Chỉ cần quyền đọc/ghi trong thư mục hiện tại

## 🎮 Cách Thức Hoạt Động

### **1. Agent Injection:**
```bash
java -javaagent:wheel-bot-agent.jar -cp core.jar com.donglh.narutoninjasaga.desktop.DesktopLauncher
```

### **2. Class Loading Hook:**
- Hook vào `DesktopLauncher` khi game start
- Intercept các class quan trọng: `kb.java`, `ak.java`, `aw.java`
- Wait for game initialization

### **3. Runtime Hook:**
- Monitor wheel interface state
- Auto detect khi wheel mở
- Direct UI interaction hoặc network message injection

### **4. Verification:**
- Comprehensive hook verification
- Memory access testing
- Method invocation testing

## 📞 Hỗ Trợ

### **Debug Information:**
```bash
# Chạy với debug mode
./launcher-macos.sh --debug

# Check system info
system_profiler SPSoftwareDataType
java -version
uname -a
```

### **Log Files:**
- Console output shows all hook status
- Use `verify` command to check hook health
- Use `memory` command to dump game state

### **Common Issues:**
1. **Java version incompatibility** - Use Java 8+
2. **Architecture mismatch** - Ensure Java matches your Mac architecture
3. **Game JAR corruption** - Re-download core.jar
4. **Permission issues** - Check file permissions and Gatekeeper settings

## 🎉 Thành Công!

Khi setup thành công, bạn sẽ thấy:
```
✅ [VERIFIED] Wheel UI class hooked successfully
✅ [VERIFIED] Network class hooked successfully  
🎮 [HOOK] Game is ready! Installing runtime hooks...
🎉 [HOOK] All hooks installed successfully!
```

**Happy spinning! 🎰**
