<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.badlogicgames.gdx</groupId>
    <artifactId>gdx-parent</artifactId>
    <version>1.9.11</version>
    <relativePath>../../pom.xml</relativePath>
  </parent>

  <artifactId>gdx-backend-lwjgl</artifactId>
  <packaging>jar</packaging>
  <name>libGDX LWJGL Backend</name>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>gdx</artifactId>
      <version>${project.version}</version>
    </dependency>
    
    <dependency>
      <groupId>org.lwjgl.lwjgl</groupId>
      <artifactId>lwjgl</artifactId>
      <version>${lwjgl.version}</version>
    </dependency>

    <dependency>
      <groupId>org.lwjgl.lwjgl</groupId>
      <artifactId>lwjgl_util</artifactId>
      <version>${lwjgl.version}</version>
    </dependency>
    
    <dependency>
      <groupId>com.badlogicgames.jlayer</groupId>
      <artifactId>jlayer</artifactId>
      <version>1.0.1-gdx</version>
    </dependency>

    <dependency>
        <groupId>org.jcraft</groupId>
        <artifactId>jorbis</artifactId>
        <version>0.0.17</version>
    </dependency>
  </dependencies>

  <build>
    <sourceDirectory>src</sourceDirectory>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>com/badlogic/gdx/backends/gwt/emu/java/lang/System.java</exclude>
          </excludes>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-sources</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
