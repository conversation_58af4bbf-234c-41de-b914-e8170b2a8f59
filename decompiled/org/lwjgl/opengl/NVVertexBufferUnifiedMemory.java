/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.LongBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVVertexBufferUnifiedMemory {
    public static final int GL_VERTEX_ATTRIB_ARRAY_UNIFIED_NV = 36638;
    public static final int GL_ELEMENT_ARRAY_UNIFIED_NV = 36639;
    public static final int GL_VERTEX_ATTRIB_ARRAY_ADDRESS_NV = 36640;
    public static final int GL_TEXTURE_COORD_ARRAY_ADDRESS_NV = 36645;
    public static final int GL_VERTEX_ARRAY_ADDRESS_NV = 36641;
    public static final int GL_NORMAL_ARRAY_ADDRESS_NV = 36642;
    public static final int GL_COLOR_ARRAY_ADDRESS_NV = 36643;
    public static final int GL_INDEX_ARRAY_ADDRESS_NV = 36644;
    public static final int GL_EDGE_FLAG_ARRAY_ADDRESS_NV = 36646;
    public static final int GL_SECONDARY_COLOR_ARRAY_ADDRESS_NV = 36647;
    public static final int GL_FOG_COORD_ARRAY_ADDRESS_NV = 36648;
    public static final int GL_ELEMENT_ARRAY_ADDRESS_NV = 36649;
    public static final int GL_VERTEX_ATTRIB_ARRAY_LENGTH_NV = 36650;
    public static final int GL_TEXTURE_COORD_ARRAY_LENGTH_NV = 36655;
    public static final int GL_VERTEX_ARRAY_LENGTH_NV = 36651;
    public static final int GL_NORMAL_ARRAY_LENGTH_NV = 36652;
    public static final int GL_COLOR_ARRAY_LENGTH_NV = 36653;
    public static final int GL_INDEX_ARRAY_LENGTH_NV = 36654;
    public static final int GL_EDGE_FLAG_ARRAY_LENGTH_NV = 36656;
    public static final int GL_SECONDARY_COLOR_ARRAY_LENGTH_NV = 36657;
    public static final int GL_FOG_COORD_ARRAY_LENGTH_NV = 36658;
    public static final int GL_ELEMENT_ARRAY_LENGTH_NV = 36659;

    private NVVertexBufferUnifiedMemory() {
    }

    public static void glBufferAddressRangeNV(int n, int n2, long l, long l2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glBufferAddressRangeNV;
        BufferChecks.checkFunctionAddress(l3);
        NVVertexBufferUnifiedMemory.nglBufferAddressRangeNV(n, n2, l, l2, l3);
    }

    static native void nglBufferAddressRangeNV(int var0, int var1, long var2, long var4, long var6);

    public static void glVertexFormatNV(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexFormatNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexBufferUnifiedMemory.nglVertexFormatNV(n, n2, n3, l);
    }

    static native void nglVertexFormatNV(int var0, int var1, int var2, long var3);

    public static void glNormalFormatNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormalFormatNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexBufferUnifiedMemory.nglNormalFormatNV(n, n2, l);
    }

    static native void nglNormalFormatNV(int var0, int var1, long var2);

    public static void glColorFormatNV(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorFormatNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexBufferUnifiedMemory.nglColorFormatNV(n, n2, n3, l);
    }

    static native void nglColorFormatNV(int var0, int var1, int var2, long var3);

    public static void glIndexFormatNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIndexFormatNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexBufferUnifiedMemory.nglIndexFormatNV(n, n2, l);
    }

    static native void nglIndexFormatNV(int var0, int var1, long var2);

    public static void glTexCoordFormatNV(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoordFormatNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexBufferUnifiedMemory.nglTexCoordFormatNV(n, n2, n3, l);
    }

    static native void nglTexCoordFormatNV(int var0, int var1, int var2, long var3);

    public static void glEdgeFlagFormatNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEdgeFlagFormatNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexBufferUnifiedMemory.nglEdgeFlagFormatNV(n, l);
    }

    static native void nglEdgeFlagFormatNV(int var0, long var1);

    public static void glSecondaryColorFormatNV(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSecondaryColorFormatNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexBufferUnifiedMemory.nglSecondaryColorFormatNV(n, n2, n3, l);
    }

    static native void nglSecondaryColorFormatNV(int var0, int var1, int var2, long var3);

    public static void glFogCoordFormatNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFogCoordFormatNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexBufferUnifiedMemory.nglFogCoordFormatNV(n, n2, l);
    }

    static native void nglFogCoordFormatNV(int var0, int var1, long var2);

    public static void glVertexAttribFormatNV(int n, int n2, int n3, boolean bl, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribFormatNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexBufferUnifiedMemory.nglVertexAttribFormatNV(n, n2, n3, bl, n4, l);
    }

    static native void nglVertexAttribFormatNV(int var0, int var1, int var2, boolean var3, int var4, long var5);

    public static void glVertexAttribIFormatNV(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribIFormatNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexBufferUnifiedMemory.nglVertexAttribIFormatNV(n, n2, n3, n4, l);
    }

    static native void nglVertexAttribIFormatNV(int var0, int var1, int var2, int var3, long var4);

    public static void glGetIntegeruNV(int n, int n2, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetIntegerui64i_vNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 1);
        NVVertexBufferUnifiedMemory.nglGetIntegerui64i_vNV(n, n2, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetIntegerui64i_vNV(int var0, int var1, long var2, long var4);

    public static long glGetIntegerui64NV(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetIntegerui64i_vNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferLong((ContextCapabilities)object);
        NVVertexBufferUnifiedMemory.nglGetIntegerui64i_vNV(n, n2, MemoryUtil.getAddress((LongBuffer)object), l);
        return ((LongBuffer)object).get(0);
    }
}
