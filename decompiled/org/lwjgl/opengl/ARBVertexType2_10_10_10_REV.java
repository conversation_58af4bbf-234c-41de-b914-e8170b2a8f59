/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.opengl.GL33;

public final class ARBVertexType2_10_10_10_REV {
    public static final int GL_UNSIGNED_INT_2_10_10_10_REV = 33640;
    public static final int GL_INT_2_10_10_10_REV = 36255;

    private ARBVertexType2_10_10_10_REV() {
    }

    public static void glVertexP2ui(int n, int n2) {
        GL33.glVertexP2ui(n, n2);
    }

    public static void glVertexP3ui(int n, int n2) {
        GL33.glVertexP3ui(n, n2);
    }

    public static void glVertexP4ui(int n, int n2) {
        GL33.glVertexP4ui(n, n2);
    }

    public static void glVertexP2u(int n, IntBuffer intBuffer) {
        GL33.glVertexP2u(n, intBuffer);
    }

    public static void glVertexP3u(int n, IntBuffer intBuffer) {
        GL33.glVertexP3u(n, intBuffer);
    }

    public static void glVertexP4u(int n, IntBuffer intBuffer) {
        GL33.glVertexP4u(n, intBuffer);
    }

    public static void glTexCoordP1ui(int n, int n2) {
        GL33.glTexCoordP1ui(n, n2);
    }

    public static void glTexCoordP2ui(int n, int n2) {
        GL33.glTexCoordP2ui(n, n2);
    }

    public static void glTexCoordP3ui(int n, int n2) {
        GL33.glTexCoordP3ui(n, n2);
    }

    public static void glTexCoordP4ui(int n, int n2) {
        GL33.glTexCoordP4ui(n, n2);
    }

    public static void glTexCoordP1u(int n, IntBuffer intBuffer) {
        GL33.glTexCoordP1u(n, intBuffer);
    }

    public static void glTexCoordP2u(int n, IntBuffer intBuffer) {
        GL33.glTexCoordP2u(n, intBuffer);
    }

    public static void glTexCoordP3u(int n, IntBuffer intBuffer) {
        GL33.glTexCoordP3u(n, intBuffer);
    }

    public static void glTexCoordP4u(int n, IntBuffer intBuffer) {
        GL33.glTexCoordP4u(n, intBuffer);
    }

    public static void glMultiTexCoordP1ui(int n, int n2, int n3) {
        GL33.glMultiTexCoordP1ui(n, n2, n3);
    }

    public static void glMultiTexCoordP2ui(int n, int n2, int n3) {
        GL33.glMultiTexCoordP2ui(n, n2, n3);
    }

    public static void glMultiTexCoordP3ui(int n, int n2, int n3) {
        GL33.glMultiTexCoordP3ui(n, n2, n3);
    }

    public static void glMultiTexCoordP4ui(int n, int n2, int n3) {
        GL33.glMultiTexCoordP4ui(n, n2, n3);
    }

    public static void glMultiTexCoordP1u(int n, int n2, IntBuffer intBuffer) {
        GL33.glMultiTexCoordP1u(n, n2, intBuffer);
    }

    public static void glMultiTexCoordP2u(int n, int n2, IntBuffer intBuffer) {
        GL33.glMultiTexCoordP2u(n, n2, intBuffer);
    }

    public static void glMultiTexCoordP3u(int n, int n2, IntBuffer intBuffer) {
        GL33.glMultiTexCoordP3u(n, n2, intBuffer);
    }

    public static void glMultiTexCoordP4u(int n, int n2, IntBuffer intBuffer) {
        GL33.glMultiTexCoordP4u(n, n2, intBuffer);
    }

    public static void glNormalP3ui(int n, int n2) {
        GL33.glNormalP3ui(n, n2);
    }

    public static void glNormalP3u(int n, IntBuffer intBuffer) {
        GL33.glNormalP3u(n, intBuffer);
    }

    public static void glColorP3ui(int n, int n2) {
        GL33.glColorP3ui(n, n2);
    }

    public static void glColorP4ui(int n, int n2) {
        GL33.glColorP4ui(n, n2);
    }

    public static void glColorP3u(int n, IntBuffer intBuffer) {
        GL33.glColorP3u(n, intBuffer);
    }

    public static void glColorP4u(int n, IntBuffer intBuffer) {
        GL33.glColorP4u(n, intBuffer);
    }

    public static void glSecondaryColorP3ui(int n, int n2) {
        GL33.glSecondaryColorP3ui(n, n2);
    }

    public static void glSecondaryColorP3u(int n, IntBuffer intBuffer) {
        GL33.glSecondaryColorP3u(n, intBuffer);
    }

    public static void glVertexAttribP1ui(int n, int n2, boolean bl, int n3) {
        GL33.glVertexAttribP1ui(n, n2, bl, n3);
    }

    public static void glVertexAttribP2ui(int n, int n2, boolean bl, int n3) {
        GL33.glVertexAttribP2ui(n, n2, bl, n3);
    }

    public static void glVertexAttribP3ui(int n, int n2, boolean bl, int n3) {
        GL33.glVertexAttribP3ui(n, n2, bl, n3);
    }

    public static void glVertexAttribP4ui(int n, int n2, boolean bl, int n3) {
        GL33.glVertexAttribP4ui(n, n2, bl, n3);
    }

    public static void glVertexAttribP1u(int n, int n2, boolean bl, IntBuffer intBuffer) {
        GL33.glVertexAttribP1u(n, n2, bl, intBuffer);
    }

    public static void glVertexAttribP2u(int n, int n2, boolean bl, IntBuffer intBuffer) {
        GL33.glVertexAttribP2u(n, n2, bl, intBuffer);
    }

    public static void glVertexAttribP3u(int n, int n2, boolean bl, IntBuffer intBuffer) {
        GL33.glVertexAttribP3u(n, n2, bl, intBuffer);
    }

    public static void glVertexAttribP4u(int n, int n2, boolean bl, IntBuffer intBuffer) {
        GL33.glVertexAttribP4u(n, n2, bl, intBuffer);
    }
}
