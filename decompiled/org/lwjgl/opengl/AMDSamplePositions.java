/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.FloatBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class AMDSamplePositions {
    public static final int GL_SUBSAMPLE_DISTANCE_AMD = 34879;

    private AMDSamplePositions() {
    }

    public static void glSetMultisampleAMD(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSetMultisamplefvAMD;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 2);
        AMDSamplePositions.nglSetMultisamplefvAMD(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglSetMultisamplefvAMD(int var0, int var1, long var2, long var4);
}
