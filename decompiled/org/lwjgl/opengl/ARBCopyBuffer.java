/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.opengl.GL31;

public final class ARBCopyBuffer {
    public static final int GL_COPY_READ_BUFFER = 36662;
    public static final int GL_COPY_WRITE_BUFFER = 36663;

    private ARBCopyBuffer() {
    }

    public static void glCopyBufferSubData(int n, int n2, long l, long l2, long l3) {
        GL31.glCopyBufferSubData(n, n2, l, l2, l3);
    }
}
