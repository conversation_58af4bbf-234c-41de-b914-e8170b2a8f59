/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVConditionalRender {
    public static final int GL_QUERY_WAIT_NV = 36371;
    public static final int GL_QUERY_NO_WAIT_NV = 36372;
    public static final int GL_QUERY_BY_REGION_WAIT_NV = 36373;
    public static final int GL_QUERY_BY_REGION_NO_WAIT_NV = 36374;

    private NVConditionalRender() {
    }

    public static void glBeginConditionalRenderNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBeginConditionalRenderNV;
        BufferChecks.checkFunctionAddress(l);
        NVConditionalRender.nglBeginConditionalRenderNV(n, n2, l);
    }

    static native void nglBeginConditionalRenderNV(int var0, int var1, long var2);

    public static void glEndConditionalRenderNV() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEndConditionalRenderNV;
        BufferChecks.checkFunctionAddress(l);
        NVConditionalRender.nglEndConditionalRenderNV(l);
    }

    static native void nglEndConditionalRenderNV(long var0);
}
