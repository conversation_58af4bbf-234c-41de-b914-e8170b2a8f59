/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ATIVertexStreams {
    public static final int GL_MAX_VERTEX_STREAMS_ATI = 34667;
    public static final int GL_VERTEX_SOURCE_ATI = 34668;
    public static final int GL_VERTEX_STREAM0_ATI = 34669;
    public static final int GL_VERTEX_STREAM1_ATI = 34670;
    public static final int GL_VERTEX_STREAM2_ATI = 34671;
    public static final int GL_VERTEX_STREAM3_ATI = 34672;
    public static final int GL_VERTEX_STREAM4_ATI = 34673;
    public static final int GL_VERTEX_STREAM5_ATI = 34674;
    public static final int GL_VERTEX_STREAM6_ATI = 34675;
    public static final int GL_VERTEX_STREAM7_ATI = 34676;

    private ATIVertexStreams() {
    }

    public static void glVertexStream2fATI(int n, float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexStream2fATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglVertexStream2fATI(n, f, f2, l);
    }

    static native void nglVertexStream2fATI(int var0, float var1, float var2, long var3);

    public static void glVertexStream2dATI(int n, double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexStream2dATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglVertexStream2dATI(n, d, d2, l);
    }

    static native void nglVertexStream2dATI(int var0, double var1, double var3, long var5);

    public static void glVertexStream2iATI(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexStream2iATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglVertexStream2iATI(n, n2, n3, l);
    }

    static native void nglVertexStream2iATI(int var0, int var1, int var2, long var3);

    public static void glVertexStream2sATI(int n, short s, short s2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexStream2sATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglVertexStream2sATI(n, s, s2, l);
    }

    static native void nglVertexStream2sATI(int var0, short var1, short var2, long var3);

    public static void glVertexStream3fATI(int n, float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexStream3fATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglVertexStream3fATI(n, f, f2, f3, l);
    }

    static native void nglVertexStream3fATI(int var0, float var1, float var2, float var3, long var4);

    public static void glVertexStream3dATI(int n, double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexStream3dATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglVertexStream3dATI(n, d, d2, d3, l);
    }

    static native void nglVertexStream3dATI(int var0, double var1, double var3, double var5, long var7);

    public static void glVertexStream3iATI(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexStream3iATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglVertexStream3iATI(n, n2, n3, n4, l);
    }

    static native void nglVertexStream3iATI(int var0, int var1, int var2, int var3, long var4);

    public static void glVertexStream3sATI(int n, short s, short s2, short s3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexStream3sATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglVertexStream3sATI(n, s, s2, s3, l);
    }

    static native void nglVertexStream3sATI(int var0, short var1, short var2, short var3, long var4);

    public static void glVertexStream4fATI(int n, float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexStream4fATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglVertexStream4fATI(n, f, f2, f3, f4, l);
    }

    static native void nglVertexStream4fATI(int var0, float var1, float var2, float var3, float var4, long var5);

    public static void glVertexStream4dATI(int n, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexStream4dATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglVertexStream4dATI(n, d, d2, d3, d4, l);
    }

    static native void nglVertexStream4dATI(int var0, double var1, double var3, double var5, double var7, long var9);

    public static void glVertexStream4iATI(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexStream4iATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglVertexStream4iATI(n, n2, n3, n4, n5, l);
    }

    static native void nglVertexStream4iATI(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glVertexStream4sATI(int n, short s, short s2, short s3, short s4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexStream4sATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglVertexStream4sATI(n, s, s2, s3, s4, l);
    }

    static native void nglVertexStream4sATI(int var0, short var1, short var2, short var3, short var4, long var5);

    public static void glNormalStream3bATI(int n, byte by, byte by2, byte by3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormalStream3bATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglNormalStream3bATI(n, by, by2, by3, l);
    }

    static native void nglNormalStream3bATI(int var0, byte var1, byte var2, byte var3, long var4);

    public static void glNormalStream3fATI(int n, float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormalStream3fATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglNormalStream3fATI(n, f, f2, f3, l);
    }

    static native void nglNormalStream3fATI(int var0, float var1, float var2, float var3, long var4);

    public static void glNormalStream3dATI(int n, double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormalStream3dATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglNormalStream3dATI(n, d, d2, d3, l);
    }

    static native void nglNormalStream3dATI(int var0, double var1, double var3, double var5, long var7);

    public static void glNormalStream3iATI(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormalStream3iATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglNormalStream3iATI(n, n2, n3, n4, l);
    }

    static native void nglNormalStream3iATI(int var0, int var1, int var2, int var3, long var4);

    public static void glNormalStream3sATI(int n, short s, short s2, short s3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormalStream3sATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglNormalStream3sATI(n, s, s2, s3, l);
    }

    static native void nglNormalStream3sATI(int var0, short var1, short var2, short var3, long var4);

    public static void glClientActiveVertexStreamATI(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClientActiveVertexStreamATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglClientActiveVertexStreamATI(n, l);
    }

    static native void nglClientActiveVertexStreamATI(int var0, long var1);

    public static void glVertexBlendEnvfATI(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexBlendEnvfATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglVertexBlendEnvfATI(n, f, l);
    }

    static native void nglVertexBlendEnvfATI(int var0, float var1, long var2);

    public static void glVertexBlendEnviATI(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexBlendEnviATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexStreams.nglVertexBlendEnviATI(n, n2, l);
    }

    static native void nglVertexBlendEnviATI(int var0, int var1, long var2);
}
