/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ARBShaderObjects {
    public static final int GL_PROGRAM_OBJECT_ARB = 35648;
    public static final int GL_OBJECT_TYPE_ARB = 35662;
    public static final int GL_OBJECT_SUBTYPE_ARB = 35663;
    public static final int GL_OBJECT_DELETE_STATUS_ARB = 35712;
    public static final int GL_OBJECT_COMPILE_STATUS_ARB = 35713;
    public static final int GL_OBJECT_LINK_STATUS_ARB = 35714;
    public static final int GL_OBJECT_VALIDATE_STATUS_ARB = 35715;
    public static final int GL_OBJECT_INFO_LOG_LENGTH_ARB = 35716;
    public static final int GL_OBJECT_ATTACHED_OBJECTS_ARB = 35717;
    public static final int GL_OBJECT_ACTIVE_UNIFORMS_ARB = 35718;
    public static final int GL_OBJECT_ACTIVE_UNIFORM_MAX_LENGTH_ARB = 35719;
    public static final int GL_OBJECT_SHADER_SOURCE_LENGTH_ARB = 35720;
    public static final int GL_SHADER_OBJECT_ARB = 35656;
    public static final int GL_FLOAT_VEC2_ARB = 35664;
    public static final int GL_FLOAT_VEC3_ARB = 35665;
    public static final int GL_FLOAT_VEC4_ARB = 35666;
    public static final int GL_INT_VEC2_ARB = 35667;
    public static final int GL_INT_VEC3_ARB = 35668;
    public static final int GL_INT_VEC4_ARB = 35669;
    public static final int GL_BOOL_ARB = 35670;
    public static final int GL_BOOL_VEC2_ARB = 35671;
    public static final int GL_BOOL_VEC3_ARB = 35672;
    public static final int GL_BOOL_VEC4_ARB = 35673;
    public static final int GL_FLOAT_MAT2_ARB = 35674;
    public static final int GL_FLOAT_MAT3_ARB = 35675;
    public static final int GL_FLOAT_MAT4_ARB = 35676;
    public static final int GL_SAMPLER_1D_ARB = 35677;
    public static final int GL_SAMPLER_2D_ARB = 35678;
    public static final int GL_SAMPLER_3D_ARB = 35679;
    public static final int GL_SAMPLER_CUBE_ARB = 35680;
    public static final int GL_SAMPLER_1D_SHADOW_ARB = 35681;
    public static final int GL_SAMPLER_2D_SHADOW_ARB = 35682;
    public static final int GL_SAMPLER_2D_RECT_ARB = 35683;
    public static final int GL_SAMPLER_2D_RECT_SHADOW_ARB = 35684;

    private ARBShaderObjects() {
    }

    public static void glDeleteObjectARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteObjectARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglDeleteObjectARB(n, l);
    }

    static native void nglDeleteObjectARB(int var0, long var1);

    public static int glGetHandleARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetHandleARB;
        BufferChecks.checkFunctionAddress(l);
        n = ARBShaderObjects.nglGetHandleARB(n, l);
        return n;
    }

    static native int nglGetHandleARB(int var0, long var1);

    public static void glDetachObjectARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDetachObjectARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglDetachObjectARB(n, n2, l);
    }

    static native void nglDetachObjectARB(int var0, int var1, long var2);

    public static int glCreateShaderObjectARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateShaderObjectARB;
        BufferChecks.checkFunctionAddress(l);
        n = ARBShaderObjects.nglCreateShaderObjectARB(n, l);
        return n;
    }

    static native int nglCreateShaderObjectARB(int var0, long var1);

    public static void glShaderSourceARB(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glShaderSourceARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        ARBShaderObjects.nglShaderSourceARB(n, 1, MemoryUtil.getAddress(byteBuffer), byteBuffer.remaining(), l);
    }

    static native void nglShaderSourceARB(int var0, int var1, long var2, int var4, long var5);

    public static void glShaderSourceARB(int n, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glShaderSourceARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglShaderSourceARB(n, 1, APIUtil.getBuffer(contextCapabilities, charSequence), charSequence.length(), l);
    }

    public static void glShaderSourceARB(int n, CharSequence[] charSequenceArray) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glShaderSourceARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkArray(charSequenceArray);
        ARBShaderObjects.nglShaderSourceARB3(n, charSequenceArray.length, APIUtil.getBuffer(contextCapabilities, charSequenceArray), APIUtil.getLengths(contextCapabilities, charSequenceArray), l);
    }

    static native void nglShaderSourceARB3(int var0, int var1, long var2, long var4, long var6);

    public static void glCompileShaderARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompileShaderARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglCompileShaderARB(n, l);
    }

    static native void nglCompileShaderARB(int var0, long var1);

    public static int glCreateProgramObjectARB() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateProgramObjectARB;
        BufferChecks.checkFunctionAddress(l);
        int n = ARBShaderObjects.nglCreateProgramObjectARB(l);
        return n;
    }

    static native int nglCreateProgramObjectARB(long var0);

    public static void glAttachObjectARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glAttachObjectARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglAttachObjectARB(n, n2, l);
    }

    static native void nglAttachObjectARB(int var0, int var1, long var2);

    public static void glLinkProgramARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLinkProgramARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglLinkProgramARB(n, l);
    }

    static native void nglLinkProgramARB(int var0, long var1);

    public static void glUseProgramObjectARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUseProgramObjectARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglUseProgramObjectARB(n, l);
    }

    static native void nglUseProgramObjectARB(int var0, long var1);

    public static void glValidateProgramARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glValidateProgramARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglValidateProgramARB(n, l);
    }

    static native void nglValidateProgramARB(int var0, long var1);

    public static void glUniform1fARB(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform1fARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglUniform1fARB(n, f, l);
    }

    static native void nglUniform1fARB(int var0, float var1, long var2);

    public static void glUniform2fARB(int n, float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform2fARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglUniform2fARB(n, f, f2, l);
    }

    static native void nglUniform2fARB(int var0, float var1, float var2, long var3);

    public static void glUniform3fARB(int n, float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform3fARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglUniform3fARB(n, f, f2, f3, l);
    }

    static native void nglUniform3fARB(int var0, float var1, float var2, float var3, long var4);

    public static void glUniform4fARB(int n, float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform4fARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglUniform4fARB(n, f, f2, f3, f4, l);
    }

    static native void nglUniform4fARB(int var0, float var1, float var2, float var3, float var4, long var5);

    public static void glUniform1iARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform1iARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglUniform1iARB(n, n2, l);
    }

    static native void nglUniform1iARB(int var0, int var1, long var2);

    public static void glUniform2iARB(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform2iARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglUniform2iARB(n, n2, n3, l);
    }

    static native void nglUniform2iARB(int var0, int var1, int var2, long var3);

    public static void glUniform3iARB(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform3iARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglUniform3iARB(n, n2, n3, n4, l);
    }

    static native void nglUniform3iARB(int var0, int var1, int var2, int var3, long var4);

    public static void glUniform4iARB(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform4iARB;
        BufferChecks.checkFunctionAddress(l);
        ARBShaderObjects.nglUniform4iARB(n, n2, n3, n4, n5, l);
    }

    static native void nglUniform4iARB(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glUniform1ARB(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform1fvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ARBShaderObjects.nglUniform1fvARB(n, floatBuffer.remaining(), MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglUniform1fvARB(int var0, int var1, long var2, long var4);

    public static void glUniform2ARB(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform2fvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ARBShaderObjects.nglUniform2fvARB(n, floatBuffer.remaining() >> 1, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglUniform2fvARB(int var0, int var1, long var2, long var4);

    public static void glUniform3ARB(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform3fvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ARBShaderObjects.nglUniform3fvARB(n, floatBuffer.remaining() / 3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglUniform3fvARB(int var0, int var1, long var2, long var4);

    public static void glUniform4ARB(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform4fvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ARBShaderObjects.nglUniform4fvARB(n, floatBuffer.remaining() >> 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglUniform4fvARB(int var0, int var1, long var2, long var4);

    public static void glUniform1ARB(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform1ivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBShaderObjects.nglUniform1ivARB(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglUniform1ivARB(int var0, int var1, long var2, long var4);

    public static void glUniform2ARB(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform2ivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBShaderObjects.nglUniform2ivARB(n, intBuffer.remaining() >> 1, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglUniform2ivARB(int var0, int var1, long var2, long var4);

    public static void glUniform3ARB(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform3ivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBShaderObjects.nglUniform3ivARB(n, intBuffer.remaining() / 3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglUniform3ivARB(int var0, int var1, long var2, long var4);

    public static void glUniform4ARB(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform4ivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBShaderObjects.nglUniform4ivARB(n, intBuffer.remaining() >> 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglUniform4ivARB(int var0, int var1, long var2, long var4);

    public static void glUniformMatrix2ARB(int n, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix2fvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ARBShaderObjects.nglUniformMatrix2fvARB(n, floatBuffer.remaining() >> 2, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglUniformMatrix2fvARB(int var0, int var1, boolean var2, long var3, long var5);

    public static void glUniformMatrix3ARB(int n, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix3fvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ARBShaderObjects.nglUniformMatrix3fvARB(n, floatBuffer.remaining() / 9, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglUniformMatrix3fvARB(int var0, int var1, boolean var2, long var3, long var5);

    public static void glUniformMatrix4ARB(int n, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix4fvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ARBShaderObjects.nglUniformMatrix4fvARB(n, floatBuffer.remaining() >> 4, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglUniformMatrix4fvARB(int var0, int var1, boolean var2, long var3, long var5);

    public static void glGetObjectParameterARB(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetObjectParameterfvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ARBShaderObjects.nglGetObjectParameterfvARB(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetObjectParameterfvARB(int var0, int var1, long var2, long var4);

    public static float glGetObjectParameterfARB(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetObjectParameterfvARB;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        ARBShaderObjects.nglGetObjectParameterfvARB(n, n2, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetObjectParameterARB(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetObjectParameterivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBShaderObjects.nglGetObjectParameterivARB(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetObjectParameterivARB(int var0, int var1, long var2, long var4);

    public static int glGetObjectParameteriARB(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetObjectParameterivARB;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        ARBShaderObjects.nglGetObjectParameterivARB(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetInfoLogARB(int n, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetInfoLogARB;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(byteBuffer);
        ARBShaderObjects.nglGetInfoLogARB(n, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetInfoLogARB(int var0, int var1, long var2, long var4, long var6);

    public static String glGetInfoLogARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetInfoLogARB;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n2);
        ARBShaderObjects.nglGetInfoLogARB(n, n2, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static void glGetAttachedObjectsARB(int n, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetAttachedObjectsARB;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(intBuffer2);
        ARBShaderObjects.nglGetAttachedObjectsARB(n, intBuffer2.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    static native void nglGetAttachedObjectsARB(int var0, int var1, long var2, long var4, long var6);

    public static int glGetUniformLocationARB(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformLocationARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = ARBShaderObjects.nglGetUniformLocationARB(n, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglGetUniformLocationARB(int var0, long var1, long var3);

    public static int glGetUniformLocationARB(int n, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformLocationARB;
        BufferChecks.checkFunctionAddress(l);
        n = ARBShaderObjects.nglGetUniformLocationARB(n, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }

    public static void glGetActiveUniformARB(int n, int n2, IntBuffer intBuffer, IntBuffer intBuffer2, IntBuffer intBuffer3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniformARB;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkBuffer(intBuffer2, 1);
        BufferChecks.checkBuffer(intBuffer3, 1);
        BufferChecks.checkDirect(byteBuffer);
        ARBShaderObjects.nglGetActiveUniformARB(n, n2, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(intBuffer3), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetActiveUniformARB(int var0, int var1, int var2, long var3, long var5, long var7, long var9, long var11);

    public static String glGetActiveUniformARB(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniformARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 2);
        IntBuffer intBuffer2 = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        IntBuffer intBuffer3 = intBuffer;
        ARBShaderObjects.nglGetActiveUniformARB(n, n2, n3, MemoryUtil.getAddress0(intBuffer2), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer3, intBuffer3.position() + 1), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer2.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static String glGetActiveUniformARB(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniformARB;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        ARBShaderObjects.nglGetActiveUniformARB(n, n2, n3, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress0(APIUtil.getBufferInt(contextCapabilities)), MemoryUtil.getAddress(APIUtil.getBufferInt(contextCapabilities), 1), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static int glGetActiveUniformSizeARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniformARB;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getBufferInt(contextCapabilities);
        ARBShaderObjects.nglGetActiveUniformARB(n, n2, 0, 0L, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer, 1), APIUtil.getBufferByte0(contextCapabilities), l);
        return intBuffer.get(0);
    }

    public static int glGetActiveUniformTypeARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniformARB;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getBufferInt(contextCapabilities);
        ARBShaderObjects.nglGetActiveUniformARB(n, n2, 0, 0L, MemoryUtil.getAddress(intBuffer, 1), MemoryUtil.getAddress(intBuffer), APIUtil.getBufferByte0(contextCapabilities), l);
        return intBuffer.get(0);
    }

    public static void glGetUniformARB(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformfvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ARBShaderObjects.nglGetUniformfvARB(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetUniformfvARB(int var0, int var1, long var2, long var4);

    public static void glGetUniformARB(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBShaderObjects.nglGetUniformivARB(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetUniformivARB(int var0, int var1, long var2, long var4);

    public static void glGetShaderSourceARB(int n, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetShaderSourceARB;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(byteBuffer);
        ARBShaderObjects.nglGetShaderSourceARB(n, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetShaderSourceARB(int var0, int var1, long var2, long var4, long var6);

    public static String glGetShaderSourceARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetShaderSourceARB;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n2);
        ARBShaderObjects.nglGetShaderSourceARB(n, n2, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }
}
