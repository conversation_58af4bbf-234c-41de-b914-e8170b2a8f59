/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.Buffer;
import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.LongBuffer;
import org.lwjgl.BufferUtils;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;

final class APIUtil {
    private static final int INITIAL_BUFFER_SIZE = 256;
    private static final int INITIAL_LENGTHS_SIZE = 4;
    private static final int BUFFERS_SIZE = 32;
    private char[] array = new char[256];
    private ByteBuffer buffer = BufferUtils.createByteBuffer(256);
    private IntBuffer lengths = BufferUtils.createIntBuffer(4);
    private final IntBuffer ints = BufferUtils.createIntBuffer(32);
    private final LongBuffer longs = BufferUtils.createLongBuffer(32);
    private final FloatBuffer floats = BufferUtils.createFloatBuffer(32);
    private final DoubleBuffer doubles = BufferUtils.createDoubleBuffer(32);

    APIUtil() {
    }

    private static char[] getArray(ContextCapabilities contextCapabilities, int n) {
        char[] cArray = contextCapabilities.util.array;
        if (contextCapabilities.util.array.length < n) {
            contextCapabilities.util.array = cArray = new char[n];
        }
        return cArray;
    }

    static ByteBuffer getBufferByte(ContextCapabilities contextCapabilities, int n) {
        ByteBuffer byteBuffer = contextCapabilities.util.buffer;
        if (byteBuffer.capacity() < n) {
            byteBuffer.capacity();
            contextCapabilities.util.buffer = byteBuffer = BufferUtils.createByteBuffer(n);
        } else {
            byteBuffer.clear();
        }
        return byteBuffer;
    }

    private static ByteBuffer getBufferByteOffset(ContextCapabilities contextCapabilities, int n) {
        ByteBuffer byteBuffer = contextCapabilities.util.buffer;
        if (byteBuffer.capacity() < n) {
            byteBuffer.capacity();
            ByteBuffer byteBuffer2 = BufferUtils.createByteBuffer(n);
            byteBuffer2.put(byteBuffer);
            contextCapabilities.util.buffer = byteBuffer = byteBuffer2;
        } else {
            ByteBuffer byteBuffer3 = byteBuffer;
            byteBuffer3.position(byteBuffer3.limit());
            ByteBuffer byteBuffer4 = byteBuffer;
            byteBuffer4.limit(byteBuffer4.capacity());
        }
        return byteBuffer;
    }

    static IntBuffer getBufferInt(ContextCapabilities contextCapabilities) {
        return contextCapabilities.util.ints;
    }

    static LongBuffer getBufferLong(ContextCapabilities contextCapabilities) {
        return contextCapabilities.util.longs;
    }

    static FloatBuffer getBufferFloat(ContextCapabilities contextCapabilities) {
        return contextCapabilities.util.floats;
    }

    static DoubleBuffer getBufferDouble(ContextCapabilities contextCapabilities) {
        return contextCapabilities.util.doubles;
    }

    static IntBuffer getLengths(ContextCapabilities contextCapabilities) {
        return APIUtil.getLengths(contextCapabilities, 1);
    }

    static IntBuffer getLengths(ContextCapabilities contextCapabilities, int n) {
        IntBuffer intBuffer = contextCapabilities.util.lengths;
        if (intBuffer.capacity() < n) {
            intBuffer.capacity();
            contextCapabilities.util.lengths = intBuffer = BufferUtils.createIntBuffer(n);
        } else {
            intBuffer.clear();
        }
        return intBuffer;
    }

    private static ByteBuffer encode(ByteBuffer byteBuffer, CharSequence charSequence) {
        for (int i = 0; i < charSequence.length(); ++i) {
            char c = charSequence.charAt(i);
            if (LWJGLUtil.DEBUG && '\u0080' <= c) {
                byteBuffer.put((byte)26);
                continue;
            }
            byteBuffer.put((byte)c);
        }
        return byteBuffer;
    }

    static String getString(ContextCapabilities object, ByteBuffer byteBuffer) {
        int n = byteBuffer.remaining();
        object = APIUtil.getArray((ContextCapabilities)object, n);
        for (int i = byteBuffer.position(); i < byteBuffer.limit(); ++i) {
            object[i - byteBuffer.position()] = (char)byteBuffer.get(i);
        }
        return new String((char[])object, 0, n);
    }

    static long getBuffer(ContextCapabilities object, CharSequence charSequence) {
        object = APIUtil.encode(APIUtil.getBufferByte((ContextCapabilities)object, charSequence.length()), charSequence);
        ((ByteBuffer)object).flip();
        return MemoryUtil.getAddress0((Buffer)object);
    }

    static long getBuffer(ContextCapabilities object, CharSequence charSequence, int n) {
        object = APIUtil.encode(APIUtil.getBufferByteOffset((ContextCapabilities)object, n + charSequence.length()), charSequence);
        ((ByteBuffer)object).flip();
        return MemoryUtil.getAddress((ByteBuffer)object);
    }

    static long getBufferNT(ContextCapabilities object, CharSequence charSequence) {
        object = APIUtil.encode(APIUtil.getBufferByte((ContextCapabilities)object, charSequence.length() + 1), charSequence);
        ((ByteBuffer)object).put((byte)0);
        ((ByteBuffer)object).flip();
        return MemoryUtil.getAddress0((Buffer)object);
    }

    static int getTotalLength(CharSequence[] charSequenceArray) {
        int n = 0;
        for (CharSequence charSequence : charSequenceArray) {
            n += charSequence.length();
        }
        return n;
    }

    static long getBuffer(ContextCapabilities object, CharSequence[] charSequenceArray) {
        object = APIUtil.getBufferByte((ContextCapabilities)object, APIUtil.getTotalLength(charSequenceArray));
        for (CharSequence charSequence : charSequenceArray) {
            APIUtil.encode((ByteBuffer)object, charSequence);
        }
        ((ByteBuffer)object).flip();
        return MemoryUtil.getAddress0((Buffer)object);
    }

    static long getBufferNT(ContextCapabilities object, CharSequence[] charSequenceArray) {
        object = APIUtil.getBufferByte((ContextCapabilities)object, APIUtil.getTotalLength(charSequenceArray) + charSequenceArray.length);
        for (CharSequence charSequence : charSequenceArray) {
            APIUtil.encode((ByteBuffer)object, charSequence);
            ((ByteBuffer)object).put((byte)0);
        }
        ((ByteBuffer)object).flip();
        return MemoryUtil.getAddress0((Buffer)object);
    }

    static long getLengths(ContextCapabilities object, CharSequence[] charSequenceArray) {
        object = APIUtil.getLengths((ContextCapabilities)object, charSequenceArray.length);
        for (CharSequence charSequence : charSequenceArray) {
            ((IntBuffer)object).put(charSequence.length());
        }
        ((IntBuffer)object).flip();
        return MemoryUtil.getAddress0((Buffer)object);
    }

    static long getInt(ContextCapabilities contextCapabilities, int n) {
        return MemoryUtil.getAddress0(APIUtil.getBufferInt(contextCapabilities).put(0, n));
    }

    static long getBufferByte0(ContextCapabilities contextCapabilities) {
        return MemoryUtil.getAddress0(APIUtil.getBufferByte(contextCapabilities, 0));
    }
}
