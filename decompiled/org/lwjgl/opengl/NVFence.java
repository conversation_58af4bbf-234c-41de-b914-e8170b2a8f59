/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVFence {
    public static final int GL_ALL_COMPLETED_NV = 34034;
    public static final int GL_FENCE_STATUS_NV = 34035;
    public static final int GL_FENCE_CONDITION_NV = 34036;

    private NVFence() {
    }

    public static void glGenFencesNV(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenFencesNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        NVFence.nglGenFencesNV(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenFencesNV(int var0, long var1, long var3);

    public static int glGenFencesNV() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenFencesNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        NVFence.nglGenFencesNV(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glDeleteFencesNV(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteFencesNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        NVFence.nglDeleteFencesNV(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeleteFencesNV(int var0, long var1, long var3);

    public static void glDeleteFencesNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteFencesNV;
        BufferChecks.checkFunctionAddress(l);
        NVFence.nglDeleteFencesNV(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static void glSetFenceNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSetFenceNV;
        BufferChecks.checkFunctionAddress(l);
        NVFence.nglSetFenceNV(n, n2, l);
    }

    static native void nglSetFenceNV(int var0, int var1, long var2);

    public static boolean glTestFenceNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTestFenceNV;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = NVFence.nglTestFenceNV(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglTestFenceNV(int var0, long var1);

    public static void glFinishFenceNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFinishFenceNV;
        BufferChecks.checkFunctionAddress(l);
        NVFence.nglFinishFenceNV(n, l);
    }

    static native void nglFinishFenceNV(int var0, long var1);

    public static boolean glIsFenceNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsFenceNV;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = NVFence.nglIsFenceNV(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsFenceNV(int var0, long var1);

    public static void glGetFenceivNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetFenceivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVFence.nglGetFenceivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetFenceivNV(int var0, int var1, long var2, long var4);
}
