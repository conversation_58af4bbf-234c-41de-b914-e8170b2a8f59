/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.security.AccessController;
import java.security.PrivilegedAction;
import java.security.PrivilegedExceptionAction;
import java.util.Map;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.WeakHashMap;
import org.lwjgl.LWJGLException;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.Sys;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GL11;
import org.lwjgl.opengl.GL30;
import org.lwjgl.opengl.OpenGLException;
import org.lwjgl.opengl.Util;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public final class GLContext {
    private static final ThreadLocal<ContextCapabilities> current_capabilities = new ThreadLocal();
    private static CapabilitiesCacheEntry fast_path_cache = new CapabilitiesCacheEntry();
    private static final ThreadLocal<CapabilitiesCacheEntry> thread_cache_entries = new ThreadLocal();
    private static final Map<Object, ContextCapabilities> capability_cache = new WeakHashMap<Object, ContextCapabilities>();
    private static int gl_ref_count;
    private static boolean did_auto_load;

    public static ContextCapabilities getCapabilities() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilitiesImpl();
        if (contextCapabilities == null) {
            throw new RuntimeException("No OpenGL context found in the current thread.");
        }
        return contextCapabilities;
    }

    private static ContextCapabilities getCapabilitiesImpl() {
        CapabilitiesCacheEntry capabilitiesCacheEntry = fast_path_cache;
        if (capabilitiesCacheEntry.owner == Thread.currentThread()) {
            return capabilitiesCacheEntry.capabilities;
        }
        return GLContext.getThreadLocalCapabilities();
    }

    static ContextCapabilities getCapabilities(Object object) {
        return capability_cache.get(object);
    }

    private static ContextCapabilities getThreadLocalCapabilities() {
        return current_capabilities.get();
    }

    static void setCapabilities(ContextCapabilities contextCapabilities) {
        current_capabilities.set(contextCapabilities);
        CapabilitiesCacheEntry capabilitiesCacheEntry = thread_cache_entries.get();
        if (capabilitiesCacheEntry == null) {
            capabilitiesCacheEntry = new CapabilitiesCacheEntry();
            thread_cache_entries.set(capabilitiesCacheEntry);
        }
        capabilitiesCacheEntry.owner = Thread.currentThread();
        capabilitiesCacheEntry.capabilities = contextCapabilities;
        fast_path_cache = capabilitiesCacheEntry;
    }

    static long getPlatformSpecificFunctionAddress(String string, String[] stringArray, String[] stringArray2, String string2) {
        String string3 = AccessController.doPrivileged(new PrivilegedAction<String>(){

            @Override
            public final String run() {
                return System.getProperty("os.name");
            }
        });
        for (int i = 0; i < stringArray.length; ++i) {
            if (!string3.startsWith(stringArray[i])) continue;
            string = string2.replaceFirst(string, stringArray2[i]);
            long l = GLContext.getFunctionAddress(string);
            return l;
        }
        return 0L;
    }

    static long getFunctionAddress(String[] stringArray) {
        for (String string : stringArray) {
            long l = GLContext.getFunctionAddress(string);
            if (l == 0L) continue;
            return l;
        }
        return 0L;
    }

    static long getFunctionAddress(String object) {
        object = MemoryUtil.encodeASCII((CharSequence)object);
        return GLContext.ngetFunctionAddress(MemoryUtil.getAddress((ByteBuffer)object));
    }

    private static native long ngetFunctionAddress(long var0);

    static int getSupportedExtensions(Set<String> set) {
        int n;
        block11: {
            int n2;
            int n3;
            int n4;
            block10: {
                Object object;
                Object object2 = GL11.glGetString(7938);
                if (object2 == null) {
                    throw new IllegalStateException("glGetString(GL_VERSION) returned null - possibly caused by missing current context.");
                }
                object2 = new StringTokenizer((String)object2, ". ");
                String string = ((StringTokenizer)object2).nextToken();
                object2 = ((StringTokenizer)object2).nextToken();
                n4 = 0;
                n3 = 0;
                try {
                    n4 = Integer.parseInt(string);
                    n3 = Integer.parseInt((String)object2);
                }
                catch (NumberFormatException numberFormatException) {
                    LWJGLUtil.log("The major and/or minor OpenGL version is malformed: " + numberFormatException.getMessage());
                }
                object2 = new int[][]{{1, 2, 3, 4, 5}, {0, 1}, {0, 1, 2, 3}, {0, 1, 2, 3, 4, 5}};
                for (n = 1; n <= 4; ++n) {
                    Object object3;
                    object = object3 = object2[n - 1];
                    n2 = ((Object)object3).length;
                    for (int i = 0; i < n2; ++i) {
                        Object object4 = object[i];
                        if (n >= n4 && (n != n4 || object4 > n3)) continue;
                        set.add("OpenGL" + Integer.toString(n) + Integer.toString((int)object4));
                    }
                }
                n = 0;
                if (n4 >= 3) break block10;
                String string2 = GL11.glGetString(7939);
                if (string2 == null) {
                    throw new IllegalStateException("glGetString(GL_EXTENSIONS) returned null - is there a context current?");
                }
                object = new StringTokenizer(string2);
                while (((StringTokenizer)object).hasMoreTokens()) {
                    set.add(((StringTokenizer)object).nextToken());
                }
                break block11;
            }
            n2 = GL11.glGetInteger(33309);
            for (int i = 0; i < n2; ++i) {
                set.add(GL30.glGetStringi(7939, i));
            }
            if (3 >= n4 && 2 > n3) break block11;
            Util.checkGLError();
            try {
                n = GL11.glGetInteger(37158);
                Util.checkGLError();
            }
            catch (OpenGLException openGLException) {
                LWJGLUtil.log("Failed to retrieve CONTEXT_PROFILE_MASK");
            }
        }
        return n;
    }

    static void initNativeStubs(final Class<?> clazz, Set set, String string) {
        GLContext.resetNativeStubs(clazz);
        if (set.contains(string)) {
            try {
                AccessController.doPrivileged(new PrivilegedExceptionAction<Object>(){

                    @Override
                    public final Object run() {
                        Method method = clazz.getDeclaredMethod("initNativeStubs", new Class[0]);
                        method.invoke(null, new Object[0]);
                        return null;
                    }
                });
                return;
            }
            catch (Exception exception) {
                LWJGLUtil.log("Failed to initialize extension " + clazz + " - exception: " + exception);
                set.remove(string);
            }
        }
    }

    public static synchronized void useContext(Object object) {
        GLContext.useContext(object, false);
    }

    public static synchronized void useContext(Object object, boolean bl) {
        if (object == null) {
            ContextCapabilities.unloadAllStubs();
            GLContext.setCapabilities(null);
            if (did_auto_load) {
                GLContext.unloadOpenGLLibrary();
            }
            return;
        }
        if (gl_ref_count == 0) {
            GLContext.loadOpenGLLibrary();
            did_auto_load = true;
        }
        try {
            ContextCapabilities contextCapabilities = capability_cache.get(object);
            if (contextCapabilities != null) {
                GLContext.setCapabilities(contextCapabilities);
                return;
            }
            new ContextCapabilities(bl);
            capability_cache.put(object, GLContext.getCapabilities());
        }
        catch (LWJGLException lWJGLException) {
            if (did_auto_load) {
                GLContext.unloadOpenGLLibrary();
            }
            throw lWJGLException;
        }
    }

    public static synchronized void loadOpenGLLibrary() {
        if (gl_ref_count == 0) {
            GLContext.nLoadOpenGLLibrary();
        }
        ++gl_ref_count;
    }

    private static native void nLoadOpenGLLibrary();

    public static synchronized void unloadOpenGLLibrary() {
        if (--gl_ref_count == 0 && LWJGLUtil.getPlatform() != 1) {
            GLContext.nUnloadOpenGLLibrary();
        }
    }

    private static native void nUnloadOpenGLLibrary();

    static native void resetNativeStubs(Class var0);

    static {
        Sys.initialize();
    }

    private static final class CapabilitiesCacheEntry {
        Thread owner;
        ContextCapabilities capabilities;

        private CapabilitiesCacheEntry() {
        }
    }
}
