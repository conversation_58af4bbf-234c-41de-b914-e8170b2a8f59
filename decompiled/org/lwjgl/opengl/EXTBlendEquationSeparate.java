/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class EXTBlendEquationSeparate {
    public static final int GL_BLEND_EQUATION_RGB_EXT = 32777;
    public static final int GL_BLEND_EQUATION_ALPHA_EXT = 34877;

    private EXTBlendEquationSeparate() {
    }

    public static void glBlendEquationSeparateEXT(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendEquationSeparateEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTBlendEquationSeparate.nglBlendEquationSeparateEXT(n, n2, l);
    }

    static native void nglBlendEquationSeparateEXT(int var0, int var1, long var2);
}
