/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVVertexArrayRange {
    public static final int GL_VERTEX_ARRAY_RANGE_NV = 34077;
    public static final int GL_VERTEX_ARRAY_RANGE_LENGTH_NV = 34078;
    public static final int GL_VERTEX_ARRAY_RANGE_VALID_NV = 34079;
    public static final int GL_MAX_VERTEX_ARRAY_RANGE_ELEMENT_NV = 34080;
    public static final int GL_VERTEX_ARRAY_RANGE_POINTER_NV = 34081;

    private NVVertexArrayRange() {
    }

    public static void glVertexArrayRangeNV(ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexArrayRangeNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        NVVertexArrayRange.nglVertexArrayRangeNV(byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glVertexArrayRangeNV(DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexArrayRangeNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        NVVertexArrayRange.nglVertexArrayRangeNV(doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glVertexArrayRangeNV(FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexArrayRangeNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        NVVertexArrayRange.nglVertexArrayRangeNV(floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glVertexArrayRangeNV(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexArrayRangeNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        NVVertexArrayRange.nglVertexArrayRangeNV(intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glVertexArrayRangeNV(ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexArrayRangeNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        NVVertexArrayRange.nglVertexArrayRangeNV(shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexArrayRangeNV(int var0, long var1, long var3);

    public static void glFlushVertexArrayRangeNV() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFlushVertexArrayRangeNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexArrayRange.nglFlushVertexArrayRangeNV(l);
    }

    static native void nglFlushVertexArrayRangeNV(long var0);

    public static ByteBuffer glAllocateMemoryNV(int n, float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glAllocateMemoryNV;
        BufferChecks.checkFunctionAddress(l);
        ByteBuffer byteBuffer = NVVertexArrayRange.nglAllocateMemoryNV(n, f, f2, f3, n, l);
        if (LWJGLUtil.CHECKS && byteBuffer == null) {
            return null;
        }
        return byteBuffer.order(ByteOrder.nativeOrder());
    }

    static native ByteBuffer nglAllocateMemoryNV(int var0, float var1, float var2, float var3, long var4, long var6);

    public static void glFreeMemoryNV(ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFreeMemoryNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        NVVertexArrayRange.nglFreeMemoryNV(MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglFreeMemoryNV(long var0, long var2);
}
