/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.Buffer;
import java.util.Arrays;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GL11;

class BaseReferences {
    int elementArrayBuffer;
    int arrayBuffer;
    final Buffer[] glVertexAttribPointer_buffer;
    final Buffer[] glTexCoordPointer_buffer;
    int glClientActiveTexture;
    int vertexArrayObject;
    int pixelPackBuffer;
    int pixelUnpackBuffer;
    int indirectBuffer;

    BaseReferences(ContextCapabilities contextCapabilities) {
        int n = contextCapabilities.OpenGL20 || contextCapabilities.GL_ARB_vertex_shader ? GL11.glGetInteger(34921) : 0;
        this.glVertexAttribPointer_buffer = new Buffer[n];
        int n2 = contextCapabilities.OpenGL20 ? GL11.glGetInteger(34930) : (contextCapabilities.OpenGL13 || contextCapabilities.GL_ARB_multitexture ? GL11.glGetInteger(34018) : 1);
        this.glTexCoordPointer_buffer = new Buffer[n2];
    }

    void clear() {
        this.elementArrayBuffer = 0;
        this.arrayBuffer = 0;
        this.glClientActiveTexture = 0;
        Arrays.fill(this.glVertexAttribPointer_buffer, null);
        Arrays.fill(this.glTexCoordPointer_buffer, null);
        this.vertexArrayObject = 0;
        this.pixelPackBuffer = 0;
        this.pixelUnpackBuffer = 0;
        this.indirectBuffer = 0;
    }

    void copy(BaseReferences baseReferences, int n) {
        if ((n & 2) != 0) {
            this.elementArrayBuffer = baseReferences.elementArrayBuffer;
            this.arrayBuffer = baseReferences.arrayBuffer;
            this.glClientActiveTexture = baseReferences.glClientActiveTexture;
            System.arraycopy(baseReferences.glVertexAttribPointer_buffer, 0, this.glVertexAttribPointer_buffer, 0, this.glVertexAttribPointer_buffer.length);
            System.arraycopy(baseReferences.glTexCoordPointer_buffer, 0, this.glTexCoordPointer_buffer, 0, this.glTexCoordPointer_buffer.length);
            this.vertexArrayObject = baseReferences.vertexArrayObject;
            this.indirectBuffer = baseReferences.indirectBuffer;
        }
        if ((n & 1) != 0) {
            this.pixelPackBuffer = baseReferences.pixelPackBuffer;
            this.pixelUnpackBuffer = baseReferences.pixelUnpackBuffer;
        }
    }
}
