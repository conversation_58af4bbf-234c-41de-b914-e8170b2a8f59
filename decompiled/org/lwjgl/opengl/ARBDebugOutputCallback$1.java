/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.opengl.ARBDebugOutputCallback;

class ARBDebugOutputCallback.1
implements ARBDebugOutputCallback.Handler {
    ARBDebugOutputCallback.1() {
    }

    public void handleMessage(int n, int n2, int n3, int n4, String string) {
        String string2;
        System.err.println("[LWJGL] ARB_debug_output message");
        System.err.println("\tID: " + n3);
        switch (n) {
            case 33350: {
                string2 = "API";
                break;
            }
            case 33351: {
                string2 = "WINDOW SYSTEM";
                break;
            }
            case 33352: {
                string2 = "SHADER COMPILER";
                break;
            }
            case 33353: {
                string2 = "THIRD PARTY";
                break;
            }
            case 33354: {
                string2 = "APPLICATION";
                break;
            }
            case 33355: {
                string2 = "OTHER";
                break;
            }
            default: {
                string2 = this.printUnknownToken(n);
            }
        }
        System.err.println("\tSource: " + string2);
        switch (n2) {
            case 33356: {
                string2 = "ERROR";
                break;
            }
            case 33357: {
                string2 = "DEPRECATED BEHAVIOR";
                break;
            }
            case 33358: {
                string2 = "UNDEFINED BEHAVIOR";
                break;
            }
            case 33359: {
                string2 = "PORTABILITY";
                break;
            }
            case 33360: {
                string2 = "PERFORMANCE";
                break;
            }
            case 33361: {
                string2 = "OTHER";
                break;
            }
            default: {
                string2 = this.printUnknownToken(n2);
            }
        }
        System.err.println("\tType: " + string2);
        switch (n4) {
            case 37190: {
                string2 = "HIGH";
                break;
            }
            case 37191: {
                string2 = "MEDIUM";
                break;
            }
            case 37192: {
                string2 = "LOW";
                break;
            }
            default: {
                string2 = this.printUnknownToken(n4);
            }
        }
        System.err.println("\tSeverity: " + string2);
        System.err.println("\tMessage: " + string);
    }

    private String printUnknownToken(int n) {
        return "Unknown (0x" + Integer.toHexString(n).toUpperCase() + ")";
    }
}
