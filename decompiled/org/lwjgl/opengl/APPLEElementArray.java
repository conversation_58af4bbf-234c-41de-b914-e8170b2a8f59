/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class APPLEElementArray {
    public static final int GL_ELEMENT_ARRAY_APPLE = 34664;
    public static final int GL_ELEMENT_ARRAY_TYPE_APPLE = 34665;
    public static final int GL_ELEMENT_ARRAY_POINTER_APPLE = 34666;

    private APPLEElementArray() {
    }

    public static void glElementPointerAPPLE(ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glElementPointerAPPLE;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        APPLEElementArray.nglElementPointerAPPLE(5121, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glElementPointerAPPLE(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glElementPointerAPPLE;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        APPLEElementArray.nglElementPointerAPPLE(5125, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glElementPointerAPPLE(ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glElementPointerAPPLE;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        APPLEElementArray.nglElementPointerAPPLE(5123, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglElementPointerAPPLE(int var0, long var1, long var3);

    public static void glDrawElementArrayAPPLE(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementArrayAPPLE;
        BufferChecks.checkFunctionAddress(l);
        APPLEElementArray.nglDrawElementArrayAPPLE(n, n2, n3, l);
    }

    static native void nglDrawElementArrayAPPLE(int var0, int var1, int var2, long var3);

    public static void glDrawRangeElementArrayAPPLE(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawRangeElementArrayAPPLE;
        BufferChecks.checkFunctionAddress(l);
        APPLEElementArray.nglDrawRangeElementArrayAPPLE(n, n2, n3, n4, n5, l);
    }

    static native void nglDrawRangeElementArrayAPPLE(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glMultiDrawElementArrayAPPLE(int n, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiDrawElementArrayAPPLE;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkBuffer(intBuffer2, intBuffer.remaining());
        APPLEElementArray.nglMultiDrawElementArrayAPPLE(n, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), intBuffer.remaining(), l);
    }

    static native void nglMultiDrawElementArrayAPPLE(int var0, long var1, long var3, int var5, long var6);

    public static void glMultiDrawRangeElementArrayAPPLE(int n, int n2, int n3, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiDrawRangeElementArrayAPPLE;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkBuffer(intBuffer2, intBuffer.remaining());
        APPLEElementArray.nglMultiDrawRangeElementArrayAPPLE(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), intBuffer.remaining(), l);
    }

    static native void nglMultiDrawRangeElementArrayAPPLE(int var0, int var1, int var2, long var3, long var5, int var7, long var8);
}
