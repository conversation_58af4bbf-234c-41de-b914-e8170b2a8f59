/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import org.lwjgl.opengl.EventQueue;
import org.lwjgl.opengl.WindowsKeycodes;

final class WindowsKeyboard {
    private final byte[] key_down_buffer = new byte[256];
    private final byte[] virt_key_down_buffer = new byte[256];
    private final EventQueue event_queue = new EventQueue(18);
    private final ByteBuffer tmp_event = ByteBuffer.allocate(18);
    private boolean has_retained_event;
    private int retained_key_code;
    private byte retained_state;
    private int retained_char;
    private long retained_millis;
    private boolean retained_repeat;

    WindowsKeyboard() {
    }

    private static native boolean isWindowsNT();

    final boolean isKeyDown(int n) {
        return this.key_down_buffer[n] == 1;
    }

    final void poll(ByteBuffer byteBuffer) {
        if (this.isKeyDown(42) && !WindowsKeyboard.isKeyPressedAsync(160)) {
            this.handleKey(16, 42, false, (byte)0, 0L, false);
        }
        if (this.isKeyDown(54) && !WindowsKeyboard.isKeyPressedAsync(161)) {
            this.handleKey(16, 54, false, (byte)0, 0L, false);
        }
        int n = byteBuffer.position();
        byteBuffer.put(this.key_down_buffer);
        byteBuffer.position(n);
    }

    private static native int MapVirtualKey(int var0, int var1);

    private static native int ToUnicode(int var0, int var1, ByteBuffer var2, CharBuffer var3, int var4, int var5);

    private static native int ToAscii(int var0, int var1, ByteBuffer var2, ByteBuffer var3, int var4);

    private static native int GetKeyboardState(ByteBuffer var0);

    private static native short GetKeyState(int var0);

    private static native short GetAsyncKeyState(int var0);

    private void putEvent(int n, byte by, int n2, long l, boolean bl) {
        this.tmp_event.clear();
        this.tmp_event.putInt(n).put(by).putInt(n2).putLong(l * 1000000L).put(bl ? (byte)1 : 0);
        this.tmp_event.flip();
        this.event_queue.putEvent(this.tmp_event);
    }

    private static int translateExtended(int n, int n2, boolean bl) {
        switch (n) {
            case 16: {
                if (n2 == 54) {
                    return 161;
                }
                return 160;
            }
            case 17: {
                if (bl) {
                    return 163;
                }
                return 162;
            }
            case 18: {
                if (bl) {
                    return 165;
                }
                return 164;
            }
        }
        return n;
    }

    private void flushRetained() {
        if (this.has_retained_event) {
            this.has_retained_event = false;
            WindowsKeyboard windowsKeyboard = this;
            windowsKeyboard.putEvent(windowsKeyboard.retained_key_code, this.retained_state, this.retained_char, this.retained_millis, this.retained_repeat);
        }
    }

    private static boolean isKeyPressed(int n) {
        return (n & 1) == 1;
    }

    private static boolean isKeyPressedAsync(int n) {
        return (WindowsKeyboard.GetAsyncKeyState(n) & 0x8000) != 0;
    }

    final void releaseAll(long l) {
        for (int i = 0; i < this.virt_key_down_buffer.length; ++i) {
            if (!WindowsKeyboard.isKeyPressed(this.virt_key_down_buffer[i])) continue;
            this.handleKey(i, 0, false, (byte)0, l, false);
        }
    }

    final void handleKey(int n, int n2, boolean bl, byte by, long l, boolean bl2) {
        n = WindowsKeyboard.translateExtended(n, n2, bl);
        if (!bl2 && WindowsKeyboard.isKeyPressed(by) == WindowsKeyboard.isKeyPressed(this.virt_key_down_buffer[n])) {
            return;
        }
        this.flushRetained();
        this.has_retained_event = true;
        n2 = WindowsKeycodes.mapVirtualKeyToLWJGLCode(n);
        if (n2 < this.key_down_buffer.length) {
            this.key_down_buffer[n2] = by;
            bl2 &= WindowsKeyboard.isKeyPressed(this.virt_key_down_buffer[n]);
            this.virt_key_down_buffer[n] = by;
        }
        this.retained_key_code = n2;
        this.retained_state = by;
        this.retained_millis = l;
        this.retained_char = 0;
        this.retained_repeat = bl2;
    }

    final void handleChar(int n, long l, boolean bl) {
        if (this.has_retained_event && this.retained_char != 0) {
            this.flushRetained();
        }
        if (!this.has_retained_event) {
            this.putEvent(0, (byte)0, n, l, bl);
            return;
        }
        this.retained_char = n;
    }

    final void read(ByteBuffer byteBuffer) {
        this.flushRetained();
        this.event_queue.copyEvents(byteBuffer);
    }
}
