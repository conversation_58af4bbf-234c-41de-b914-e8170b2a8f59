/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class EXTTransformFeedback {
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_EXT = 35982;
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_START_EXT = 35972;
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_SIZE_EXT = 35973;
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_BINDING_EXT = 35983;
    public static final int GL_INTERLEAVED_ATTRIBS_EXT = 35980;
    public static final int GL_SEPARATE_ATTRIBS_EXT = 35981;
    public static final int GL_PRIMITIVES_GENERATED_EXT = 35975;
    public static final int GL_TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN_EXT = 35976;
    public static final int GL_RASTERIZER_DISCARD_EXT = 35977;
    public static final int GL_MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS_EXT = 35978;
    public static final int GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS_EXT = 35979;
    public static final int GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS_EXT = 35968;
    public static final int GL_TRANSFORM_FEEDBACK_VARYINGS_EXT = 35971;
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_MODE_EXT = 35967;
    public static final int GL_TRANSFORM_FEEDBACK_VARYING_MAX_LENGTH_EXT = 35958;

    private EXTTransformFeedback() {
    }

    public static void glBindBufferRangeEXT(int n, int n2, int n3, long l, long l2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glBindBufferRangeEXT;
        BufferChecks.checkFunctionAddress(l3);
        EXTTransformFeedback.nglBindBufferRangeEXT(n, n2, n3, l, l2, l3);
    }

    static native void nglBindBufferRangeEXT(int var0, int var1, int var2, long var3, long var5, long var7);

    public static void glBindBufferOffsetEXT(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glBindBufferOffsetEXT;
        BufferChecks.checkFunctionAddress(l2);
        EXTTransformFeedback.nglBindBufferOffsetEXT(n, n2, n3, l, l2);
    }

    static native void nglBindBufferOffsetEXT(int var0, int var1, int var2, long var3, long var5);

    public static void glBindBufferBaseEXT(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindBufferBaseEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTTransformFeedback.nglBindBufferBaseEXT(n, n2, n3, l);
    }

    static native void nglBindBufferBaseEXT(int var0, int var1, int var2, long var3);

    public static void glBeginTransformFeedbackEXT(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBeginTransformFeedbackEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTTransformFeedback.nglBeginTransformFeedbackEXT(n, l);
    }

    static native void nglBeginTransformFeedbackEXT(int var0, long var1);

    public static void glEndTransformFeedbackEXT() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEndTransformFeedbackEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTTransformFeedback.nglEndTransformFeedbackEXT(l);
    }

    static native void nglEndTransformFeedbackEXT(long var0);

    public static void glTransformFeedbackVaryingsEXT(int n, int n2, ByteBuffer byteBuffer, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTransformFeedbackVaryingsEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer, n2);
        EXTTransformFeedback.nglTransformFeedbackVaryingsEXT(n, n2, MemoryUtil.getAddress(byteBuffer), n3, l);
    }

    static native void nglTransformFeedbackVaryingsEXT(int var0, int var1, long var2, int var4, long var5);

    public static void glTransformFeedbackVaryingsEXT(int n, CharSequence[] charSequenceArray, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTransformFeedbackVaryingsEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkArray(charSequenceArray);
        EXTTransformFeedback.nglTransformFeedbackVaryingsEXT(n, charSequenceArray.length, APIUtil.getBufferNT(contextCapabilities, charSequenceArray), n2, l);
    }

    public static void glGetTransformFeedbackVaryingEXT(int n, int n2, IntBuffer intBuffer, IntBuffer intBuffer2, IntBuffer intBuffer3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTransformFeedbackVaryingEXT;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkBuffer(intBuffer2, 1);
        BufferChecks.checkBuffer(intBuffer3, 1);
        BufferChecks.checkDirect(byteBuffer);
        EXTTransformFeedback.nglGetTransformFeedbackVaryingEXT(n, n2, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(intBuffer3), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetTransformFeedbackVaryingEXT(int var0, int var1, int var2, long var3, long var5, long var7, long var9, long var11);

    public static String glGetTransformFeedbackVaryingEXT(int n, int n2, int n3, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTransformFeedbackVaryingEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        BufferChecks.checkBuffer(intBuffer2, 1);
        IntBuffer intBuffer3 = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        EXTTransformFeedback.nglGetTransformFeedbackVaryingEXT(n, n2, n3, MemoryUtil.getAddress0(intBuffer3), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer3.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }
}
