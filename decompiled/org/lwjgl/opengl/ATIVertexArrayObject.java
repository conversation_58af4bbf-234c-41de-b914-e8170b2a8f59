/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ATIVertexArrayObject {
    public static final int GL_STATIC_ATI = 34656;
    public static final int GL_DYNAMIC_ATI = 34657;
    public static final int GL_PRESERVE_ATI = 34658;
    public static final int GL_DISCARD_ATI = 34659;
    public static final int GL_OBJECT_BUFFER_SIZE_ATI = 34660;
    public static final int GL_OBJECT_BUFFER_USAGE_ATI = 34661;
    public static final int GL_ARRAY_OBJECT_BUFFER_ATI = 34662;
    public static final int GL_ARRAY_OBJECT_OFFSET_ATI = 34663;

    private ATIVertexArrayObject() {
    }

    public static int glNewObjectBufferATI(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNewObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        n = ATIVertexArrayObject.nglNewObjectBufferATI(n, 0L, n2, l);
        return n;
    }

    public static int glNewObjectBufferATI(ByteBuffer byteBuffer, int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNewObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        int n2 = ATIVertexArrayObject.nglNewObjectBufferATI(byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), n, l);
        return n2;
    }

    public static int glNewObjectBufferATI(DoubleBuffer doubleBuffer, int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNewObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        int n2 = ATIVertexArrayObject.nglNewObjectBufferATI(doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), n, l);
        return n2;
    }

    public static int glNewObjectBufferATI(FloatBuffer floatBuffer, int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNewObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        int n2 = ATIVertexArrayObject.nglNewObjectBufferATI(floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), n, l);
        return n2;
    }

    public static int glNewObjectBufferATI(IntBuffer intBuffer, int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNewObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        int n2 = ATIVertexArrayObject.nglNewObjectBufferATI(intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), n, l);
        return n2;
    }

    public static int glNewObjectBufferATI(ShortBuffer shortBuffer, int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNewObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        int n2 = ATIVertexArrayObject.nglNewObjectBufferATI(shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), n, l);
        return n2;
    }

    static native int nglNewObjectBufferATI(int var0, long var1, int var3, long var4);

    public static boolean glIsObjectBufferATI(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = ATIVertexArrayObject.nglIsObjectBufferATI(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsObjectBufferATI(int var0, long var1);

    public static void glUpdateObjectBufferATI(int n, int n2, ByteBuffer byteBuffer, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUpdateObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        ATIVertexArrayObject.nglUpdateObjectBufferATI(n, n2, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), n3, l);
    }

    public static void glUpdateObjectBufferATI(int n, int n2, DoubleBuffer doubleBuffer, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUpdateObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ATIVertexArrayObject.nglUpdateObjectBufferATI(n, n2, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), n3, l);
    }

    public static void glUpdateObjectBufferATI(int n, int n2, FloatBuffer floatBuffer, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUpdateObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ATIVertexArrayObject.nglUpdateObjectBufferATI(n, n2, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), n3, l);
    }

    public static void glUpdateObjectBufferATI(int n, int n2, IntBuffer intBuffer, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUpdateObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ATIVertexArrayObject.nglUpdateObjectBufferATI(n, n2, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), n3, l);
    }

    public static void glUpdateObjectBufferATI(int n, int n2, ShortBuffer shortBuffer, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUpdateObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        ATIVertexArrayObject.nglUpdateObjectBufferATI(n, n2, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), n3, l);
    }

    static native void nglUpdateObjectBufferATI(int var0, int var1, int var2, long var3, int var5, long var6);

    public static void glGetObjectBufferATI(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetObjectBufferfvATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ATIVertexArrayObject.nglGetObjectBufferfvATI(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetObjectBufferfvATI(int var0, int var1, long var2, long var4);

    public static void glGetObjectBufferATI(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetObjectBufferivATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ATIVertexArrayObject.nglGetObjectBufferivATI(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetObjectBufferivATI(int var0, int var1, long var2, long var4);

    public static int glGetObjectBufferiATI(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetObjectBufferivATI;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        ATIVertexArrayObject.nglGetObjectBufferivATI(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glFreeObjectBufferATI(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFreeObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexArrayObject.nglFreeObjectBufferATI(n, l);
    }

    static native void nglFreeObjectBufferATI(int var0, long var1);

    public static void glArrayObjectATI(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glArrayObjectATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexArrayObject.nglArrayObjectATI(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglArrayObjectATI(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glGetArrayObjectATI(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetArrayObjectfvATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ATIVertexArrayObject.nglGetArrayObjectfvATI(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetArrayObjectfvATI(int var0, int var1, long var2, long var4);

    public static void glGetArrayObjectATI(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetArrayObjectivATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        ATIVertexArrayObject.nglGetArrayObjectivATI(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetArrayObjectivATI(int var0, int var1, long var2, long var4);

    public static void glVariantArrayObjectATI(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVariantArrayObjectATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexArrayObject.nglVariantArrayObjectATI(n, n2, n3, n4, n5, l);
    }

    static native void nglVariantArrayObjectATI(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glGetVariantArrayObjectATI(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVariantArrayObjectfvATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ATIVertexArrayObject.nglGetVariantArrayObjectfvATI(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetVariantArrayObjectfvATI(int var0, int var1, long var2, long var4);

    public static void glGetVariantArrayObjectATI(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVariantArrayObjectivATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        ATIVertexArrayObject.nglGetVariantArrayObjectivATI(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetVariantArrayObjectivATI(int var0, int var1, long var2, long var4);
}
