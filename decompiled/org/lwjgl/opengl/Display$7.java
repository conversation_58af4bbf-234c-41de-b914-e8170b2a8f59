/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.security.PrivilegedAction;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
static final class Display.7
implements PrivilegedAction<Boolean> {
    final /* synthetic */ String val$property_name;

    Display.7(String string) {
        this.val$property_name = string;
    }

    @Override
    public final Boolean run() {
        return Boolean.getBoolean(this.val$property_name);
    }
}
