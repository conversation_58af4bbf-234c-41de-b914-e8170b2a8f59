/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.opengl.Context;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.Drawable;
import org.lwjgl.opengl.PixelFormatLWJGL;

interface DrawableLWJGL
extends Drawable {
    public void setPixelFormat(PixelFormatLWJGL var1);

    public void setPixelFormat(PixelFormatLWJGL var1, ContextAttribs var2);

    public PixelFormatLWJGL getPixelFormat();

    public Context getContext();

    public Context createSharedContext();

    public void checkGLError();

    public void setSwapInterval(int var1);

    public void swapBuffers();

    public void initContext(float var1, float var2, float var3);
}
