/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Canvas;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.Display;
import org.lwjgl.opengl.MacOSXCanvasPeerInfo;
import org.lwjgl.opengl.MacOSXDisplay;
import org.lwjgl.opengl.PixelFormat;

final class MacOSXDisplayPeerInfo
extends MacOSXCanvasPeerInfo {
    private boolean locked;

    MacOSXDisplayPeerInfo(PixelFormat pixelFormat, ContextAttribs contextAttribs, boolean bl) {
        super(pixelFormat, contextAttribs, bl);
    }

    protected final void doLockAndInitHandle() {
        if (this.locked) {
            throw new RuntimeException("Already locked");
        }
        Canvas canvas = ((MacOSXDisplay)Display.getImplementation()).getCanvas();
        if (canvas != null) {
            this.initHandle(canvas);
            this.locked = true;
        }
    }

    protected final void doUnlock() {
        if (this.locked) {
            super.doUnlock();
            this.locked = false;
        }
    }
}
