/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import org.lwjgl.opengl.GL41;

public final class ARBSeparateShaderObjects {
    public static final int GL_VERTEX_SHADER_BIT = 1;
    public static final int GL_FRAGMENT_SHADER_BIT = 2;
    public static final int GL_GEOMETRY_SHADER_BIT = 4;
    public static final int GL_TESS_CONTROL_SHADER_BIT = 8;
    public static final int GL_TESS_EVALUATION_SHADER_BIT = 16;
    public static final int GL_ALL_SHADER_BITS = -1;
    public static final int GL_PROGRAM_SEPARABLE = 33368;
    public static final int GL_ACTIVE_PROGRAM = 33369;
    public static final int GL_PROGRAM_PIPELINE_BINDING = 33370;

    private ARBSeparateShaderObjects() {
    }

    public static void glUseProgramStages(int n, int n2, int n3) {
        GL41.glUseProgramStages(n, n2, n3);
    }

    public static void glActiveShaderProgram(int n, int n2) {
        GL41.glActiveShaderProgram(n, n2);
    }

    public static int glCreateShaderProgram(int n, ByteBuffer byteBuffer) {
        return GL41.glCreateShaderProgram(n, byteBuffer);
    }

    public static int glCreateShaderProgram(int n, int n2, ByteBuffer byteBuffer) {
        return GL41.glCreateShaderProgram(n, n2, byteBuffer);
    }

    public static int glCreateShaderProgram(int n, ByteBuffer[] byteBufferArray) {
        return GL41.glCreateShaderProgram(n, byteBufferArray);
    }

    public static int glCreateShaderProgram(int n, CharSequence charSequence) {
        return GL41.glCreateShaderProgram(n, charSequence);
    }

    public static int glCreateShaderProgram(int n, CharSequence[] charSequenceArray) {
        return GL41.glCreateShaderProgram(n, charSequenceArray);
    }

    public static void glBindProgramPipeline(int n) {
        GL41.glBindProgramPipeline(n);
    }

    public static void glDeleteProgramPipelines(IntBuffer intBuffer) {
        GL41.glDeleteProgramPipelines(intBuffer);
    }

    public static void glDeleteProgramPipelines(int n) {
        GL41.glDeleteProgramPipelines(n);
    }

    public static void glGenProgramPipelines(IntBuffer intBuffer) {
        GL41.glGenProgramPipelines(intBuffer);
    }

    public static int glGenProgramPipelines() {
        return GL41.glGenProgramPipelines();
    }

    public static boolean glIsProgramPipeline(int n) {
        return GL41.glIsProgramPipeline(n);
    }

    public static void glProgramParameteri(int n, int n2, int n3) {
        GL41.glProgramParameteri(n, n2, n3);
    }

    public static void glGetProgramPipeline(int n, int n2, IntBuffer intBuffer) {
        GL41.glGetProgramPipeline(n, n2, intBuffer);
    }

    public static int glGetProgramPipelinei(int n, int n2) {
        return GL41.glGetProgramPipelinei(n, n2);
    }

    public static void glProgramUniform1i(int n, int n2, int n3) {
        GL41.glProgramUniform1i(n, n2, n3);
    }

    public static void glProgramUniform2i(int n, int n2, int n3, int n4) {
        GL41.glProgramUniform2i(n, n2, n3, n4);
    }

    public static void glProgramUniform3i(int n, int n2, int n3, int n4, int n5) {
        GL41.glProgramUniform3i(n, n2, n3, n4, n5);
    }

    public static void glProgramUniform4i(int n, int n2, int n3, int n4, int n5, int n6) {
        GL41.glProgramUniform4i(n, n2, n3, n4, n5, n6);
    }

    public static void glProgramUniform1f(int n, int n2, float f) {
        GL41.glProgramUniform1f(n, n2, f);
    }

    public static void glProgramUniform2f(int n, int n2, float f, float f2) {
        GL41.glProgramUniform2f(n, n2, f, f2);
    }

    public static void glProgramUniform3f(int n, int n2, float f, float f2, float f3) {
        GL41.glProgramUniform3f(n, n2, f, f2, f3);
    }

    public static void glProgramUniform4f(int n, int n2, float f, float f2, float f3, float f4) {
        GL41.glProgramUniform4f(n, n2, f, f2, f3, f4);
    }

    public static void glProgramUniform1d(int n, int n2, double d) {
        GL41.glProgramUniform1d(n, n2, d);
    }

    public static void glProgramUniform2d(int n, int n2, double d, double d2) {
        GL41.glProgramUniform2d(n, n2, d, d2);
    }

    public static void glProgramUniform3d(int n, int n2, double d, double d2, double d3) {
        GL41.glProgramUniform3d(n, n2, d, d2, d3);
    }

    public static void glProgramUniform4d(int n, int n2, double d, double d2, double d3, double d4) {
        GL41.glProgramUniform4d(n, n2, d, d2, d3, d4);
    }

    public static void glProgramUniform1(int n, int n2, IntBuffer intBuffer) {
        GL41.glProgramUniform1(n, n2, intBuffer);
    }

    public static void glProgramUniform2(int n, int n2, IntBuffer intBuffer) {
        GL41.glProgramUniform2(n, n2, intBuffer);
    }

    public static void glProgramUniform3(int n, int n2, IntBuffer intBuffer) {
        GL41.glProgramUniform3(n, n2, intBuffer);
    }

    public static void glProgramUniform4(int n, int n2, IntBuffer intBuffer) {
        GL41.glProgramUniform4(n, n2, intBuffer);
    }

    public static void glProgramUniform1(int n, int n2, FloatBuffer floatBuffer) {
        GL41.glProgramUniform1(n, n2, floatBuffer);
    }

    public static void glProgramUniform2(int n, int n2, FloatBuffer floatBuffer) {
        GL41.glProgramUniform2(n, n2, floatBuffer);
    }

    public static void glProgramUniform3(int n, int n2, FloatBuffer floatBuffer) {
        GL41.glProgramUniform3(n, n2, floatBuffer);
    }

    public static void glProgramUniform4(int n, int n2, FloatBuffer floatBuffer) {
        GL41.glProgramUniform4(n, n2, floatBuffer);
    }

    public static void glProgramUniform1(int n, int n2, DoubleBuffer doubleBuffer) {
        GL41.glProgramUniform1(n, n2, doubleBuffer);
    }

    public static void glProgramUniform2(int n, int n2, DoubleBuffer doubleBuffer) {
        GL41.glProgramUniform2(n, n2, doubleBuffer);
    }

    public static void glProgramUniform3(int n, int n2, DoubleBuffer doubleBuffer) {
        GL41.glProgramUniform3(n, n2, doubleBuffer);
    }

    public static void glProgramUniform4(int n, int n2, DoubleBuffer doubleBuffer) {
        GL41.glProgramUniform4(n, n2, doubleBuffer);
    }

    public static void glProgramUniform1ui(int n, int n2, int n3) {
        GL41.glProgramUniform1ui(n, n2, n3);
    }

    public static void glProgramUniform2ui(int n, int n2, int n3, int n4) {
        GL41.glProgramUniform2ui(n, n2, n3, n4);
    }

    public static void glProgramUniform3ui(int n, int n2, int n3, int n4, int n5) {
        GL41.glProgramUniform3ui(n, n2, n3, n4, n5);
    }

    public static void glProgramUniform4ui(int n, int n2, int n3, int n4, int n5, int n6) {
        GL41.glProgramUniform4ui(n, n2, n3, n4, n5, n6);
    }

    public static void glProgramUniform1u(int n, int n2, IntBuffer intBuffer) {
        GL41.glProgramUniform1u(n, n2, intBuffer);
    }

    public static void glProgramUniform2u(int n, int n2, IntBuffer intBuffer) {
        GL41.glProgramUniform2u(n, n2, intBuffer);
    }

    public static void glProgramUniform3u(int n, int n2, IntBuffer intBuffer) {
        GL41.glProgramUniform3u(n, n2, intBuffer);
    }

    public static void glProgramUniform4u(int n, int n2, IntBuffer intBuffer) {
        GL41.glProgramUniform4u(n, n2, intBuffer);
    }

    public static void glProgramUniformMatrix2(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        GL41.glProgramUniformMatrix2(n, n2, bl, floatBuffer);
    }

    public static void glProgramUniformMatrix3(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        GL41.glProgramUniformMatrix3(n, n2, bl, floatBuffer);
    }

    public static void glProgramUniformMatrix4(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        GL41.glProgramUniformMatrix4(n, n2, bl, floatBuffer);
    }

    public static void glProgramUniformMatrix2(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        GL41.glProgramUniformMatrix2(n, n2, bl, doubleBuffer);
    }

    public static void glProgramUniformMatrix3(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        GL41.glProgramUniformMatrix3(n, n2, bl, doubleBuffer);
    }

    public static void glProgramUniformMatrix4(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        GL41.glProgramUniformMatrix4(n, n2, bl, doubleBuffer);
    }

    public static void glProgramUniformMatrix2x3(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        GL41.glProgramUniformMatrix2x3(n, n2, bl, floatBuffer);
    }

    public static void glProgramUniformMatrix3x2(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        GL41.glProgramUniformMatrix3x2(n, n2, bl, floatBuffer);
    }

    public static void glProgramUniformMatrix2x4(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        GL41.glProgramUniformMatrix2x4(n, n2, bl, floatBuffer);
    }

    public static void glProgramUniformMatrix4x2(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        GL41.glProgramUniformMatrix4x2(n, n2, bl, floatBuffer);
    }

    public static void glProgramUniformMatrix3x4(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        GL41.glProgramUniformMatrix3x4(n, n2, bl, floatBuffer);
    }

    public static void glProgramUniformMatrix4x3(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        GL41.glProgramUniformMatrix4x3(n, n2, bl, floatBuffer);
    }

    public static void glProgramUniformMatrix2x3(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        GL41.glProgramUniformMatrix2x3(n, n2, bl, doubleBuffer);
    }

    public static void glProgramUniformMatrix3x2(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        GL41.glProgramUniformMatrix3x2(n, n2, bl, doubleBuffer);
    }

    public static void glProgramUniformMatrix2x4(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        GL41.glProgramUniformMatrix2x4(n, n2, bl, doubleBuffer);
    }

    public static void glProgramUniformMatrix4x2(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        GL41.glProgramUniformMatrix4x2(n, n2, bl, doubleBuffer);
    }

    public static void glProgramUniformMatrix3x4(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        GL41.glProgramUniformMatrix3x4(n, n2, bl, doubleBuffer);
    }

    public static void glProgramUniformMatrix4x3(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        GL41.glProgramUniformMatrix4x3(n, n2, bl, doubleBuffer);
    }

    public static void glValidateProgramPipeline(int n) {
        GL41.glValidateProgramPipeline(n);
    }

    public static void glGetProgramPipelineInfoLog(int n, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        GL41.glGetProgramPipelineInfoLog(n, intBuffer, byteBuffer);
    }

    public static String glGetProgramPipelineInfoLog(int n, int n2) {
        return GL41.glGetProgramPipelineInfoLog(n, n2);
    }
}
