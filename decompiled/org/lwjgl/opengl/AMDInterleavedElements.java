/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class AMDInterleavedElements {
    public static final int GL_VERTEX_ELEMENT_SWIZZLE_AMD = 37284;
    public static final int GL_VERTEX_ID_SWIZZLE_AMD = 37285;

    private AMDInterleavedElements() {
    }

    public static void glVertexAttribParameteriAMD(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribParameteriAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDInterleavedElements.nglVertexAttribParameteriAMD(n, n2, n3, l);
    }

    static native void nglVertexAttribParameteriAMD(int var0, int var1, int var2, long var3);
}
