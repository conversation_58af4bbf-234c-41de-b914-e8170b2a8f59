/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
static final class FastIntMap.Entry<T> {
    final int key;
    T value;
    FastIntMap.Entry<T> next;

    FastIntMap.Entry(int n, T t, FastIntMap.Entry<T> entry) {
        this.key = n;
        this.value = t;
        this.next = entry;
    }

    public final int getKey() {
        return this.key;
    }

    public final T getValue() {
        return this.value;
    }
}
