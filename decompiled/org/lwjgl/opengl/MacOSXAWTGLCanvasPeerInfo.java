/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Canvas;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.MacOSXCanvasPeerInfo;
import org.lwjgl.opengl.PixelFormat;

final class MacOSXAWTGLCanvasPeerInfo
extends MacOSXCanvasPeerInfo {
    private final Canvas component;

    MacOSXAWTGLCanvasPeerInfo(Canvas canvas, PixelFormat pixelFormat, ContextAttribs contextAttribs, boolean bl) {
        super(pixelFormat, contextAttribs, bl);
        this.component = canvas;
    }

    protected final void doLockAndInitHandle() {
        MacOSXAWTGLCanvasPeerInfo macOSXAWTGLCanvasPeerInfo = this;
        macOSXAWTGLCanvasPeerInfo.initHandle(macOSXAWTGLCanvasPeerInfo.component);
    }
}
