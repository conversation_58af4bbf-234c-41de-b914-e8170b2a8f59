/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GL14;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;

public final class ARBImaging {
    public static final int GL_BLEND_COLOR = 32773;
    public static final int GL_FUNC_ADD = 32774;
    public static final int GL_MIN = 32775;
    public static final int GL_MAX = 32776;
    public static final int GL_BLEND_EQUATION = 32777;
    public static final int GL_FUNC_SUBTRACT = 32778;
    public static final int GL_FUNC_REVERSE_SUBTRACT = 32779;
    public static final int GL_COLOR_MATRIX = 32945;
    public static final int GL_COLOR_MATRIX_STACK_DEPTH = 32946;
    public static final int GL_MAX_COLOR_MATRIX_STACK_DEPTH = 32947;
    public static final int GL_POST_COLOR_MATRIX_RED_SCALE = 32948;
    public static final int GL_POST_COLOR_MATRIX_GREEN_SCALE = 32949;
    public static final int GL_POST_COLOR_MATRIX_BLUE_SCALE = 32950;
    public static final int GL_POST_COLOR_MATRIX_ALPHA_SCALE = 32951;
    public static final int GL_POST_COLOR_MATRIX_RED_BIAS = 32952;
    public static final int GL_POST_COLOR_MATRIX_GREEN_BIAS = 32953;
    public static final int GL_POST_COLOR_MATRIX_BLUE_BIAS = 32954;
    public static final int GL_POST_COLOR_MATRIX_ALPHA_BIAS = 32955;
    public static final int GL_COLOR_TABLE = 32976;
    public static final int GL_POST_CONVOLUTION_COLOR_TABLE = 32977;
    public static final int GL_POST_COLOR_MATRIX_COLOR_TABLE = 32978;
    public static final int GL_PROXY_COLOR_TABLE = 32979;
    public static final int GL_PROXY_POST_CONVOLUTION_COLOR_TABLE = 32980;
    public static final int GL_PROXY_POST_COLOR_MATRIX_COLOR_TABLE = 32981;
    public static final int GL_COLOR_TABLE_SCALE = 32982;
    public static final int GL_COLOR_TABLE_BIAS = 32983;
    public static final int GL_COLOR_TABLE_FORMAT = 32984;
    public static final int GL_COLOR_TABLE_WIDTH = 32985;
    public static final int GL_COLOR_TABLE_RED_SIZE = 32986;
    public static final int GL_COLOR_TABLE_GREEN_SIZE = 32987;
    public static final int GL_COLOR_TABLE_BLUE_SIZE = 32988;
    public static final int GL_COLOR_TABLE_ALPHA_SIZE = 32989;
    public static final int GL_COLOR_TABLE_LUMINANCE_SIZE = 32990;
    public static final int GL_COLOR_TABLE_INTENSITY_SIZE = 32991;
    public static final int GL_CONVOLUTION_1D = 32784;
    public static final int GL_CONVOLUTION_2D = 32785;
    public static final int GL_SEPARABLE_2D = 32786;
    public static final int GL_CONVOLUTION_BORDER_MODE = 32787;
    public static final int GL_CONVOLUTION_FILTER_SCALE = 32788;
    public static final int GL_CONVOLUTION_FILTER_BIAS = 32789;
    public static final int GL_REDUCE = 32790;
    public static final int GL_CONVOLUTION_FORMAT = 32791;
    public static final int GL_CONVOLUTION_WIDTH = 32792;
    public static final int GL_CONVOLUTION_HEIGHT = 32793;
    public static final int GL_MAX_CONVOLUTION_WIDTH = 32794;
    public static final int GL_MAX_CONVOLUTION_HEIGHT = 32795;
    public static final int GL_POST_CONVOLUTION_RED_SCALE = 32796;
    public static final int GL_POST_CONVOLUTION_GREEN_SCALE = 32797;
    public static final int GL_POST_CONVOLUTION_BLUE_SCALE = 32798;
    public static final int GL_POST_CONVOLUTION_ALPHA_SCALE = 32799;
    public static final int GL_POST_CONVOLUTION_RED_BIAS = 32800;
    public static final int GL_POST_CONVOLUTION_GREEN_BIAS = 32801;
    public static final int GL_POST_CONVOLUTION_BLUE_BIAS = 32802;
    public static final int GL_POST_CONVOLUTION_ALPHA_BIAS = 32803;
    public static final int GL_IGNORE_BORDER = 33104;
    public static final int GL_CONSTANT_BORDER = 33105;
    public static final int GL_REPLICATE_BORDER = 33107;
    public static final int GL_CONVOLUTION_BORDER_COLOR = 33108;
    public static final int GL_HISTOGRAM = 32804;
    public static final int GL_PROXY_HISTOGRAM = 32805;
    public static final int GL_HISTOGRAM_WIDTH = 32806;
    public static final int GL_HISTOGRAM_FORMAT = 32807;
    public static final int GL_HISTOGRAM_RED_SIZE = 32808;
    public static final int GL_HISTOGRAM_GREEN_SIZE = 32809;
    public static final int GL_HISTOGRAM_BLUE_SIZE = 32810;
    public static final int GL_HISTOGRAM_ALPHA_SIZE = 32811;
    public static final int GL_HISTOGRAM_LUMINANCE_SIZE = 32812;
    public static final int GL_HISTOGRAM_SINK = 32813;
    public static final int GL_MINMAX = 32814;
    public static final int GL_MINMAX_FORMAT = 32815;
    public static final int GL_MINMAX_SINK = 32816;
    public static final int GL_TABLE_TOO_LARGE = 32817;

    private ARBImaging() {
    }

    public static void glColorTable(int n, int n2, int n3, int n4, int n5, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorTable;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, 256);
        ARBImaging.nglColorTable(n, n2, n3, n4, n5, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glColorTable(int n, int n2, int n3, int n4, int n5, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorTable;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(doubleBuffer, 256);
        ARBImaging.nglColorTable(n, n2, n3, n4, n5, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glColorTable(int n, int n2, int n3, int n4, int n5, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorTable;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(floatBuffer, 256);
        ARBImaging.nglColorTable(n, n2, n3, n4, n5, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglColorTable(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glColorTable(int n, int n2, int n3, int n4, int n5, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glColorTable;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        ARBImaging.nglColorTableBO(n, n2, n3, n4, n5, l, l2);
    }

    static native void nglColorTableBO(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glColorSubTable(int n, int n2, int n3, int n4, int n5, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorSubTable;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, 256);
        ARBImaging.nglColorSubTable(n, n2, n3, n4, n5, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glColorSubTable(int n, int n2, int n3, int n4, int n5, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorSubTable;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(doubleBuffer, 256);
        ARBImaging.nglColorSubTable(n, n2, n3, n4, n5, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glColorSubTable(int n, int n2, int n3, int n4, int n5, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorSubTable;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(floatBuffer, 256);
        ARBImaging.nglColorSubTable(n, n2, n3, n4, n5, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglColorSubTable(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glColorSubTable(int n, int n2, int n3, int n4, int n5, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glColorSubTable;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        ARBImaging.nglColorSubTableBO(n, n2, n3, n4, n5, l, l2);
    }

    static native void nglColorSubTableBO(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glColorTableParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorTableParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        ARBImaging.nglColorTableParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglColorTableParameteriv(int var0, int var1, long var2, long var4);

    public static void glColorTableParameter(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorTableParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ARBImaging.nglColorTableParameterfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglColorTableParameterfv(int var0, int var1, long var2, long var4);

    public static void glCopyColorSubTable(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCopyColorSubTable;
        BufferChecks.checkFunctionAddress(l);
        ARBImaging.nglCopyColorSubTable(n, n2, n3, n4, n5, l);
    }

    static native void nglCopyColorSubTable(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glCopyColorTable(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCopyColorTable;
        BufferChecks.checkFunctionAddress(l);
        ARBImaging.nglCopyColorTable(n, n2, n3, n4, n5, l);
    }

    static native void nglCopyColorTable(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glGetColorTable(int n, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetColorTable;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 256);
        ARBImaging.nglGetColorTable(n, n2, n3, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetColorTable(int n, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetColorTable;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 256);
        ARBImaging.nglGetColorTable(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetColorTable(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetColorTable;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 256);
        ARBImaging.nglGetColorTable(n, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetColorTable(int var0, int var1, int var2, long var3, long var5);

    public static void glGetColorTableParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetColorTableParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        ARBImaging.nglGetColorTableParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetColorTableParameteriv(int var0, int var1, long var2, long var4);

    public static void glGetColorTableParameter(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetColorTableParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ARBImaging.nglGetColorTableParameterfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetColorTableParameterfv(int var0, int var1, long var2, long var4);

    public static void glBlendEquation(int n) {
        GL14.glBlendEquation(n);
    }

    public static void glBlendColor(float f, float f2, float f3, float f4) {
        GL14.glBlendColor(f, f2, f3, f4);
    }

    public static void glHistogram(int n, int n2, int n3, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glHistogram;
        BufferChecks.checkFunctionAddress(l);
        ARBImaging.nglHistogram(n, n2, n3, bl, l);
    }

    static native void nglHistogram(int var0, int var1, int var2, boolean var3, long var4);

    public static void glResetHistogram(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glResetHistogram;
        BufferChecks.checkFunctionAddress(l);
        ARBImaging.nglResetHistogram(n, l);
    }

    static native void nglResetHistogram(int var0, long var1);

    public static void glGetHistogram(int n, boolean bl, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetHistogram;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, 256);
        ARBImaging.nglGetHistogram(n, bl, n2, n3, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetHistogram(int n, boolean bl, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetHistogram;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(doubleBuffer, 256);
        ARBImaging.nglGetHistogram(n, bl, n2, n3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetHistogram(int n, boolean bl, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetHistogram;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(floatBuffer, 256);
        ARBImaging.nglGetHistogram(n, bl, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetHistogram(int n, boolean bl, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetHistogram;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(intBuffer, 256);
        ARBImaging.nglGetHistogram(n, bl, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetHistogram(int n, boolean bl, int n2, int n3, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetHistogram;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(shortBuffer, 256);
        ARBImaging.nglGetHistogram(n, bl, n2, n3, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetHistogram(int var0, boolean var1, int var2, int var3, long var4, long var6);

    public static void glGetHistogram(int n, boolean bl, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetHistogram;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        ARBImaging.nglGetHistogramBO(n, bl, n2, n3, l, l2);
    }

    static native void nglGetHistogramBO(int var0, boolean var1, int var2, int var3, long var4, long var6);

    public static void glGetHistogramParameter(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetHistogramParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 256);
        ARBImaging.nglGetHistogramParameterfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetHistogramParameterfv(int var0, int var1, long var2, long var4);

    public static void glGetHistogramParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetHistogramParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 256);
        ARBImaging.nglGetHistogramParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetHistogramParameteriv(int var0, int var1, long var2, long var4);

    public static void glMinmax(int n, int n2, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMinmax;
        BufferChecks.checkFunctionAddress(l);
        ARBImaging.nglMinmax(n, n2, bl, l);
    }

    static native void nglMinmax(int var0, int var1, boolean var2, long var3);

    public static void glResetMinmax(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glResetMinmax;
        BufferChecks.checkFunctionAddress(l);
        ARBImaging.nglResetMinmax(n, l);
    }

    static native void nglResetMinmax(int var0, long var1);

    public static void glGetMinmax(int n, boolean bl, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMinmax;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, 4);
        ARBImaging.nglGetMinmax(n, bl, n2, n3, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetMinmax(int n, boolean bl, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMinmax;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        ARBImaging.nglGetMinmax(n, bl, n2, n3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetMinmax(int n, boolean bl, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMinmax;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ARBImaging.nglGetMinmax(n, bl, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetMinmax(int n, boolean bl, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMinmax;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(intBuffer, 4);
        ARBImaging.nglGetMinmax(n, bl, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetMinmax(int n, boolean bl, int n2, int n3, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMinmax;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(shortBuffer, 4);
        ARBImaging.nglGetMinmax(n, bl, n2, n3, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetMinmax(int var0, boolean var1, int var2, int var3, long var4, long var6);

    public static void glGetMinmax(int n, boolean bl, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetMinmax;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        ARBImaging.nglGetMinmaxBO(n, bl, n2, n3, l, l2);
    }

    static native void nglGetMinmaxBO(int var0, boolean var1, int var2, int var3, long var4, long var6);

    public static void glGetMinmaxParameter(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMinmaxParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ARBImaging.nglGetMinmaxParameterfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetMinmaxParameterfv(int var0, int var1, long var2, long var4);

    public static void glGetMinmaxParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMinmaxParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        ARBImaging.nglGetMinmaxParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetMinmaxParameteriv(int var0, int var1, long var2, long var4);

    public static void glConvolutionFilter1D(int n, int n2, int n3, int n4, int n5, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glConvolutionFilter1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ByteBuffer byteBuffer2 = byteBuffer;
        BufferChecks.checkBuffer(byteBuffer2, GLChecks.calculateImageStorage(byteBuffer2, n4, n5, n3, 1, 1));
        ARBImaging.nglConvolutionFilter1D(n, n2, n3, n4, n5, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glConvolutionFilter1D(int n, int n2, int n3, int n4, int n5, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glConvolutionFilter1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        DoubleBuffer doubleBuffer2 = doubleBuffer;
        BufferChecks.checkBuffer(doubleBuffer2, GLChecks.calculateImageStorage(doubleBuffer2, n4, n5, n3, 1, 1));
        ARBImaging.nglConvolutionFilter1D(n, n2, n3, n4, n5, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glConvolutionFilter1D(int n, int n2, int n3, int n4, int n5, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glConvolutionFilter1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        FloatBuffer floatBuffer2 = floatBuffer;
        BufferChecks.checkBuffer(floatBuffer2, GLChecks.calculateImageStorage(floatBuffer2, n4, n5, n3, 1, 1));
        ARBImaging.nglConvolutionFilter1D(n, n2, n3, n4, n5, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glConvolutionFilter1D(int n, int n2, int n3, int n4, int n5, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glConvolutionFilter1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        IntBuffer intBuffer2 = intBuffer;
        BufferChecks.checkBuffer(intBuffer2, GLChecks.calculateImageStorage(intBuffer2, n4, n5, n3, 1, 1));
        ARBImaging.nglConvolutionFilter1D(n, n2, n3, n4, n5, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glConvolutionFilter1D(int n, int n2, int n3, int n4, int n5, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glConvolutionFilter1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ShortBuffer shortBuffer2 = shortBuffer;
        BufferChecks.checkBuffer(shortBuffer2, GLChecks.calculateImageStorage(shortBuffer2, n4, n5, n3, 1, 1));
        ARBImaging.nglConvolutionFilter1D(n, n2, n3, n4, n5, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglConvolutionFilter1D(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glConvolutionFilter1D(int n, int n2, int n3, int n4, int n5, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glConvolutionFilter1D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        ARBImaging.nglConvolutionFilter1DBO(n, n2, n3, n4, n5, l, l2);
    }

    static native void nglConvolutionFilter1DBO(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glConvolutionFilter2D(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glConvolutionFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ByteBuffer byteBuffer2 = byteBuffer;
        BufferChecks.checkBuffer(byteBuffer2, GLChecks.calculateImageStorage(byteBuffer2, n5, n6, n3, n4, 1));
        ARBImaging.nglConvolutionFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glConvolutionFilter2D(int n, int n2, int n3, int n4, int n5, int n6, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glConvolutionFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        IntBuffer intBuffer2 = intBuffer;
        BufferChecks.checkBuffer(intBuffer2, GLChecks.calculateImageStorage(intBuffer2, n5, n6, n3, n4, 1));
        ARBImaging.nglConvolutionFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glConvolutionFilter2D(int n, int n2, int n3, int n4, int n5, int n6, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glConvolutionFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ShortBuffer shortBuffer2 = shortBuffer;
        BufferChecks.checkBuffer(shortBuffer2, GLChecks.calculateImageStorage(shortBuffer2, n5, n6, n3, n4, 1));
        ARBImaging.nglConvolutionFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglConvolutionFilter2D(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glConvolutionFilter2D(int n, int n2, int n3, int n4, int n5, int n6, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glConvolutionFilter2D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        ARBImaging.nglConvolutionFilter2DBO(n, n2, n3, n4, n5, n6, l, l2);
    }

    static native void nglConvolutionFilter2DBO(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glConvolutionParameterf(int n, int n2, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glConvolutionParameterf;
        BufferChecks.checkFunctionAddress(l);
        ARBImaging.nglConvolutionParameterf(n, n2, f, l);
    }

    static native void nglConvolutionParameterf(int var0, int var1, float var2, long var3);

    public static void glConvolutionParameter(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glConvolutionParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ARBImaging.nglConvolutionParameterfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglConvolutionParameterfv(int var0, int var1, long var2, long var4);

    public static void glConvolutionParameteri(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glConvolutionParameteri;
        BufferChecks.checkFunctionAddress(l);
        ARBImaging.nglConvolutionParameteri(n, n2, n3, l);
    }

    static native void nglConvolutionParameteri(int var0, int var1, int var2, long var3);

    public static void glConvolutionParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glConvolutionParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        ARBImaging.nglConvolutionParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglConvolutionParameteriv(int var0, int var1, long var2, long var4);

    public static void glCopyConvolutionFilter1D(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCopyConvolutionFilter1D;
        BufferChecks.checkFunctionAddress(l);
        ARBImaging.nglCopyConvolutionFilter1D(n, n2, n3, n4, n5, l);
    }

    static native void nglCopyConvolutionFilter1D(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glCopyConvolutionFilter2D(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCopyConvolutionFilter2D;
        BufferChecks.checkFunctionAddress(l);
        ARBImaging.nglCopyConvolutionFilter2D(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglCopyConvolutionFilter2D(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glGetConvolutionFilter(int n, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetConvolutionFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglGetConvolutionFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetConvolutionFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetConvolutionFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglGetConvolutionFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetConvolutionFilter(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetConvolutionFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        ARBImaging.nglGetConvolutionFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetConvolutionFilter(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetConvolutionFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglGetConvolutionFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetConvolutionFilter(int n, int n2, int n3, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetConvolutionFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglGetConvolutionFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetConvolutionFilter(int var0, int var1, int var2, long var3, long var5);

    public static void glGetConvolutionFilter(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetConvolutionFilter;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        ARBImaging.nglGetConvolutionFilterBO(n, n2, n3, l, l2);
    }

    static native void nglGetConvolutionFilterBO(int var0, int var1, int var2, long var3, long var5);

    public static void glGetConvolutionParameter(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetConvolutionParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ARBImaging.nglGetConvolutionParameterfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetConvolutionParameterfv(int var0, int var1, long var2, long var4);

    public static void glGetConvolutionParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetConvolutionParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        ARBImaging.nglGetConvolutionParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetConvolutionParameteriv(int var0, int var1, long var2, long var4);

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, DoubleBuffer doubleBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, DoubleBuffer doubleBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, FloatBuffer floatBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, FloatBuffer floatBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, FloatBuffer floatBuffer, FloatBuffer floatBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(floatBuffer2);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(floatBuffer2), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, FloatBuffer floatBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, FloatBuffer floatBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, IntBuffer intBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, IntBuffer intBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, IntBuffer intBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, ShortBuffer shortBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, ShortBuffer shortBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, ShortBuffer shortBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, ShortBuffer shortBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBImaging.nglSeparableFilter2D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    static native void nglSeparableFilter2D(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8, long var10);

    public static void glSeparableFilter2D(int n, int n2, int n3, int n4, int n5, int n6, long l, long l2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glSeparableFilter2D;
        BufferChecks.checkFunctionAddress(l3);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        ARBImaging.nglSeparableFilter2DBO(n, n2, n3, n4, n5, n6, l, l2, l3);
    }

    static native void nglSeparableFilter2DBO(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8, long var10);

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, ByteBuffer byteBuffer2, ByteBuffer byteBuffer3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        BufferChecks.checkDirect(byteBuffer3);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(byteBuffer2), MemoryUtil.getAddress(byteBuffer3), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, ByteBuffer byteBuffer2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(byteBuffer2), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, ByteBuffer byteBuffer2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(byteBuffer2), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, ByteBuffer byteBuffer2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(byteBuffer2), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, IntBuffer intBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, IntBuffer intBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, IntBuffer intBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, ShortBuffer shortBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, ShortBuffer shortBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ByteBuffer byteBuffer, ShortBuffer shortBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(doubleBuffer2), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2, DoubleBuffer doubleBuffer3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        BufferChecks.checkDirect(doubleBuffer3);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(doubleBuffer2), MemoryUtil.getAddress(doubleBuffer3), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(doubleBuffer2), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(doubleBuffer2), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, IntBuffer intBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, IntBuffer intBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, ByteBuffer byteBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, ByteBuffer byteBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, ByteBuffer byteBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, DoubleBuffer doubleBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, IntBuffer intBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, IntBuffer intBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, ShortBuffer shortBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, ShortBuffer shortBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, FloatBuffer floatBuffer, ShortBuffer shortBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, ByteBuffer byteBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, ByteBuffer byteBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, ByteBuffer byteBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, DoubleBuffer doubleBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, IntBuffer intBuffer2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, IntBuffer intBuffer2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, IntBuffer intBuffer2, IntBuffer intBuffer3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        BufferChecks.checkDirect(intBuffer3);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(intBuffer3), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, IntBuffer intBuffer2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        BufferChecks.checkDirect(shortBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, ShortBuffer shortBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, ShortBuffer shortBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, IntBuffer intBuffer, ShortBuffer shortBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, ByteBuffer byteBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, ByteBuffer byteBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, ByteBuffer byteBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, IntBuffer intBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, IntBuffer intBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, ShortBuffer shortBuffer2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        BufferChecks.checkDirect(byteBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(shortBuffer2), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, ShortBuffer shortBuffer2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        BufferChecks.checkDirect(doubleBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(shortBuffer2), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, ShortBuffer shortBuffer2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        BufferChecks.checkDirect(intBuffer);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(shortBuffer2), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetSeparableFilter(int n, int n2, int n3, ShortBuffer shortBuffer, ShortBuffer shortBuffer2, ShortBuffer shortBuffer3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        BufferChecks.checkDirect(shortBuffer3);
        ARBImaging.nglGetSeparableFilter(n, n2, n3, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(shortBuffer2), MemoryUtil.getAddress(shortBuffer3), l);
    }

    static native void nglGetSeparableFilter(int var0, int var1, int var2, long var3, long var5, long var7, long var9);

    public static void glGetSeparableFilter(int n, int n2, int n3, long l, long l2, long l3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l4 = contextCapabilities.glGetSeparableFilter;
        BufferChecks.checkFunctionAddress(l4);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        ARBImaging.nglGetSeparableFilterBO(n, n2, n3, l, l2, l3, l4);
    }

    static native void nglGetSeparableFilterBO(int var0, int var1, int var2, long var3, long var5, long var7, long var9);
}
