/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.util.Iterator;
import org.lwjgl.opengl.FastIntMap;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public class FastIntMap.EntryIterator
implements Iterator<FastIntMap.Entry<V>> {
    private int nextIndex;
    private FastIntMap.Entry<V> current;

    FastIntMap.EntryIterator() {
        this.reset();
    }

    public void reset() {
        int n;
        this.current = null;
        FastIntMap.Entry[] entryArray = FastIntMap.this.table;
        for (n = entryArray.length - 1; n >= 0 && entryArray[n] == null; --n) {
        }
        this.nextIndex = n;
    }

    @Override
    public boolean hasNext() {
        if (this.nextIndex >= 0) {
            return true;
        }
        FastIntMap.Entry entry = this.current;
        return entry != null && entry.next != null;
    }

    @Override
    public FastIntMap.Entry<V> next() {
        FastIntMap.Entry entry = this.current;
        if (entry != null && (entry = entry.next) != null) {
            this.current = entry;
            return entry;
        }
        FastIntMap.Entry[] entryArray = FastIntMap.this.table;
        int n = this.nextIndex;
        entry = this.current = entryArray[n];
        while (--n >= 0 && entryArray[n] == null) {
        }
        this.nextIndex = n;
        return entry;
    }

    @Override
    public void remove() {
        FastIntMap.this.remove(this.current.key);
    }
}
