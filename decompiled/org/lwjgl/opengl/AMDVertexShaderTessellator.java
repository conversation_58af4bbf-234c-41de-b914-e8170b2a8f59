/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class AMDVertexShaderTessellator {
    public static final int GL_SAMPLER_BUFFER_AMD = 36865;
    public static final int GL_INT_SAMPLER_BUFFER_AMD = 36866;
    public static final int GL_UNSIGNED_INT_SAMPLER_BUFFER_AMD = 36867;
    public static final int GL_DISCRETE_AMD = 36870;
    public static final int GL_CONTINUOUS_AMD = 36871;
    public static final int GL_TESSELLATION_MODE_AMD = 36868;
    public static final int GL_TESSELLATION_FACTOR_AMD = 36869;

    private AMDVertexShaderTessellator() {
    }

    public static void glTessellationFactorAMD(float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTessellationFactorAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDVertexShaderTessellator.nglTessellationFactorAMD(f, l);
    }

    static native void nglTessellationFactorAMD(float var0, long var1);

    public static void glTessellationModeAMD(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTessellationModeAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDVertexShaderTessellator.nglTessellationModeAMD(n, l);
    }

    static native void nglTessellationModeAMD(int var0, long var1);
}
