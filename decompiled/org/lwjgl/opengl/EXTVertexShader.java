/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.StateTracker;

public final class EXTVertexShader {
    public static final int GL_VERTEX_SHADER_EXT = 34688;
    public static final int GL_VERTEX_SHADER_BINDING_EXT = 34689;
    public static final int GL_OP_INDEX_EXT = 34690;
    public static final int GL_OP_NEGATE_EXT = 34691;
    public static final int GL_OP_DOT3_EXT = 34692;
    public static final int GL_OP_DOT4_EXT = 34693;
    public static final int GL_OP_MUL_EXT = 34694;
    public static final int GL_OP_ADD_EXT = 34695;
    public static final int GL_OP_MADD_EXT = 34696;
    public static final int GL_OP_FRAC_EXT = 34697;
    public static final int GL_OP_MAX_EXT = 34698;
    public static final int GL_OP_MIN_EXT = 34699;
    public static final int GL_OP_SET_GE_EXT = 34700;
    public static final int GL_OP_SET_LT_EXT = 34701;
    public static final int GL_OP_CLAMP_EXT = 34702;
    public static final int GL_OP_FLOOR_EXT = 34703;
    public static final int GL_OP_ROUND_EXT = 34704;
    public static final int GL_OP_EXP_BASE_2_EXT = 34705;
    public static final int GL_OP_LOG_BASE_2_EXT = 34706;
    public static final int GL_OP_POWER_EXT = 34707;
    public static final int GL_OP_RECIP_EXT = 34708;
    public static final int GL_OP_RECIP_SQRT_EXT = 34709;
    public static final int GL_OP_SUB_EXT = 34710;
    public static final int GL_OP_CROSS_PRODUCT_EXT = 34711;
    public static final int GL_OP_MULTIPLY_MATRIX_EXT = 34712;
    public static final int GL_OP_MOV_EXT = 34713;
    public static final int GL_OUTPUT_VERTEX_EXT = 34714;
    public static final int GL_OUTPUT_COLOR0_EXT = 34715;
    public static final int GL_OUTPUT_COLOR1_EXT = 34716;
    public static final int GL_OUTPUT_TEXTURE_COORD0_EXT = 34717;
    public static final int GL_OUTPUT_TEXTURE_COORD1_EXT = 34718;
    public static final int GL_OUTPUT_TEXTURE_COORD2_EXT = 34719;
    public static final int GL_OUTPUT_TEXTURE_COORD3_EXT = 34720;
    public static final int GL_OUTPUT_TEXTURE_COORD4_EXT = 34721;
    public static final int GL_OUTPUT_TEXTURE_COORD5_EXT = 34722;
    public static final int GL_OUTPUT_TEXTURE_COORD6_EXT = 34723;
    public static final int GL_OUTPUT_TEXTURE_COORD7_EXT = 34724;
    public static final int GL_OUTPUT_TEXTURE_COORD8_EXT = 34725;
    public static final int GL_OUTPUT_TEXTURE_COORD9_EXT = 34726;
    public static final int GL_OUTPUT_TEXTURE_COORD10_EXT = 34727;
    public static final int GL_OUTPUT_TEXTURE_COORD11_EXT = 34728;
    public static final int GL_OUTPUT_TEXTURE_COORD12_EXT = 34729;
    public static final int GL_OUTPUT_TEXTURE_COORD13_EXT = 34730;
    public static final int GL_OUTPUT_TEXTURE_COORD14_EXT = 34731;
    public static final int GL_OUTPUT_TEXTURE_COORD15_EXT = 34732;
    public static final int GL_OUTPUT_TEXTURE_COORD16_EXT = 34733;
    public static final int GL_OUTPUT_TEXTURE_COORD17_EXT = 34734;
    public static final int GL_OUTPUT_TEXTURE_COORD18_EXT = 34735;
    public static final int GL_OUTPUT_TEXTURE_COORD19_EXT = 34736;
    public static final int GL_OUTPUT_TEXTURE_COORD20_EXT = 34737;
    public static final int GL_OUTPUT_TEXTURE_COORD21_EXT = 34738;
    public static final int GL_OUTPUT_TEXTURE_COORD22_EXT = 34739;
    public static final int GL_OUTPUT_TEXTURE_COORD23_EXT = 34740;
    public static final int GL_OUTPUT_TEXTURE_COORD24_EXT = 34741;
    public static final int GL_OUTPUT_TEXTURE_COORD25_EXT = 34742;
    public static final int GL_OUTPUT_TEXTURE_COORD26_EXT = 34743;
    public static final int GL_OUTPUT_TEXTURE_COORD27_EXT = 34744;
    public static final int GL_OUTPUT_TEXTURE_COORD28_EXT = 34745;
    public static final int GL_OUTPUT_TEXTURE_COORD29_EXT = 34746;
    public static final int GL_OUTPUT_TEXTURE_COORD30_EXT = 34747;
    public static final int GL_OUTPUT_TEXTURE_COORD31_EXT = 34748;
    public static final int GL_OUTPUT_FOG_EXT = 34749;
    public static final int GL_SCALAR_EXT = 34750;
    public static final int GL_VECTOR_EXT = 34751;
    public static final int GL_MATRIX_EXT = 34752;
    public static final int GL_VARIANT_EXT = 34753;
    public static final int GL_INVARIANT_EXT = 34754;
    public static final int GL_LOCAL_CONSTANT_EXT = 34755;
    public static final int GL_LOCAL_EXT = 34756;
    public static final int GL_MAX_VERTEX_SHADER_INSTRUCTIONS_EXT = 34757;
    public static final int GL_MAX_VERTEX_SHADER_VARIANTS_EXT = 34758;
    public static final int GL_MAX_VERTEX_SHADER_INVARIANTS_EXT = 34759;
    public static final int GL_MAX_VERTEX_SHADER_LOCAL_CONSTANTS_EXT = 34760;
    public static final int GL_MAX_VERTEX_SHADER_LOCALS_EXT = 34761;
    public static final int GL_MAX_OPTIMIZED_VERTEX_SHADER_INSTRUCTIONS_EXT = 34762;
    public static final int GL_MAX_OPTIMIZED_VERTEX_SHADER_VARIANTS_EXT = 34763;
    public static final int GL_MAX_OPTIMIZED_VERTEX_SHADER_INVARIANTS_EXT = 34764;
    public static final int GL_MAX_OPTIMIZED_VERTEX_SHADER_LOCAL_CONSTANTS_EXT = 34765;
    public static final int GL_MAX_OPTIMIZED_VERTEX_SHADER_LOCALS_EXT = 34766;
    public static final int GL_VERTEX_SHADER_INSTRUCTIONS_EXT = 34767;
    public static final int GL_VERTEX_SHADER_VARIANTS_EXT = 34768;
    public static final int GL_VERTEX_SHADER_INVARIANTS_EXT = 34769;
    public static final int GL_VERTEX_SHADER_LOCAL_CONSTANTS_EXT = 34770;
    public static final int GL_VERTEX_SHADER_LOCALS_EXT = 34771;
    public static final int GL_VERTEX_SHADER_OPTIMIZED_EXT = 34772;
    public static final int GL_X_EXT = 34773;
    public static final int GL_Y_EXT = 34774;
    public static final int GL_Z_EXT = 34775;
    public static final int GL_W_EXT = 34776;
    public static final int GL_NEGATIVE_X_EXT = 34777;
    public static final int GL_NEGATIVE_Y_EXT = 34778;
    public static final int GL_NEGATIVE_Z_EXT = 34779;
    public static final int GL_NEGATIVE_W_EXT = 34780;
    public static final int GL_ZERO_EXT = 34781;
    public static final int GL_ONE_EXT = 34782;
    public static final int GL_NEGATIVE_ONE_EXT = 34783;
    public static final int GL_NORMALIZED_RANGE_EXT = 34784;
    public static final int GL_FULL_RANGE_EXT = 34785;
    public static final int GL_CURRENT_VERTEX_EXT = 34786;
    public static final int GL_MVP_MATRIX_EXT = 34787;
    public static final int GL_VARIANT_VALUE_EXT = 34788;
    public static final int GL_VARIANT_DATATYPE_EXT = 34789;
    public static final int GL_VARIANT_ARRAY_STRIDE_EXT = 34790;
    public static final int GL_VARIANT_ARRAY_TYPE_EXT = 34791;
    public static final int GL_VARIANT_ARRAY_EXT = 34792;
    public static final int GL_VARIANT_ARRAY_POINTER_EXT = 34793;
    public static final int GL_INVARIANT_VALUE_EXT = 34794;
    public static final int GL_INVARIANT_DATATYPE_EXT = 34795;
    public static final int GL_LOCAL_CONSTANT_VALUE_EXT = 34796;
    public static final int GL_LOCAL_CONSTANT_DATATYPE_EXT = 34797;

    private EXTVertexShader() {
    }

    public static void glBeginVertexShaderEXT() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBeginVertexShaderEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexShader.nglBeginVertexShaderEXT(l);
    }

    static native void nglBeginVertexShaderEXT(long var0);

    public static void glEndVertexShaderEXT() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEndVertexShaderEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexShader.nglEndVertexShaderEXT(l);
    }

    static native void nglEndVertexShaderEXT(long var0);

    public static void glBindVertexShaderEXT(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindVertexShaderEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexShader.nglBindVertexShaderEXT(n, l);
    }

    static native void nglBindVertexShaderEXT(int var0, long var1);

    public static int glGenVertexShadersEXT(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenVertexShadersEXT;
        BufferChecks.checkFunctionAddress(l);
        n = EXTVertexShader.nglGenVertexShadersEXT(n, l);
        return n;
    }

    static native int nglGenVertexShadersEXT(int var0, long var1);

    public static void glDeleteVertexShaderEXT(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteVertexShaderEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexShader.nglDeleteVertexShaderEXT(n, l);
    }

    static native void nglDeleteVertexShaderEXT(int var0, long var1);

    public static void glShaderOp1EXT(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glShaderOp1EXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexShader.nglShaderOp1EXT(n, n2, n3, l);
    }

    static native void nglShaderOp1EXT(int var0, int var1, int var2, long var3);

    public static void glShaderOp2EXT(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glShaderOp2EXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexShader.nglShaderOp2EXT(n, n2, n3, n4, l);
    }

    static native void nglShaderOp2EXT(int var0, int var1, int var2, int var3, long var4);

    public static void glShaderOp3EXT(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glShaderOp3EXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexShader.nglShaderOp3EXT(n, n2, n3, n4, n5, l);
    }

    static native void nglShaderOp3EXT(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glSwizzleEXT(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSwizzleEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexShader.nglSwizzleEXT(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglSwizzleEXT(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glWriteMaskEXT(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glWriteMaskEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexShader.nglWriteMaskEXT(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglWriteMaskEXT(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glInsertComponentEXT(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInsertComponentEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexShader.nglInsertComponentEXT(n, n2, n3, l);
    }

    static native void nglInsertComponentEXT(int var0, int var1, int var2, long var3);

    public static void glExtractComponentEXT(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glExtractComponentEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexShader.nglExtractComponentEXT(n, n2, n3, l);
    }

    static native void nglExtractComponentEXT(int var0, int var1, int var2, long var3);

    public static int glGenSymbolsEXT(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenSymbolsEXT;
        BufferChecks.checkFunctionAddress(l);
        n = EXTVertexShader.nglGenSymbolsEXT(n, n2, n3, n4, l);
        return n;
    }

    static native int nglGenSymbolsEXT(int var0, int var1, int var2, int var3, long var4);

    public static void glSetInvariantEXT(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSetInvariantEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        EXTVertexShader.nglSetInvariantEXT(n, 5130, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glSetInvariantEXT(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSetInvariantEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        EXTVertexShader.nglSetInvariantEXT(n, 5126, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glSetInvariantEXT(int n, boolean bl, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSetInvariantEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 4);
        EXTVertexShader.nglSetInvariantEXT(n, bl ? 5121 : 5120, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glSetInvariantEXT(int n, boolean bl, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSetInvariantEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        EXTVertexShader.nglSetInvariantEXT(n, bl ? 5125 : 5124, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glSetInvariantEXT(int n, boolean bl, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSetInvariantEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(shortBuffer, 4);
        EXTVertexShader.nglSetInvariantEXT(n, bl ? 5123 : 5122, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglSetInvariantEXT(int var0, int var1, long var2, long var4);

    public static void glSetLocalConstantEXT(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSetLocalConstantEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        EXTVertexShader.nglSetLocalConstantEXT(n, 5130, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glSetLocalConstantEXT(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSetLocalConstantEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        EXTVertexShader.nglSetLocalConstantEXT(n, 5126, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glSetLocalConstantEXT(int n, boolean bl, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSetLocalConstantEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 4);
        EXTVertexShader.nglSetLocalConstantEXT(n, bl ? 5121 : 5120, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glSetLocalConstantEXT(int n, boolean bl, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSetLocalConstantEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        EXTVertexShader.nglSetLocalConstantEXT(n, bl ? 5125 : 5124, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glSetLocalConstantEXT(int n, boolean bl, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSetLocalConstantEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(shortBuffer, 4);
        EXTVertexShader.nglSetLocalConstantEXT(n, bl ? 5123 : 5122, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglSetLocalConstantEXT(int var0, int var1, long var2, long var4);

    public static void glVariantEXT(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVariantbvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 4);
        EXTVertexShader.nglVariantbvEXT(n, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglVariantbvEXT(int var0, long var1, long var3);

    public static void glVariantEXT(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVariantsvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(shortBuffer, 4);
        EXTVertexShader.nglVariantsvEXT(n, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVariantsvEXT(int var0, long var1, long var3);

    public static void glVariantEXT(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVariantivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        EXTVertexShader.nglVariantivEXT(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVariantivEXT(int var0, long var1, long var3);

    public static void glVariantEXT(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVariantfvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        EXTVertexShader.nglVariantfvEXT(n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglVariantfvEXT(int var0, long var1, long var3);

    public static void glVariantEXT(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVariantdvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        EXTVertexShader.nglVariantdvEXT(n, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVariantdvEXT(int var0, long var1, long var3);

    public static void glVariantuEXT(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVariantubvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 4);
        EXTVertexShader.nglVariantubvEXT(n, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglVariantubvEXT(int var0, long var1, long var3);

    public static void glVariantuEXT(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVariantusvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(shortBuffer, 4);
        EXTVertexShader.nglVariantusvEXT(n, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVariantusvEXT(int var0, long var1, long var3);

    public static void glVariantuEXT(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVariantuivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        EXTVertexShader.nglVariantuivEXT(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVariantuivEXT(int var0, long var1, long var3);

    public static void glVariantPointerEXT(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVariantPointerEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).EXT_vertex_shader_glVariantPointerEXT_pAddr = doubleBuffer;
        }
        EXTVertexShader.nglVariantPointerEXT(n, 5130, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glVariantPointerEXT(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVariantPointerEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).EXT_vertex_shader_glVariantPointerEXT_pAddr = floatBuffer;
        }
        EXTVertexShader.nglVariantPointerEXT(n, 5126, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glVariantPointerEXT(int n, boolean bl, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVariantPointerEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).EXT_vertex_shader_glVariantPointerEXT_pAddr = byteBuffer;
        }
        EXTVertexShader.nglVariantPointerEXT(n, bl ? 5121 : 5120, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glVariantPointerEXT(int n, boolean bl, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVariantPointerEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).EXT_vertex_shader_glVariantPointerEXT_pAddr = intBuffer;
        }
        EXTVertexShader.nglVariantPointerEXT(n, bl ? 5125 : 5124, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glVariantPointerEXT(int n, boolean bl, int n2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVariantPointerEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).EXT_vertex_shader_glVariantPointerEXT_pAddr = shortBuffer;
        }
        EXTVertexShader.nglVariantPointerEXT(n, bl ? 5123 : 5122, n2, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVariantPointerEXT(int var0, int var1, int var2, long var3, long var5);

    public static void glVariantPointerEXT(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glVariantPointerEXT;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        EXTVertexShader.nglVariantPointerEXTBO(n, n2, n3, l, l2);
    }

    static native void nglVariantPointerEXTBO(int var0, int var1, int var2, long var3, long var5);

    public static void glEnableVariantClientStateEXT(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEnableVariantClientStateEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexShader.nglEnableVariantClientStateEXT(n, l);
    }

    static native void nglEnableVariantClientStateEXT(int var0, long var1);

    public static void glDisableVariantClientStateEXT(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDisableVariantClientStateEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexShader.nglDisableVariantClientStateEXT(n, l);
    }

    static native void nglDisableVariantClientStateEXT(int var0, long var1);

    public static int glBindLightParameterEXT(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindLightParameterEXT;
        BufferChecks.checkFunctionAddress(l);
        n = EXTVertexShader.nglBindLightParameterEXT(n, n2, l);
        return n;
    }

    static native int nglBindLightParameterEXT(int var0, int var1, long var2);

    public static int glBindMaterialParameterEXT(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindMaterialParameterEXT;
        BufferChecks.checkFunctionAddress(l);
        n = EXTVertexShader.nglBindMaterialParameterEXT(n, n2, l);
        return n;
    }

    static native int nglBindMaterialParameterEXT(int var0, int var1, long var2);

    public static int glBindTexGenParameterEXT(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindTexGenParameterEXT;
        BufferChecks.checkFunctionAddress(l);
        n = EXTVertexShader.nglBindTexGenParameterEXT(n, n2, n3, l);
        return n;
    }

    static native int nglBindTexGenParameterEXT(int var0, int var1, int var2, long var3);

    public static int glBindTextureUnitParameterEXT(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindTextureUnitParameterEXT;
        BufferChecks.checkFunctionAddress(l);
        n = EXTVertexShader.nglBindTextureUnitParameterEXT(n, n2, l);
        return n;
    }

    static native int nglBindTextureUnitParameterEXT(int var0, int var1, long var2);

    public static int glBindParameterEXT(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindParameterEXT;
        BufferChecks.checkFunctionAddress(l);
        n = EXTVertexShader.nglBindParameterEXT(n, l);
        return n;
    }

    static native int nglBindParameterEXT(int var0, long var1);

    public static boolean glIsVariantEnabledEXT(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsVariantEnabledEXT;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = EXTVertexShader.nglIsVariantEnabledEXT(n, n2, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsVariantEnabledEXT(int var0, int var1, long var2);

    public static void glGetVariantBooleanEXT(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVariantBooleanvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 4);
        EXTVertexShader.nglGetVariantBooleanvEXT(n, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetVariantBooleanvEXT(int var0, int var1, long var2, long var4);

    public static void glGetVariantIntegerEXT(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVariantIntegervEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        EXTVertexShader.nglGetVariantIntegervEXT(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetVariantIntegervEXT(int var0, int var1, long var2, long var4);

    public static void glGetVariantFloatEXT(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVariantFloatvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        EXTVertexShader.nglGetVariantFloatvEXT(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetVariantFloatvEXT(int var0, int var1, long var2, long var4);

    public static ByteBuffer glGetVariantPointerEXT(int n, int n2, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetVariantPointervEXT;
        BufferChecks.checkFunctionAddress(l2);
        ByteBuffer byteBuffer = EXTVertexShader.nglGetVariantPointervEXT(n, n2, l, l2);
        if (LWJGLUtil.CHECKS && byteBuffer == null) {
            return null;
        }
        return byteBuffer.order(ByteOrder.nativeOrder());
    }

    static native ByteBuffer nglGetVariantPointervEXT(int var0, int var1, long var2, long var4);

    public static void glGetInvariantBooleanEXT(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetInvariantBooleanvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 4);
        EXTVertexShader.nglGetInvariantBooleanvEXT(n, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetInvariantBooleanvEXT(int var0, int var1, long var2, long var4);

    public static void glGetInvariantIntegerEXT(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetInvariantIntegervEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        EXTVertexShader.nglGetInvariantIntegervEXT(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetInvariantIntegervEXT(int var0, int var1, long var2, long var4);

    public static void glGetInvariantFloatEXT(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetInvariantFloatvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        EXTVertexShader.nglGetInvariantFloatvEXT(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetInvariantFloatvEXT(int var0, int var1, long var2, long var4);

    public static void glGetLocalConstantBooleanEXT(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetLocalConstantBooleanvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 4);
        EXTVertexShader.nglGetLocalConstantBooleanvEXT(n, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetLocalConstantBooleanvEXT(int var0, int var1, long var2, long var4);

    public static void glGetLocalConstantIntegerEXT(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetLocalConstantIntegervEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        EXTVertexShader.nglGetLocalConstantIntegervEXT(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetLocalConstantIntegervEXT(int var0, int var1, long var2, long var4);

    public static void glGetLocalConstantFloatEXT(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetLocalConstantFloatvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        EXTVertexShader.nglGetLocalConstantFloatvEXT(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetLocalConstantFloatvEXT(int var0, int var1, long var2, long var4);
}
