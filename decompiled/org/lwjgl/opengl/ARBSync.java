/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import java.nio.LongBuffer;
import org.lwjgl.opengl.GL32;
import org.lwjgl.opengl.GLSync;

public final class ARBSync {
    public static final int GL_MAX_SERVER_WAIT_TIMEOUT = 37137;
    public static final int GL_OBJECT_TYPE = 37138;
    public static final int GL_SYNC_CONDITION = 37139;
    public static final int GL_SYNC_STATUS = 37140;
    public static final int GL_SYNC_FLAGS = 37141;
    public static final int GL_SYNC_FENCE = 37142;
    public static final int GL_SYNC_GPU_COMMANDS_COMPLETE = 37143;
    public static final int GL_UNSIGNALED = 37144;
    public static final int GL_SIGNALED = 37145;
    public static final int GL_SYNC_FLUSH_COMMANDS_BIT = 1;
    public static final long GL_TIMEOUT_IGNORED = -1L;
    public static final int GL_ALREADY_SIGNALED = 37146;
    public static final int GL_TIMEOUT_EXPIRED = 37147;
    public static final int GL_CONDITION_SATISFIED = 37148;
    public static final int GL_WAIT_FAILED = 37149;

    private ARBSync() {
    }

    public static GLSync glFenceSync(int n, int n2) {
        return GL32.glFenceSync(n, n2);
    }

    public static boolean glIsSync(GLSync gLSync) {
        return GL32.glIsSync(gLSync);
    }

    public static void glDeleteSync(GLSync gLSync) {
        GL32.glDeleteSync(gLSync);
    }

    public static int glClientWaitSync(GLSync gLSync, int n, long l) {
        return GL32.glClientWaitSync(gLSync, n, l);
    }

    public static void glWaitSync(GLSync gLSync, int n, long l) {
        GL32.glWaitSync(gLSync, n, l);
    }

    public static void glGetInteger64(int n, LongBuffer longBuffer) {
        GL32.glGetInteger64(n, longBuffer);
    }

    public static long glGetInteger64(int n) {
        return GL32.glGetInteger64(n);
    }

    public static void glGetSync(GLSync gLSync, int n, IntBuffer intBuffer, IntBuffer intBuffer2) {
        GL32.glGetSync(gLSync, n, intBuffer, intBuffer2);
    }

    public static int glGetSync(GLSync gLSync, int n) {
        return GL32.glGetSynci(gLSync, n);
    }

    public static int glGetSynci(GLSync gLSync, int n) {
        return GL32.glGetSynci(gLSync, n);
    }
}
