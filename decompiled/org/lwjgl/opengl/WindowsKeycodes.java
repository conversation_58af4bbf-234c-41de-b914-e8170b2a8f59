/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

final class WindowsKeycodes {
    public static final int VK_LBUTTON = 1;
    public static final int VK_RBUTTON = 2;
    public static final int VK_CANCEL = 3;
    public static final int VK_MBUTTON = 4;
    public static final int VK_XBUTTON1 = 5;
    public static final int VK_XBUTTON2 = 6;
    public static final int VK_BACK = 8;
    public static final int VK_TAB = 9;
    public static final int VK_CLEAR = 12;
    public static final int VK_RETURN = 13;
    public static final int VK_SHIFT = 16;
    public static final int VK_CONTROL = 17;
    public static final int VK_MENU = 18;
    public static final int VK_PAUSE = 19;
    public static final int VK_CAPITAL = 20;
    public static final int VK_KANA = 21;
    public static final int VK_HANGEUL = 21;
    public static final int VK_HANGUL = 21;
    public static final int VK_JUNJA = 23;
    public static final int VK_FINAL = 24;
    public static final int VK_HANJA = 25;
    public static final int VK_KANJI = 25;
    public static final int VK_ESCAPE = 27;
    public static final int VK_CONVERT = 28;
    public static final int VK_NONCONVERT = 29;
    public static final int VK_ACCEPT = 30;
    public static final int VK_MODECHANGE = 31;
    public static final int VK_SPACE = 32;
    public static final int VK_PRIOR = 33;
    public static final int VK_NEXT = 34;
    public static final int VK_END = 35;
    public static final int VK_HOME = 36;
    public static final int VK_LEFT = 37;
    public static final int VK_UP = 38;
    public static final int VK_RIGHT = 39;
    public static final int VK_DOWN = 40;
    public static final int VK_SELECT = 41;
    public static final int VK_PRINT = 42;
    public static final int VK_EXECUTE = 43;
    public static final int VK_SNAPSHOT = 44;
    public static final int VK_INSERT = 45;
    public static final int VK_DELETE = 46;
    public static final int VK_HELP = 47;
    public static final int VK_0 = 48;
    public static final int VK_1 = 49;
    public static final int VK_2 = 50;
    public static final int VK_3 = 51;
    public static final int VK_4 = 52;
    public static final int VK_5 = 53;
    public static final int VK_6 = 54;
    public static final int VK_7 = 55;
    public static final int VK_8 = 56;
    public static final int VK_9 = 57;
    public static final int VK_A = 65;
    public static final int VK_B = 66;
    public static final int VK_C = 67;
    public static final int VK_D = 68;
    public static final int VK_E = 69;
    public static final int VK_F = 70;
    public static final int VK_G = 71;
    public static final int VK_H = 72;
    public static final int VK_I = 73;
    public static final int VK_J = 74;
    public static final int VK_K = 75;
    public static final int VK_L = 76;
    public static final int VK_M = 77;
    public static final int VK_N = 78;
    public static final int VK_O = 79;
    public static final int VK_P = 80;
    public static final int VK_Q = 81;
    public static final int VK_R = 82;
    public static final int VK_S = 83;
    public static final int VK_T = 84;
    public static final int VK_U = 85;
    public static final int VK_V = 86;
    public static final int VK_W = 87;
    public static final int VK_X = 88;
    public static final int VK_Y = 89;
    public static final int VK_Z = 90;
    public static final int VK_LWIN = 91;
    public static final int VK_RWIN = 92;
    public static final int VK_APPS = 93;
    public static final int VK_SLEEP = 95;
    public static final int VK_NUMPAD0 = 96;
    public static final int VK_NUMPAD1 = 97;
    public static final int VK_NUMPAD2 = 98;
    public static final int VK_NUMPAD3 = 99;
    public static final int VK_NUMPAD4 = 100;
    public static final int VK_NUMPAD5 = 101;
    public static final int VK_NUMPAD6 = 102;
    public static final int VK_NUMPAD7 = 103;
    public static final int VK_NUMPAD8 = 104;
    public static final int VK_NUMPAD9 = 105;
    public static final int VK_MULTIPLY = 106;
    public static final int VK_ADD = 107;
    public static final int VK_SEPARATOR = 108;
    public static final int VK_SUBTRACT = 109;
    public static final int VK_DECIMAL = 110;
    public static final int VK_DIVIDE = 111;
    public static final int VK_F1 = 112;
    public static final int VK_F2 = 113;
    public static final int VK_F3 = 114;
    public static final int VK_F4 = 115;
    public static final int VK_F5 = 116;
    public static final int VK_F6 = 117;
    public static final int VK_F7 = 118;
    public static final int VK_F8 = 119;
    public static final int VK_F9 = 120;
    public static final int VK_F10 = 121;
    public static final int VK_F11 = 122;
    public static final int VK_F12 = 123;
    public static final int VK_F13 = 124;
    public static final int VK_F14 = 125;
    public static final int VK_F15 = 126;
    public static final int VK_F16 = 127;
    public static final int VK_F17 = 128;
    public static final int VK_F18 = 129;
    public static final int VK_F19 = 130;
    public static final int VK_F20 = 131;
    public static final int VK_F21 = 132;
    public static final int VK_F22 = 133;
    public static final int VK_F23 = 134;
    public static final int VK_F24 = 135;
    public static final int VK_NUMLOCK = 144;
    public static final int VK_SCROLL = 145;
    public static final int VK_OEM_NEC_EQUAL = 146;
    public static final int VK_OEM_FJ_JISHO = 146;
    public static final int VK_OEM_FJ_MASSHOU = 147;
    public static final int VK_OEM_FJ_TOUROKU = 148;
    public static final int VK_OEM_FJ_LOYA = 149;
    public static final int VK_OEM_FJ_ROYA = 150;
    public static final int VK_LSHIFT = 160;
    public static final int VK_RSHIFT = 161;
    public static final int VK_LCONTROL = 162;
    public static final int VK_RCONTROL = 163;
    public static final int VK_LMENU = 164;
    public static final int VK_RMENU = 165;
    public static final int VK_BROWSER_BACK = 166;
    public static final int VK_BROWSER_FORWARD = 167;
    public static final int VK_BROWSER_REFRESH = 168;
    public static final int VK_BROWSER_STOP = 169;
    public static final int VK_BROWSER_SEARCH = 170;
    public static final int VK_BROWSER_FAVORITES = 171;
    public static final int VK_BROWSER_HOME = 172;
    public static final int VK_VOLUME_MUTE = 173;
    public static final int VK_VOLUME_DOWN = 174;
    public static final int VK_VOLUME_UP = 175;
    public static final int VK_MEDIA_NEXT_TRACK = 176;
    public static final int VK_MEDIA_PREV_TRACK = 177;
    public static final int VK_MEDIA_STOP = 178;
    public static final int VK_MEDIA_PLAY_PAUSE = 179;
    public static final int VK_LAUNCH_MAIL = 180;
    public static final int VK_LAUNCH_MEDIA_SELECT = 181;
    public static final int VK_LAUNCH_APP1 = 182;
    public static final int VK_LAUNCH_APP2 = 183;
    public static final int VK_OEM_1 = 186;
    public static final int VK_OEM_PLUS = 187;
    public static final int VK_OEM_COMMA = 188;
    public static final int VK_OEM_MINUS = 189;
    public static final int VK_OEM_PERIOD = 190;
    public static final int VK_OEM_2 = 191;
    public static final int VK_OEM_3 = 192;
    public static final int VK_OEM_4 = 219;
    public static final int VK_OEM_5 = 220;
    public static final int VK_OEM_6 = 221;
    public static final int VK_OEM_7 = 222;
    public static final int VK_OEM_8 = 223;
    public static final int VK_OEM_AX = 225;
    public static final int VK_OEM_102 = 226;
    public static final int VK_ICO_HELP = 227;
    public static final int VK_ICO_00 = 228;
    public static final int VK_PROCESSKEY = 229;
    public static final int VK_ICO_CLEAR = 230;
    public static final int VK_PACKET = 231;
    public static final int VK_OEM_RESET = 233;
    public static final int VK_OEM_JUMP = 234;
    public static final int VK_OEM_PA1 = 235;
    public static final int VK_OEM_PA2 = 236;
    public static final int VK_OEM_PA3 = 237;
    public static final int VK_OEM_WSCTRL = 238;
    public static final int VK_OEM_CUSEL = 239;
    public static final int VK_OEM_ATTN = 240;
    public static final int VK_OEM_FINISH = 241;
    public static final int VK_OEM_COPY = 242;
    public static final int VK_OEM_AUTO = 243;
    public static final int VK_OEM_ENLW = 244;
    public static final int VK_OEM_BACKTAB = 245;
    public static final int VK_ATTN = 246;
    public static final int VK_CRSEL = 247;
    public static final int VK_EXSEL = 248;
    public static final int VK_EREOF = 249;
    public static final int VK_PLAY = 250;
    public static final int VK_ZOOM = 251;
    public static final int VK_NONAME = 252;
    public static final int VK_PA1 = 253;
    public static final int VK_OEM_CLEAR = 254;

    WindowsKeycodes() {
    }

    public static int mapVirtualKeyToLWJGLCode(int n) {
        switch (n) {
            case 27: {
                return 1;
            }
            case 49: {
                return 2;
            }
            case 50: {
                return 3;
            }
            case 51: {
                return 4;
            }
            case 52: {
                return 5;
            }
            case 53: {
                return 6;
            }
            case 54: {
                return 7;
            }
            case 55: {
                return 8;
            }
            case 56: {
                return 9;
            }
            case 57: {
                return 10;
            }
            case 48: {
                return 11;
            }
            case 189: {
                return 12;
            }
            case 187: {
                return 13;
            }
            case 8: {
                return 14;
            }
            case 9: {
                return 15;
            }
            case 81: {
                return 16;
            }
            case 87: {
                return 17;
            }
            case 69: {
                return 18;
            }
            case 82: {
                return 19;
            }
            case 84: {
                return 20;
            }
            case 89: {
                return 21;
            }
            case 85: {
                return 22;
            }
            case 73: {
                return 23;
            }
            case 79: {
                return 24;
            }
            case 80: {
                return 25;
            }
            case 219: {
                return 26;
            }
            case 221: {
                return 27;
            }
            case 13: {
                return 28;
            }
            case 162: {
                return 29;
            }
            case 65: {
                return 30;
            }
            case 83: {
                return 31;
            }
            case 68: {
                return 32;
            }
            case 70: {
                return 33;
            }
            case 71: {
                return 34;
            }
            case 72: {
                return 35;
            }
            case 74: {
                return 36;
            }
            case 75: {
                return 37;
            }
            case 76: {
                return 38;
            }
            case 186: {
                return 39;
            }
            case 222: {
                return 40;
            }
            case 192: 
            case 223: {
                return 41;
            }
            case 160: {
                return 42;
            }
            case 220: {
                return 43;
            }
            case 90: {
                return 44;
            }
            case 88: {
                return 45;
            }
            case 67: {
                return 46;
            }
            case 86: {
                return 47;
            }
            case 66: {
                return 48;
            }
            case 78: {
                return 49;
            }
            case 77: {
                return 50;
            }
            case 188: {
                return 51;
            }
            case 190: {
                return 52;
            }
            case 191: {
                return 53;
            }
            case 161: {
                return 54;
            }
            case 106: {
                return 55;
            }
            case 164: {
                return 56;
            }
            case 32: {
                return 57;
            }
            case 20: {
                return 58;
            }
            case 112: {
                return 59;
            }
            case 113: {
                return 60;
            }
            case 114: {
                return 61;
            }
            case 115: {
                return 62;
            }
            case 116: {
                return 63;
            }
            case 117: {
                return 64;
            }
            case 118: {
                return 65;
            }
            case 119: {
                return 66;
            }
            case 120: {
                return 67;
            }
            case 121: {
                return 68;
            }
            case 144: {
                return 69;
            }
            case 145: {
                return 70;
            }
            case 103: {
                return 71;
            }
            case 104: {
                return 72;
            }
            case 105: {
                return 73;
            }
            case 109: {
                return 74;
            }
            case 100: {
                return 75;
            }
            case 101: {
                return 76;
            }
            case 102: {
                return 77;
            }
            case 107: {
                return 78;
            }
            case 97: {
                return 79;
            }
            case 98: {
                return 80;
            }
            case 99: {
                return 81;
            }
            case 96: {
                return 82;
            }
            case 110: {
                return 83;
            }
            case 122: {
                return 87;
            }
            case 123: {
                return 88;
            }
            case 124: {
                return 100;
            }
            case 125: {
                return 101;
            }
            case 126: {
                return 102;
            }
            case 21: {
                return 112;
            }
            case 28: {
                return 121;
            }
            case 29: {
                return 123;
            }
            case 25: {
                return 148;
            }
            case 163: {
                return 157;
            }
            case 108: {
                return 179;
            }
            case 111: {
                return 181;
            }
            case 44: {
                return 183;
            }
            case 165: {
                return 184;
            }
            case 19: {
                return 197;
            }
            case 36: {
                return 199;
            }
            case 38: {
                return 200;
            }
            case 33: {
                return 201;
            }
            case 37: {
                return 203;
            }
            case 39: {
                return 205;
            }
            case 35: {
                return 207;
            }
            case 40: {
                return 208;
            }
            case 34: {
                return 209;
            }
            case 45: {
                return 210;
            }
            case 46: {
                return 211;
            }
            case 91: {
                return 219;
            }
            case 92: {
                return 220;
            }
            case 93: {
                return 221;
            }
            case 95: {
                return 223;
            }
        }
        return 0;
    }
}
