/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.security.PrivilegedAction;
import org.lwjgl.opengl.XRandR;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
class LinuxDisplay.2
implements PrivilegedAction<Object> {
    LinuxDisplay.2() {
    }

    @Override
    public Object run() {
        XRandR.restoreConfiguration();
        return null;
    }
}
