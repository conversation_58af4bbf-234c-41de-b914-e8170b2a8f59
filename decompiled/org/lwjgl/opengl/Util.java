/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.opengl.GL11;
import org.lwjgl.opengl.OpenGLException;

public final class Util {
    private Util() {
    }

    public static void checkGLError() {
        int n = GL11.glGetError();
        if (n != 0) {
            throw new OpenGLException(n);
        }
    }

    public static String translateGLErrorString(int n) {
        switch (n) {
            case 0: {
                return "No error";
            }
            case 1280: {
                return "Invalid enum";
            }
            case 1281: {
                return "Invalid value";
            }
            case 1282: {
                return "Invalid operation";
            }
            case 1283: {
                return "Stack overflow";
            }
            case 1284: {
                return "Stack underflow";
            }
            case 1285: {
                return "Out of memory";
            }
            case 32817: {
                return "Table too large";
            }
            case 1286: {
                return "Invalid framebuffer operation";
            }
        }
        return null;
    }
}
