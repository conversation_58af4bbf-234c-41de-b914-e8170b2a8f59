/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;

public final class ARBDrawInstanced {
    private ARBDrawInstanced() {
    }

    public static void glDrawArraysInstancedARB(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawArraysInstancedARB;
        BufferChecks.checkFunctionAddress(l);
        ARBDrawInstanced.nglDrawArraysInstancedARB(n, n2, n3, n4, l);
    }

    static native void nglDrawArraysInstancedARB(int var0, int var1, int var2, int var3, long var4);

    public static void glDrawElementsInstancedARB(int n, ByteBuffer byteBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsInstancedARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBDrawInstanced.nglDrawElementsInstancedARB(n, byteBuffer.remaining(), 5121, MemoryUtil.getAddress(byteBuffer), n2, l);
    }

    public static void glDrawElementsInstancedARB(int n, IntBuffer intBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsInstancedARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        ARBDrawInstanced.nglDrawElementsInstancedARB(n, intBuffer.remaining(), 5125, MemoryUtil.getAddress(intBuffer), n2, l);
    }

    public static void glDrawElementsInstancedARB(int n, ShortBuffer shortBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsInstancedARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        ARBDrawInstanced.nglDrawElementsInstancedARB(n, shortBuffer.remaining(), 5123, MemoryUtil.getAddress(shortBuffer), n2, l);
    }

    static native void nglDrawElementsInstancedARB(int var0, int var1, int var2, long var3, int var5, long var6);

    public static void glDrawElementsInstancedARB(int n, int n2, int n3, long l, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glDrawElementsInstancedARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureElementVBOenabled(contextCapabilities);
        ARBDrawInstanced.nglDrawElementsInstancedARBBO(n, n2, n3, l, n4, l2);
    }

    static native void nglDrawElementsInstancedARBBO(int var0, int var1, int var2, long var3, int var5, long var6);
}
