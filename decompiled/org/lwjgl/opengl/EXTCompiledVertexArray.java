/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class EXTCompiledVertexArray {
    public static final int GL_ARRAY_ELEMENT_LOCK_FIRST_EXT = 33192;
    public static final int GL_ARRAY_ELEMENT_LOCK_COUNT_EXT = 33193;

    private EXTCompiledVertexArray() {
    }

    public static void glLockArraysEXT(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLockArraysEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTCompiledVertexArray.nglLockArraysEXT(n, n2, l);
    }

    static native void nglLockArraysEXT(int var0, int var1, long var2);

    public static void glUnlockArraysEXT() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUnlockArraysEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTCompiledVertexArray.nglUnlockArraysEXT(l);
    }

    static native void nglUnlockArraysEXT(long var0);
}
