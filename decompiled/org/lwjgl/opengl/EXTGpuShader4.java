/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.StateTracker;

public final class EXTGpuShader4 {
    public static final int GL_VERTEX_ATTRIB_ARRAY_INTEGER_EXT = 35069;
    public static final int GL_SAMPLER_1D_ARRAY_EXT = 36288;
    public static final int GL_SAMPLER_2D_ARRAY_EXT = 36289;
    public static final int GL_SAMPLER_BUFFER_EXT = 36290;
    public static final int GL_SAMPLER_1D_ARRAY_SHADOW_EXT = 36291;
    public static final int GL_SAMPLER_2D_ARRAY_SHADOW_EXT = 36292;
    public static final int GL_SAMPLER_CUBE_SHADOW_EXT = 36293;
    public static final int GL_UNSIGNED_INT_VEC2_EXT = 36294;
    public static final int GL_UNSIGNED_INT_VEC3_EXT = 36295;
    public static final int GL_UNSIGNED_INT_VEC4_EXT = 36296;
    public static final int GL_INT_SAMPLER_1D_EXT = 36297;
    public static final int GL_INT_SAMPLER_2D_EXT = 36298;
    public static final int GL_INT_SAMPLER_3D_EXT = 36299;
    public static final int GL_INT_SAMPLER_CUBE_EXT = 36300;
    public static final int GL_INT_SAMPLER_2D_RECT_EXT = 36301;
    public static final int GL_INT_SAMPLER_1D_ARRAY_EXT = 36302;
    public static final int GL_INT_SAMPLER_2D_ARRAY_EXT = 36303;
    public static final int GL_INT_SAMPLER_BUFFER_EXT = 36304;
    public static final int GL_UNSIGNED_INT_SAMPLER_1D_EXT = 36305;
    public static final int GL_UNSIGNED_INT_SAMPLER_2D_EXT = 36306;
    public static final int GL_UNSIGNED_INT_SAMPLER_3D_EXT = 36307;
    public static final int GL_UNSIGNED_INT_SAMPLER_CUBE_EXT = 36308;
    public static final int GL_UNSIGNED_INT_SAMPLER_2D_RECT_EXT = 36309;
    public static final int GL_UNSIGNED_INT_SAMPLER_1D_ARRAY_EXT = 36310;
    public static final int GL_UNSIGNED_INT_SAMPLER_2D_ARRAY_EXT = 36311;
    public static final int GL_UNSIGNED_INT_SAMPLER_BUFFER_EXT = 36312;
    public static final int GL_MIN_PROGRAM_TEXEL_OFFSET_EXT = 35076;
    public static final int GL_MAX_PROGRAM_TEXEL_OFFSET_EXT = 35077;

    private EXTGpuShader4() {
    }

    public static void glVertexAttribI1iEXT(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI1iEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTGpuShader4.nglVertexAttribI1iEXT(n, n2, l);
    }

    static native void nglVertexAttribI1iEXT(int var0, int var1, long var2);

    public static void glVertexAttribI2iEXT(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI2iEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTGpuShader4.nglVertexAttribI2iEXT(n, n2, n3, l);
    }

    static native void nglVertexAttribI2iEXT(int var0, int var1, int var2, long var3);

    public static void glVertexAttribI3iEXT(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI3iEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTGpuShader4.nglVertexAttribI3iEXT(n, n2, n3, n4, l);
    }

    static native void nglVertexAttribI3iEXT(int var0, int var1, int var2, int var3, long var4);

    public static void glVertexAttribI4iEXT(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI4iEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTGpuShader4.nglVertexAttribI4iEXT(n, n2, n3, n4, n5, l);
    }

    static native void nglVertexAttribI4iEXT(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glVertexAttribI1uiEXT(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI1uiEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTGpuShader4.nglVertexAttribI1uiEXT(n, n2, l);
    }

    static native void nglVertexAttribI1uiEXT(int var0, int var1, long var2);

    public static void glVertexAttribI2uiEXT(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI2uiEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTGpuShader4.nglVertexAttribI2uiEXT(n, n2, n3, l);
    }

    static native void nglVertexAttribI2uiEXT(int var0, int var1, int var2, long var3);

    public static void glVertexAttribI3uiEXT(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI3uiEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTGpuShader4.nglVertexAttribI3uiEXT(n, n2, n3, n4, l);
    }

    static native void nglVertexAttribI3uiEXT(int var0, int var1, int var2, int var3, long var4);

    public static void glVertexAttribI4uiEXT(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI4uiEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTGpuShader4.nglVertexAttribI4uiEXT(n, n2, n3, n4, n5, l);
    }

    static native void nglVertexAttribI4uiEXT(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glVertexAttribI1EXT(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI1ivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        EXTGpuShader4.nglVertexAttribI1ivEXT(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexAttribI1ivEXT(int var0, long var1, long var3);

    public static void glVertexAttribI2EXT(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI2ivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 2);
        EXTGpuShader4.nglVertexAttribI2ivEXT(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexAttribI2ivEXT(int var0, long var1, long var3);

    public static void glVertexAttribI3EXT(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI3ivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 3);
        EXTGpuShader4.nglVertexAttribI3ivEXT(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexAttribI3ivEXT(int var0, long var1, long var3);

    public static void glVertexAttribI4EXT(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI4ivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        EXTGpuShader4.nglVertexAttribI4ivEXT(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexAttribI4ivEXT(int var0, long var1, long var3);

    public static void glVertexAttribI1uEXT(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI1uivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        EXTGpuShader4.nglVertexAttribI1uivEXT(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexAttribI1uivEXT(int var0, long var1, long var3);

    public static void glVertexAttribI2uEXT(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI2uivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 2);
        EXTGpuShader4.nglVertexAttribI2uivEXT(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexAttribI2uivEXT(int var0, long var1, long var3);

    public static void glVertexAttribI3uEXT(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI3uivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 3);
        EXTGpuShader4.nglVertexAttribI3uivEXT(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexAttribI3uivEXT(int var0, long var1, long var3);

    public static void glVertexAttribI4uEXT(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI4uivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        EXTGpuShader4.nglVertexAttribI4uivEXT(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexAttribI4uivEXT(int var0, long var1, long var3);

    public static void glVertexAttribI4EXT(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI4bvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 4);
        EXTGpuShader4.nglVertexAttribI4bvEXT(n, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglVertexAttribI4bvEXT(int var0, long var1, long var3);

    public static void glVertexAttribI4EXT(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI4svEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(shortBuffer, 4);
        EXTGpuShader4.nglVertexAttribI4svEXT(n, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexAttribI4svEXT(int var0, long var1, long var3);

    public static void glVertexAttribI4uEXT(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI4ubvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 4);
        EXTGpuShader4.nglVertexAttribI4ubvEXT(n, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglVertexAttribI4ubvEXT(int var0, long var1, long var3);

    public static void glVertexAttribI4uEXT(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribI4usvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(shortBuffer, 4);
        EXTGpuShader4.nglVertexAttribI4usvEXT(n, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexAttribI4usvEXT(int var0, long var1, long var3);

    public static void glVertexAttribIPointerEXT(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribIPointerEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = byteBuffer;
        }
        EXTGpuShader4.nglVertexAttribIPointerEXT(n, n2, n3, n4, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glVertexAttribIPointerEXT(int n, int n2, int n3, int n4, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribIPointerEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = intBuffer;
        }
        EXTGpuShader4.nglVertexAttribIPointerEXT(n, n2, n3, n4, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glVertexAttribIPointerEXT(int n, int n2, int n3, int n4, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribIPointerEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = shortBuffer;
        }
        EXTGpuShader4.nglVertexAttribIPointerEXT(n, n2, n3, n4, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexAttribIPointerEXT(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glVertexAttribIPointerEXT(int n, int n2, int n3, int n4, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glVertexAttribIPointerEXT;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        EXTGpuShader4.nglVertexAttribIPointerEXTBO(n, n2, n3, n4, l, l2);
    }

    static native void nglVertexAttribIPointerEXTBO(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glGetVertexAttribIEXT(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribIivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        EXTGpuShader4.nglGetVertexAttribIivEXT(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetVertexAttribIivEXT(int var0, int var1, long var2, long var4);

    public static void glGetVertexAttribIuEXT(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribIuivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        EXTGpuShader4.nglGetVertexAttribIuivEXT(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetVertexAttribIuivEXT(int var0, int var1, long var2, long var4);

    public static void glUniform1uiEXT(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform1uiEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTGpuShader4.nglUniform1uiEXT(n, n2, l);
    }

    static native void nglUniform1uiEXT(int var0, int var1, long var2);

    public static void glUniform2uiEXT(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform2uiEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTGpuShader4.nglUniform2uiEXT(n, n2, n3, l);
    }

    static native void nglUniform2uiEXT(int var0, int var1, int var2, long var3);

    public static void glUniform3uiEXT(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform3uiEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTGpuShader4.nglUniform3uiEXT(n, n2, n3, n4, l);
    }

    static native void nglUniform3uiEXT(int var0, int var1, int var2, int var3, long var4);

    public static void glUniform4uiEXT(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform4uiEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTGpuShader4.nglUniform4uiEXT(n, n2, n3, n4, n5, l);
    }

    static native void nglUniform4uiEXT(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glUniform1uEXT(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform1uivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        EXTGpuShader4.nglUniform1uivEXT(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglUniform1uivEXT(int var0, int var1, long var2, long var4);

    public static void glUniform2uEXT(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform2uivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        EXTGpuShader4.nglUniform2uivEXT(n, intBuffer.remaining() >> 1, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglUniform2uivEXT(int var0, int var1, long var2, long var4);

    public static void glUniform3uEXT(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform3uivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        EXTGpuShader4.nglUniform3uivEXT(n, intBuffer.remaining() / 3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglUniform3uivEXT(int var0, int var1, long var2, long var4);

    public static void glUniform4uEXT(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform4uivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        EXTGpuShader4.nglUniform4uivEXT(n, intBuffer.remaining() >> 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglUniform4uivEXT(int var0, int var1, long var2, long var4);

    public static void glGetUniformuEXT(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformuivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        EXTGpuShader4.nglGetUniformuivEXT(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetUniformuivEXT(int var0, int var1, long var2, long var4);

    public static void glBindFragDataLocationEXT(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindFragDataLocationEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        EXTGpuShader4.nglBindFragDataLocationEXT(n, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglBindFragDataLocationEXT(int var0, int var1, long var2, long var4);

    public static void glBindFragDataLocationEXT(int n, int n2, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindFragDataLocationEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTGpuShader4.nglBindFragDataLocationEXT(n, n2, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
    }

    public static int glGetFragDataLocationEXT(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetFragDataLocationEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = EXTGpuShader4.nglGetFragDataLocationEXT(n, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglGetFragDataLocationEXT(int var0, long var1, long var3);

    public static int glGetFragDataLocationEXT(int n, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetFragDataLocationEXT;
        BufferChecks.checkFunctionAddress(l);
        n = EXTGpuShader4.nglGetFragDataLocationEXT(n, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }
}
