/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

class StateStack {
    private int[] state_stack = new int[1];
    private int stack_pos = 0;

    public int getState() {
        return this.state_stack[this.stack_pos];
    }

    public void pushState(int n) {
        int n2 = ++this.stack_pos;
        if (this.stack_pos == this.state_stack.length) {
            this.growState();
        }
        this.state_stack[n2] = n;
    }

    public int popState() {
        return this.state_stack[this.stack_pos--];
    }

    public void growState() {
        int[] nArray = new int[this.state_stack.length + 1];
        System.arraycopy(this.state_stack, 0, nArray, 0, this.state_stack.length);
        this.state_stack = nArray;
    }

    StateStack(int n) {
        this.state_stack[this.stack_pos] = n;
    }
}
