/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;

private static final class WindowsDisplay.Rect {
    public int left;
    public int top;
    public int right;
    public int bottom;

    private WindowsDisplay.Rect() {
    }

    public final void copyToBuffer(IntBuffer intBuffer) {
        intBuffer.put(0, this.left).put(1, this.top).put(2, this.right).put(3, this.bottom);
    }

    public final void copyFromBuffer(IntBuffer intBuffer) {
        this.left = intBuffer.get(0);
        this.top = intBuffer.get(1);
        this.right = intBuffer.get(2);
        this.bottom = intBuffer.get(3);
    }

    public final void offset(int n, int n2) {
        this.left += n;
        this.top += n2;
        this.right += n;
        this.bottom += n2;
    }

    public static void intersect(WindowsDisplay.Rect rect, WindowsDisplay.Rect rect2, WindowsDisplay.Rect rect3) {
        rect3.left = Math.max(rect.left, rect2.left);
        rect3.top = Math.max(rect.top, rect2.top);
        rect3.right = Math.min(rect.right, rect2.right);
        rect3.bottom = Math.min(rect.bottom, rect2.bottom);
    }

    public final String toString() {
        return "Rect: left = " + this.left + " top = " + this.top + " right = " + this.right + " bottom = " + this.bottom + ", width: " + (this.right - this.left) + ", height: " + (this.bottom - this.top);
    }
}
