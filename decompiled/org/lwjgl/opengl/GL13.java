/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.StateTracker;

public final class GL13 {
    public static final int GL_TEXTURE0 = 33984;
    public static final int GL_TEXTURE1 = 33985;
    public static final int GL_TEXTURE2 = 33986;
    public static final int GL_TEXTURE3 = 33987;
    public static final int GL_TEXTURE4 = 33988;
    public static final int GL_TEXTURE5 = 33989;
    public static final int GL_TEXTURE6 = 33990;
    public static final int GL_TEXTURE7 = 33991;
    public static final int GL_TEXTURE8 = 33992;
    public static final int GL_TEXTURE9 = 33993;
    public static final int GL_TEXTURE10 = 33994;
    public static final int GL_TEXTURE11 = 33995;
    public static final int GL_TEXTURE12 = 33996;
    public static final int GL_TEXTURE13 = 33997;
    public static final int GL_TEXTURE14 = 33998;
    public static final int GL_TEXTURE15 = 33999;
    public static final int GL_TEXTURE16 = 34000;
    public static final int GL_TEXTURE17 = 34001;
    public static final int GL_TEXTURE18 = 34002;
    public static final int GL_TEXTURE19 = 34003;
    public static final int GL_TEXTURE20 = 34004;
    public static final int GL_TEXTURE21 = 34005;
    public static final int GL_TEXTURE22 = 34006;
    public static final int GL_TEXTURE23 = 34007;
    public static final int GL_TEXTURE24 = 34008;
    public static final int GL_TEXTURE25 = 34009;
    public static final int GL_TEXTURE26 = 34010;
    public static final int GL_TEXTURE27 = 34011;
    public static final int GL_TEXTURE28 = 34012;
    public static final int GL_TEXTURE29 = 34013;
    public static final int GL_TEXTURE30 = 34014;
    public static final int GL_TEXTURE31 = 34015;
    public static final int GL_ACTIVE_TEXTURE = 34016;
    public static final int GL_CLIENT_ACTIVE_TEXTURE = 34017;
    public static final int GL_MAX_TEXTURE_UNITS = 34018;
    public static final int GL_NORMAL_MAP = 34065;
    public static final int GL_REFLECTION_MAP = 34066;
    public static final int GL_TEXTURE_CUBE_MAP = 34067;
    public static final int GL_TEXTURE_BINDING_CUBE_MAP = 34068;
    public static final int GL_TEXTURE_CUBE_MAP_POSITIVE_X = 34069;
    public static final int GL_TEXTURE_CUBE_MAP_NEGATIVE_X = 34070;
    public static final int GL_TEXTURE_CUBE_MAP_POSITIVE_Y = 34071;
    public static final int GL_TEXTURE_CUBE_MAP_NEGATIVE_Y = 34072;
    public static final int GL_TEXTURE_CUBE_MAP_POSITIVE_Z = 34073;
    public static final int GL_TEXTURE_CUBE_MAP_NEGATIVE_Z = 34074;
    public static final int GL_PROXY_TEXTURE_CUBE_MAP = 34075;
    public static final int GL_MAX_CUBE_MAP_TEXTURE_SIZE = 34076;
    public static final int GL_COMPRESSED_ALPHA = 34025;
    public static final int GL_COMPRESSED_LUMINANCE = 34026;
    public static final int GL_COMPRESSED_LUMINANCE_ALPHA = 34027;
    public static final int GL_COMPRESSED_INTENSITY = 34028;
    public static final int GL_COMPRESSED_RGB = 34029;
    public static final int GL_COMPRESSED_RGBA = 34030;
    public static final int GL_TEXTURE_COMPRESSION_HINT = 34031;
    public static final int GL_TEXTURE_COMPRESSED_IMAGE_SIZE = 34464;
    public static final int GL_TEXTURE_COMPRESSED = 34465;
    public static final int GL_NUM_COMPRESSED_TEXTURE_FORMATS = 34466;
    public static final int GL_COMPRESSED_TEXTURE_FORMATS = 34467;
    public static final int GL_MULTISAMPLE = 32925;
    public static final int GL_SAMPLE_ALPHA_TO_COVERAGE = 32926;
    public static final int GL_SAMPLE_ALPHA_TO_ONE = 32927;
    public static final int GL_SAMPLE_COVERAGE = 32928;
    public static final int GL_SAMPLE_BUFFERS = 32936;
    public static final int GL_SAMPLES = 32937;
    public static final int GL_SAMPLE_COVERAGE_VALUE = 32938;
    public static final int GL_SAMPLE_COVERAGE_INVERT = 32939;
    public static final int GL_MULTISAMPLE_BIT = 0x20000000;
    public static final int GL_TRANSPOSE_MODELVIEW_MATRIX = 34019;
    public static final int GL_TRANSPOSE_PROJECTION_MATRIX = 34020;
    public static final int GL_TRANSPOSE_TEXTURE_MATRIX = 34021;
    public static final int GL_TRANSPOSE_COLOR_MATRIX = 34022;
    public static final int GL_COMBINE = 34160;
    public static final int GL_COMBINE_RGB = 34161;
    public static final int GL_COMBINE_ALPHA = 34162;
    public static final int GL_SOURCE0_RGB = 34176;
    public static final int GL_SOURCE1_RGB = 34177;
    public static final int GL_SOURCE2_RGB = 34178;
    public static final int GL_SOURCE0_ALPHA = 34184;
    public static final int GL_SOURCE1_ALPHA = 34185;
    public static final int GL_SOURCE2_ALPHA = 34186;
    public static final int GL_OPERAND0_RGB = 34192;
    public static final int GL_OPERAND1_RGB = 34193;
    public static final int GL_OPERAND2_RGB = 34194;
    public static final int GL_OPERAND0_ALPHA = 34200;
    public static final int GL_OPERAND1_ALPHA = 34201;
    public static final int GL_OPERAND2_ALPHA = 34202;
    public static final int GL_RGB_SCALE = 34163;
    public static final int GL_ADD_SIGNED = 34164;
    public static final int GL_INTERPOLATE = 34165;
    public static final int GL_SUBTRACT = 34023;
    public static final int GL_CONSTANT = 34166;
    public static final int GL_PRIMARY_COLOR = 34167;
    public static final int GL_PREVIOUS = 34168;
    public static final int GL_DOT3_RGB = 34478;
    public static final int GL_DOT3_RGBA = 34479;
    public static final int GL_CLAMP_TO_BORDER = 33069;

    private GL13() {
    }

    public static void glActiveTexture(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glActiveTexture;
        BufferChecks.checkFunctionAddress(l);
        GL13.nglActiveTexture(n, l);
    }

    static native void nglActiveTexture(int var0, long var1);

    public static void glClientActiveTexture(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClientActiveTexture;
        BufferChecks.checkFunctionAddress(l);
        StateTracker.getReferences((ContextCapabilities)contextCapabilities).glClientActiveTexture = n - 33984;
        GL13.nglClientActiveTexture(n, l);
    }

    static native void nglClientActiveTexture(int var0, long var1);

    public static void glCompressedTexImage1D(int n, int n2, int n3, int n4, int n5, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL13.nglCompressedTexImage1D(n, n2, n3, n4, n5, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTexImage1D(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glCompressedTexImage1D(int n, int n2, int n3, int n4, int n5, int n6, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTexImage1D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL13.nglCompressedTexImage1DBO(n, n2, n3, n4, n5, n6, l, l2);
    }

    static native void nglCompressedTexImage1DBO(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glCompressedTexImage1D(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        GL13.nglCompressedTexImage1D(n, n2, n3, n4, n5, n6, 0L, l);
    }

    public static void glCompressedTexImage2D(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL13.nglCompressedTexImage2D(n, n2, n3, n4, n5, n6, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTexImage2D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, long var7, long var9);

    public static void glCompressedTexImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTexImage2D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL13.nglCompressedTexImage2DBO(n, n2, n3, n4, n5, n6, n7, l, l2);
    }

    static native void nglCompressedTexImage2DBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, long var7, long var9);

    public static void glCompressedTexImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        GL13.nglCompressedTexImage2D(n, n2, n3, n4, n5, n6, n7, 0L, l);
    }

    public static void glCompressedTexImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexImage3D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL13.nglCompressedTexImage3D(n, n2, n3, n4, n5, n6, n7, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTexImage3D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glCompressedTexImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTexImage3D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL13.nglCompressedTexImage3DBO(n, n2, n3, n4, n5, n6, n7, n8, l, l2);
    }

    static native void nglCompressedTexImage3DBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glCompressedTexImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexImage3D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        GL13.nglCompressedTexImage3D(n, n2, n3, n4, n5, n6, n7, n8, 0L, l);
    }

    public static void glCompressedTexSubImage1D(int n, int n2, int n3, int n4, int n5, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexSubImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL13.nglCompressedTexSubImage1D(n, n2, n3, n4, n5, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTexSubImage1D(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glCompressedTexSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTexSubImage1D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL13.nglCompressedTexSubImage1DBO(n, n2, n3, n4, n5, n6, l, l2);
    }

    static native void nglCompressedTexSubImage1DBO(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glCompressedTexSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexSubImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL13.nglCompressedTexSubImage2D(n, n2, n3, n4, n5, n6, n7, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTexSubImage2D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glCompressedTexSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTexSubImage2D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL13.nglCompressedTexSubImage2DBO(n, n2, n3, n4, n5, n6, n7, n8, l, l2);
    }

    static native void nglCompressedTexSubImage2DBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glCompressedTexSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexSubImage3D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL13.nglCompressedTexSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTexSubImage3D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9, long var10, long var12);

    public static void glCompressedTexSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTexSubImage3D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL13.nglCompressedTexSubImage3DBO(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, l, l2);
    }

    static native void nglCompressedTexSubImage3DBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9, long var10, long var12);

    public static void glGetCompressedTexImage(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCompressedTexImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL13.nglGetCompressedTexImage(n, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetCompressedTexImage(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCompressedTexImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        GL13.nglGetCompressedTexImage(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetCompressedTexImage(int n, int n2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCompressedTexImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        GL13.nglGetCompressedTexImage(n, n2, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetCompressedTexImage(int var0, int var1, long var2, long var4);

    public static void glGetCompressedTexImage(int n, int n2, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetCompressedTexImage;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        GL13.nglGetCompressedTexImageBO(n, n2, l, l2);
    }

    static native void nglGetCompressedTexImageBO(int var0, int var1, long var2, long var4);

    public static void glMultiTexCoord1f(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord1f;
        BufferChecks.checkFunctionAddress(l);
        GL13.nglMultiTexCoord1f(n, f, l);
    }

    static native void nglMultiTexCoord1f(int var0, float var1, long var2);

    public static void glMultiTexCoord1d(int n, double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord1d;
        BufferChecks.checkFunctionAddress(l);
        GL13.nglMultiTexCoord1d(n, d, l);
    }

    static native void nglMultiTexCoord1d(int var0, double var1, long var3);

    public static void glMultiTexCoord2f(int n, float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord2f;
        BufferChecks.checkFunctionAddress(l);
        GL13.nglMultiTexCoord2f(n, f, f2, l);
    }

    static native void nglMultiTexCoord2f(int var0, float var1, float var2, long var3);

    public static void glMultiTexCoord2d(int n, double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord2d;
        BufferChecks.checkFunctionAddress(l);
        GL13.nglMultiTexCoord2d(n, d, d2, l);
    }

    static native void nglMultiTexCoord2d(int var0, double var1, double var3, long var5);

    public static void glMultiTexCoord3f(int n, float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord3f;
        BufferChecks.checkFunctionAddress(l);
        GL13.nglMultiTexCoord3f(n, f, f2, f3, l);
    }

    static native void nglMultiTexCoord3f(int var0, float var1, float var2, float var3, long var4);

    public static void glMultiTexCoord3d(int n, double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord3d;
        BufferChecks.checkFunctionAddress(l);
        GL13.nglMultiTexCoord3d(n, d, d2, d3, l);
    }

    static native void nglMultiTexCoord3d(int var0, double var1, double var3, double var5, long var7);

    public static void glMultiTexCoord4f(int n, float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord4f;
        BufferChecks.checkFunctionAddress(l);
        GL13.nglMultiTexCoord4f(n, f, f2, f3, f4, l);
    }

    static native void nglMultiTexCoord4f(int var0, float var1, float var2, float var3, float var4, long var5);

    public static void glMultiTexCoord4d(int n, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord4d;
        BufferChecks.checkFunctionAddress(l);
        GL13.nglMultiTexCoord4d(n, d, d2, d3, d4, l);
    }

    static native void nglMultiTexCoord4d(int var0, double var1, double var3, double var5, double var7, long var9);

    public static void glLoadTransposeMatrix(FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLoadTransposeMatrixf;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 16);
        GL13.nglLoadTransposeMatrixf(MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglLoadTransposeMatrixf(long var0, long var2);

    public static void glLoadTransposeMatrix(DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLoadTransposeMatrixd;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 16);
        GL13.nglLoadTransposeMatrixd(MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglLoadTransposeMatrixd(long var0, long var2);

    public static void glMultTransposeMatrix(FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultTransposeMatrixf;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 16);
        GL13.nglMultTransposeMatrixf(MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglMultTransposeMatrixf(long var0, long var2);

    public static void glMultTransposeMatrix(DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultTransposeMatrixd;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 16);
        GL13.nglMultTransposeMatrixd(MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglMultTransposeMatrixd(long var0, long var2);

    public static void glSampleCoverage(float f, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSampleCoverage;
        BufferChecks.checkFunctionAddress(l);
        GL13.nglSampleCoverage(f, bl, l);
    }

    static native void nglSampleCoverage(float var0, boolean var1, long var2);
}
