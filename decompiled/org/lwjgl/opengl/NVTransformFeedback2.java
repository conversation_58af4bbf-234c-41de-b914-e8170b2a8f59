/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVTransformFeedback2 {
    public static final int GL_TRANSFORM_FEEDBACK_NV = 36386;
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_PAUSED_NV = 36387;
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_ACTIVE_NV = 36388;
    public static final int GL_TRANSFORM_FEEDBACK_BINDING_NV = 36389;

    private NVTransformFeedback2() {
    }

    public static void glBindTransformFeedbackNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindTransformFeedbackNV;
        BufferChecks.checkFunctionAddress(l);
        NVTransformFeedback2.nglBindTransformFeedbackNV(n, n2, l);
    }

    static native void nglBindTransformFeedbackNV(int var0, int var1, long var2);

    public static void glDeleteTransformFeedbacksNV(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteTransformFeedbacksNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        NVTransformFeedback2.nglDeleteTransformFeedbacksNV(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeleteTransformFeedbacksNV(int var0, long var1, long var3);

    public static void glDeleteTransformFeedbacksNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteTransformFeedbacksNV;
        BufferChecks.checkFunctionAddress(l);
        NVTransformFeedback2.nglDeleteTransformFeedbacksNV(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static void glGenTransformFeedbacksNV(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenTransformFeedbacksNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        NVTransformFeedback2.nglGenTransformFeedbacksNV(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenTransformFeedbacksNV(int var0, long var1, long var3);

    public static int glGenTransformFeedbacksNV() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenTransformFeedbacksNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        NVTransformFeedback2.nglGenTransformFeedbacksNV(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static boolean glIsTransformFeedbackNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsTransformFeedbackNV;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = NVTransformFeedback2.nglIsTransformFeedbackNV(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsTransformFeedbackNV(int var0, long var1);

    public static void glPauseTransformFeedbackNV() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPauseTransformFeedbackNV;
        BufferChecks.checkFunctionAddress(l);
        NVTransformFeedback2.nglPauseTransformFeedbackNV(l);
    }

    static native void nglPauseTransformFeedbackNV(long var0);

    public static void glResumeTransformFeedbackNV() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glResumeTransformFeedbackNV;
        BufferChecks.checkFunctionAddress(l);
        NVTransformFeedback2.nglResumeTransformFeedbackNV(l);
    }

    static native void nglResumeTransformFeedbackNV(long var0);

    public static void glDrawTransformFeedbackNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawTransformFeedbackNV;
        BufferChecks.checkFunctionAddress(l);
        NVTransformFeedback2.nglDrawTransformFeedbackNV(n, n2, l);
    }

    static native void nglDrawTransformFeedbackNV(int var0, int var1, long var2);
}
