/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.opengl.ContextGL;
import org.lwjgl.opengl.ContextImplementation;
import org.lwjgl.opengl.PeerInfo;

final class MacOSXContextImplementation
implements ContextImplementation {
    MacOSXContextImplementation() {
    }

    public final ByteBuffer create(PeerInfo peerInfo, IntBuffer buffer, ByteBuffer byteBuffer) {
        buffer = peerInfo.lockAndGetHandle();
        try {
            buffer = MacOSXContextImplementation.nCreate((ByteBuffer)buffer, byteBuffer);
            return buffer;
        }
        finally {
            peerInfo.unlock();
        }
    }

    private static native ByteBuffer nCreate(ByteBuffer var0, ByteBuffer var1);

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public final void swapBuffers() {
        ContextGL contextGL = ContextGL.getCurrentContext();
        if (contextGL == null) {
            throw new IllegalStateException("No context is current");
        }
        ContextGL contextGL2 = contextGL;
        synchronized (contextGL2) {
            MacOSXContextImplementation.nSwapBuffers(contextGL.getHandle());
            return;
        }
    }

    final native long getCGLShareGroup(ByteBuffer var1);

    private static native void nSwapBuffers(ByteBuffer var0);

    public final void update(ByteBuffer byteBuffer) {
        MacOSXContextImplementation.nUpdate(byteBuffer);
    }

    private static native void nUpdate(ByteBuffer var0);

    public final void releaseCurrentContext() {
        MacOSXContextImplementation.nReleaseCurrentContext();
    }

    private static native void nReleaseCurrentContext();

    public final void releaseDrawable(ByteBuffer byteBuffer) {
        MacOSXContextImplementation.clearDrawable(byteBuffer);
    }

    private static native void clearDrawable(ByteBuffer var0);

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    static void resetView(PeerInfo peerInfo, ContextGL contextGL) {
        ByteBuffer byteBuffer = peerInfo.lockAndGetHandle();
        try {
            ContextGL contextGL2 = contextGL;
            synchronized (contextGL2) {
                MacOSXContextImplementation.clearDrawable(contextGL.getHandle());
                MacOSXContextImplementation.setView(byteBuffer, contextGL.getHandle());
            }
            return;
        }
        finally {
            peerInfo.unlock();
        }
    }

    public final void makeCurrent(PeerInfo peerInfo, ByteBuffer byteBuffer) {
        ByteBuffer byteBuffer2 = peerInfo.lockAndGetHandle();
        try {
            MacOSXContextImplementation.setView(byteBuffer2, byteBuffer);
            MacOSXContextImplementation.nMakeCurrent(byteBuffer);
            return;
        }
        finally {
            peerInfo.unlock();
        }
    }

    private static native void setView(ByteBuffer var0, ByteBuffer var1);

    private static native void nMakeCurrent(ByteBuffer var0);

    public final boolean isCurrent(ByteBuffer byteBuffer) {
        boolean bl = MacOSXContextImplementation.nIsCurrent(byteBuffer);
        return bl;
    }

    private static native boolean nIsCurrent(ByteBuffer var0);

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public final void setSwapInterval(int n) {
        ContextGL contextGL;
        ContextGL contextGL2 = contextGL = ContextGL.getCurrentContext();
        synchronized (contextGL) {
            MacOSXContextImplementation.nSetSwapInterval(contextGL.getHandle(), n);
            // ** MonitorExit[var3_4] (shouldn't be in output)
            return;
        }
    }

    private static native void nSetSwapInterval(ByteBuffer var0, int var1);

    public final void destroy(PeerInfo peerInfo, ByteBuffer byteBuffer) {
        MacOSXContextImplementation.nDestroy(byteBuffer);
    }

    private static native void nDestroy(ByteBuffer var0);
}
