/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Canvas;
import java.awt.Component;
import java.awt.Container;
import java.awt.Insets;
import java.awt.Point;
import java.awt.event.ComponentEvent;
import java.awt.event.ComponentListener;
import java.nio.ByteBuffer;
import javax.swing.SwingUtilities;
import org.lwjgl.opengl.AWTSurfaceLock;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.MacOSXPeerInfo;
import org.lwjgl.opengl.PixelFormat;

abstract class MacOSXCanvasPeerInfo
extends MacOSXPeerInfo {
    private final AWTSurfaceLock awt_surface = new AWTSurfaceLock();
    public ByteBuffer window_handle;

    protected MacOSXCanvasPeerInfo(PixelFormat pixelFormat, ContextAttribs contextAttribs, boolean bl) {
        super(pixelFormat, contextAttribs, true, true, bl, true);
    }

    protected void initHandle(Canvas canvas) {
        boolean bl = true;
        boolean bl2 = true;
        String string = System.getProperty("java.version");
        if (string.startsWith("1.5") || string.startsWith("1.6")) {
            bl = false;
        } else if (string.startsWith("1.7")) {
            bl2 = false;
        }
        Insets insets = MacOSXCanvasPeerInfo.getInsets(canvas);
        int n = insets != null ? insets.top : 0;
        int n2 = insets != null ? insets.left : 0;
        this.window_handle = MacOSXCanvasPeerInfo.nInitHandle(this.awt_surface.lockAndGetHandle(canvas), this.getHandle(), this.window_handle, bl, bl2, canvas.getX() - n2, canvas.getY() - n);
        if (string.startsWith("1.7")) {
            this.addComponentListener(canvas);
            MacOSXCanvasPeerInfo.reSetLayerBounds(canvas, this.getHandle());
        }
    }

    private void addComponentListener(final Canvas canvas) {
        ComponentListener[] componentListenerArray = canvas.getComponentListeners();
        for (int i = 0; i < componentListenerArray.length; ++i) {
            ComponentListener componentListener = componentListenerArray[i];
            if (componentListener.toString() != "CanvasPeerInfoListener") continue;
            return;
        }
        ComponentListener componentListener = new ComponentListener(){

            public void componentHidden(ComponentEvent componentEvent) {
            }

            public void componentMoved(ComponentEvent componentEvent) {
                MacOSXCanvasPeerInfo.reSetLayerBounds(canvas, MacOSXCanvasPeerInfo.this.getHandle());
            }

            public void componentResized(ComponentEvent componentEvent) {
                MacOSXCanvasPeerInfo.reSetLayerBounds(canvas, MacOSXCanvasPeerInfo.this.getHandle());
            }

            public void componentShown(ComponentEvent componentEvent) {
            }

            public String toString() {
                return "CanvasPeerInfoListener";
            }
        };
        canvas.addComponentListener(componentListener);
    }

    private static native ByteBuffer nInitHandle(ByteBuffer var0, ByteBuffer var1, ByteBuffer var2, boolean var3, boolean var4, int var5, int var6);

    private static native void nSetLayerPosition(ByteBuffer var0, int var1, int var2);

    private static native void nSetLayerBounds(ByteBuffer var0, int var1, int var2, int var3, int var4);

    private static void reSetLayerBounds(Canvas canvas, ByteBuffer byteBuffer) {
        Component component = SwingUtilities.getRoot(canvas);
        Point point = SwingUtilities.convertPoint(canvas.getParent(), canvas.getLocation(), component);
        int n = (int)point.getX();
        int n2 = (int)point.getY();
        Insets insets = MacOSXCanvasPeerInfo.getInsets(canvas);
        int n3 = insets != null ? insets.left : 0;
        n2 -= insets != null ? insets.top : 0;
        n2 = component.getHeight() - n2 - canvas.getHeight();
        MacOSXCanvasPeerInfo.nSetLayerBounds(byteBuffer, n -= n3, n2, canvas.getWidth(), canvas.getHeight());
    }

    protected void doUnlock() {
        this.awt_surface.unlock();
    }

    private static Insets getInsets(Canvas component) {
        if ((component = SwingUtilities.getRootPane(component)) != null) {
            return ((Container)component).getInsets();
        }
        return new Insets(0, 0, 0, 0);
    }
}
