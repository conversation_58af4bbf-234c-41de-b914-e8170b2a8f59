/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.opengl.Display;
import org.lwjgl.opengl.DrawableGL;
import org.lwjgl.opengl.GlobalLock;

static final class Display.5
extends DrawableGL {
    Display.5() {
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public final void destroy() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (!Display.isCreated()) {
                return;
            }
            Display.releaseDrawable();
            super.destroy();
            Display.destroyWindow();
            x = (y = -1);
            Display.access$702(null);
            Display.reset();
            Display.removeShutdownHook();
            return;
        }
    }
}
