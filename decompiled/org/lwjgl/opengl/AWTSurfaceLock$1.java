/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Canvas;
import java.security.PrivilegedExceptionAction;
import org.lwjgl.opengl.AWTSurfaceLock;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
class AWTSurfaceLock.1
implements PrivilegedExceptionAction<Boolean> {
    final /* synthetic */ Canvas val$component;

    AWTSurfaceLock.1(Canvas canvas) {
        this.val$component = canvas;
    }

    @Override
    public Boolean run() {
        return AWTSurfaceLock.lockAndInitHandle(AWTSurfaceLock.this.lock_buffer, this.val$component);
    }
}
