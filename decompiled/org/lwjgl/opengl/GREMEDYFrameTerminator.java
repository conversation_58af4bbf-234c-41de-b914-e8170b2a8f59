/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class GREMEDYFrameTerminator {
    private GREMEDYFrameTerminator() {
    }

    public static void glFrameTerminatorGREMEDY() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFrameTerminatorGREMEDY;
        BufferChecks.checkFunctionAddress(l);
        GREMEDYFrameTerminator.nglFrameTerminatorGREMEDY(l);
    }

    static native void nglFrameTerminatorGREMEDY(long var0);
}
