/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class EXTMultiDrawArrays {
    private EXTMultiDrawArrays() {
    }

    public static void glMultiDrawArraysEXT(int n, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiDrawArraysEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkBuffer(intBuffer2, intBuffer.remaining());
        EXTMultiDrawArrays.nglMultiDrawArraysEXT(n, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), intBuffer.remaining(), l);
    }

    static native void nglMultiDrawArraysEXT(int var0, long var1, long var3, int var5, long var6);
}
