/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class EXTStencilTwoSide {
    public static final int GL_STENCIL_TEST_TWO_SIDE_EXT = 35088;
    public static final int GL_ACTIVE_STENCIL_FACE_EXT = 35089;

    private EXTStencilTwoSide() {
    }

    public static void glActiveStencilFaceEXT(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glActiveStencilFaceEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTStencilTwoSide.nglActiveStencilFaceEXT(n, l);
    }

    static native void nglActiveStencilFaceEXT(int var0, long var1);
}
