/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.lwjgl.opengles.GLContext
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import org.lwjgl.opengl.WindowsPeerInfo;
import org.lwjgl.opengles.GLContext;

final class WindowsDisplayPeerInfo
extends WindowsPeerInfo {
    final boolean egl;

    WindowsDisplayPeerInfo(boolean bl) {
        this.egl = bl;
        if (bl) {
            GLContext.loadOpenGLLibrary();
            return;
        }
        org.lwjgl.opengl.GLContext.loadOpenGLLibrary();
    }

    final void initDC(long l, long l2) {
        WindowsDisplayPeerInfo.nInitDC(this.getHandle(), l, l2);
    }

    private static native void nInitDC(ByteBuffer var0, long var1, long var3);

    protected final void doLockAndInitHandle() {
    }

    protected final void doUnlock() {
    }

    public final void destroy() {
        super.destroy();
        if (this.egl) {
            GLContext.unloadOpenGLLibrary();
            return;
        }
        org.lwjgl.opengl.GLContext.unloadOpenGLLibrary();
    }
}
