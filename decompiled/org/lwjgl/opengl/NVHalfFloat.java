/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVHalfFloat {
    public static final int GL_HALF_FLOAT_NV = 5131;

    private NVHalfFloat() {
    }

    public static void glVertex2hNV(short s, short s2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertex2hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglVertex2hNV(s, s2, l);
    }

    static native void nglVertex2hNV(short var0, short var1, long var2);

    public static void glVertex3hNV(short s, short s2, short s3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertex3hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglVertex3hNV(s, s2, s3, l);
    }

    static native void nglVertex3hNV(short var0, short var1, short var2, long var3);

    public static void glVertex4hNV(short s, short s2, short s3, short s4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertex4hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglVertex4hNV(s, s2, s3, s4, l);
    }

    static native void nglVertex4hNV(short var0, short var1, short var2, short var3, long var4);

    public static void glNormal3hNV(short s, short s2, short s3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormal3hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglNormal3hNV(s, s2, s3, l);
    }

    static native void nglNormal3hNV(short var0, short var1, short var2, long var3);

    public static void glColor3hNV(short s, short s2, short s3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColor3hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglColor3hNV(s, s2, s3, l);
    }

    static native void nglColor3hNV(short var0, short var1, short var2, long var3);

    public static void glColor4hNV(short s, short s2, short s3, short s4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColor4hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglColor4hNV(s, s2, s3, s4, l);
    }

    static native void nglColor4hNV(short var0, short var1, short var2, short var3, long var4);

    public static void glTexCoord1hNV(short s) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoord1hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglTexCoord1hNV(s, l);
    }

    static native void nglTexCoord1hNV(short var0, long var1);

    public static void glTexCoord2hNV(short s, short s2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoord2hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglTexCoord2hNV(s, s2, l);
    }

    static native void nglTexCoord2hNV(short var0, short var1, long var2);

    public static void glTexCoord3hNV(short s, short s2, short s3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoord3hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglTexCoord3hNV(s, s2, s3, l);
    }

    static native void nglTexCoord3hNV(short var0, short var1, short var2, long var3);

    public static void glTexCoord4hNV(short s, short s2, short s3, short s4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoord4hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglTexCoord4hNV(s, s2, s3, s4, l);
    }

    static native void nglTexCoord4hNV(short var0, short var1, short var2, short var3, long var4);

    public static void glMultiTexCoord1hNV(int n, short s) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord1hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglMultiTexCoord1hNV(n, s, l);
    }

    static native void nglMultiTexCoord1hNV(int var0, short var1, long var2);

    public static void glMultiTexCoord2hNV(int n, short s, short s2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord2hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglMultiTexCoord2hNV(n, s, s2, l);
    }

    static native void nglMultiTexCoord2hNV(int var0, short var1, short var2, long var3);

    public static void glMultiTexCoord3hNV(int n, short s, short s2, short s3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord3hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglMultiTexCoord3hNV(n, s, s2, s3, l);
    }

    static native void nglMultiTexCoord3hNV(int var0, short var1, short var2, short var3, long var4);

    public static void glMultiTexCoord4hNV(int n, short s, short s2, short s3, short s4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord4hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglMultiTexCoord4hNV(n, s, s2, s3, s4, l);
    }

    static native void nglMultiTexCoord4hNV(int var0, short var1, short var2, short var3, short var4, long var5);

    public static void glFogCoordhNV(short s) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFogCoordhNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglFogCoordhNV(s, l);
    }

    static native void nglFogCoordhNV(short var0, long var1);

    public static void glSecondaryColor3hNV(short s, short s2, short s3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSecondaryColor3hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglSecondaryColor3hNV(s, s2, s3, l);
    }

    static native void nglSecondaryColor3hNV(short var0, short var1, short var2, long var3);

    public static void glVertexWeighthNV(short s) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexWeighthNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglVertexWeighthNV(s, l);
    }

    static native void nglVertexWeighthNV(short var0, long var1);

    public static void glVertexAttrib1hNV(int n, short s) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib1hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglVertexAttrib1hNV(n, s, l);
    }

    static native void nglVertexAttrib1hNV(int var0, short var1, long var2);

    public static void glVertexAttrib2hNV(int n, short s, short s2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib2hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglVertexAttrib2hNV(n, s, s2, l);
    }

    static native void nglVertexAttrib2hNV(int var0, short var1, short var2, long var3);

    public static void glVertexAttrib3hNV(int n, short s, short s2, short s3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib3hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglVertexAttrib3hNV(n, s, s2, s3, l);
    }

    static native void nglVertexAttrib3hNV(int var0, short var1, short var2, short var3, long var4);

    public static void glVertexAttrib4hNV(int n, short s, short s2, short s3, short s4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib4hNV;
        BufferChecks.checkFunctionAddress(l);
        NVHalfFloat.nglVertexAttrib4hNV(n, s, s2, s3, s4, l);
    }

    static native void nglVertexAttrib4hNV(int var0, short var1, short var2, short var3, short var4, long var5);

    public static void glVertexAttribs1NV(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs1hvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        NVHalfFloat.nglVertexAttribs1hvNV(n, shortBuffer.remaining(), MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexAttribs1hvNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribs2NV(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs2hvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        NVHalfFloat.nglVertexAttribs2hvNV(n, shortBuffer.remaining() >> 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexAttribs2hvNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribs3NV(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs3hvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        NVHalfFloat.nglVertexAttribs3hvNV(n, shortBuffer.remaining() / 3, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexAttribs3hvNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribs4NV(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs4hvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        NVHalfFloat.nglVertexAttribs4hvNV(n, shortBuffer.remaining() >> 2, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexAttribs4hvNV(int var0, int var1, long var2, long var4);
}
