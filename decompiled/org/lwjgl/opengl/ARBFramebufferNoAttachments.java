/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GL43;
import org.lwjgl.opengl.GLContext;

public final class ARBFramebufferNoAttachments {
    public static final int GL_FRAMEBUFFER_DEFAULT_WIDTH = 37648;
    public static final int GL_FRAMEBUFFER_DEFAULT_HEIGHT = 37649;
    public static final int GL_FRAMEBUFFER_DEFAULT_LAYERS = 37650;
    public static final int GL_FRAMEBUFFER_DEFAULT_SAMPLES = 37651;
    public static final int GL_FRAMEBUFFER_DEFAULT_FIXED_SAMPLE_LOCATIONS = 37652;
    public static final int GL_MAX_FRAMEBUFFER_WIDTH = 37653;
    public static final int GL_MAX_FRAMEBUFFER_HEIGHT = 37654;
    public static final int GL_MAX_FRAMEBUFFER_LAYERS = 37655;
    public static final int GL_MAX_FRAMEBUFFER_SAMPLES = 37656;

    private ARBFramebufferNoAttachments() {
    }

    public static void glFramebufferParameteri(int n, int n2, int n3) {
        GL43.glFramebufferParameteri(n, n2, n3);
    }

    public static void glGetFramebufferParameter(int n, int n2, IntBuffer intBuffer) {
        GL43.glGetFramebufferParameter(n, n2, intBuffer);
    }

    public static int glGetFramebufferParameteri(int n, int n2) {
        return GL43.glGetFramebufferParameteri(n, n2);
    }

    public static void glNamedFramebufferParameteriEXT(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedFramebufferParameteriEXT;
        BufferChecks.checkFunctionAddress(l);
        ARBFramebufferNoAttachments.nglNamedFramebufferParameteriEXT(n, n2, n3, l);
    }

    static native void nglNamedFramebufferParameteriEXT(int var0, int var1, int var2, long var3);

    public static void glGetNamedFramebufferParameterEXT(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetNamedFramebufferParameterivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        ARBFramebufferNoAttachments.nglGetNamedFramebufferParameterivEXT(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetNamedFramebufferParameterivEXT(int var0, int var1, long var2, long var4);

    public static int glGetNamedFramebufferParameterEXT(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetNamedFramebufferParameterivEXT;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        ARBFramebufferNoAttachments.nglGetNamedFramebufferParameterivEXT(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }
}
