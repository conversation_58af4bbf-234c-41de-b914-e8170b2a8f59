/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.opengl.GL40;

public final class ARBTransformFeedback3 {
    public static final int GL_MAX_TRANSFORM_FEEDBACK_BUFFERS = 36464;
    public static final int GL_MAX_VERTEX_STREAMS = 36465;

    private ARBTransformFeedback3() {
    }

    public static void glDrawTransformFeedbackStream(int n, int n2, int n3) {
        GL40.glDrawTransformFeedbackStream(n, n2, n3);
    }

    public static void glBeginQueryIndexed(int n, int n2, int n3) {
        GL40.glBeginQueryIndexed(n, n2, n3);
    }

    public static void glEndQueryIndexed(int n, int n2) {
        GL40.glEndQueryIndexed(n, n2);
    }

    public static void glGetQueryIndexed(int n, int n2, int n3, IntBuffer intBuffer) {
        GL40.glGetQueryIndexed(n, n2, n3, intBuffer);
    }

    public static int glGetQueryIndexedi(int n, int n2, int n3) {
        return GL40.glGetQueryIndexedi(n, n2, n3);
    }
}
