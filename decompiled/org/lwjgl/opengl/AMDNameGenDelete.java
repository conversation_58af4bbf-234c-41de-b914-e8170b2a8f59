/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class AMDNameGenDelete {
    public static final int GL_DATA_BUFFER_AMD = 37201;
    public static final int GL_PERFORMANCE_MONITOR_AMD = 37202;
    public static final int GL_QUERY_OBJECT_AMD = 37203;
    public static final int GL_VERTEX_ARRAY_OBJECT_AMD = 37204;
    public static final int GL_SAMPLER_OBJECT_AMD = 37205;

    private AMDNameGenDelete() {
    }

    public static void glGenNamesAMD(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenNamesAMD;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        AMDNameGenDelete.nglGenNamesAMD(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenNamesAMD(int var0, int var1, long var2, long var4);

    public static int glGenNamesAMD(int n) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenNamesAMD;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        AMDNameGenDelete.nglGenNamesAMD(n, 1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glDeleteNamesAMD(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteNamesAMD;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        AMDNameGenDelete.nglDeleteNamesAMD(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeleteNamesAMD(int var0, int var1, long var2, long var4);

    public static void glDeleteNamesAMD(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteNamesAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDNameGenDelete.nglDeleteNamesAMD(n, 1, APIUtil.getInt(contextCapabilities, n2), l);
    }

    public static boolean glIsNameAMD(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsNameAMD;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = AMDNameGenDelete.nglIsNameAMD(n, n2, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsNameAMD(int var0, int var1, long var2);
}
