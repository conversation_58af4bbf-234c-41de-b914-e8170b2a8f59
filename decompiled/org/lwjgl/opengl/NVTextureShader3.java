/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

public final class NVTextureShader3 {
    public static final int GL_OFFSET_PROJECTIVE_TEXTURE_2D_NV = 34896;
    public static final int GL_OFFSET_PROJECTIVE_TEXTURE_2D_SCALE_NV = 34897;
    public static final int GL_OFFSET_PROJECTIVE_TEXTURE_RECTANGLE_NV = 34898;
    public static final int GL_OFFSET_PROJECTIVE_TEXTURE_RECTANGLE_SCALE_NV = 34899;
    public static final int GL_OFFSET_HILO_TEXTURE_2D_NV = 34900;
    public static final int GL_OFFSET_HILO_TEXTURE_RECTANGLE_NV = 34901;
    public static final int GL_OFFSET_HILO_PROJECTIVE_TEXTURE_2D_NV = 34902;
    public static final int GL_OFFSET_HILO_PROJECTIVE_TEXTURE_RECTANGLE_NV = 34903;
    public static final int GL_DEPENDENT_HILO_TEXTURE_2D_NV = 34904;
    public static final int GL_DEPENDENT_RGB_TEXTURE_3D_NV = 34905;
    public static final int GL_DEPENDENT_RGB_TEXTURE_CUBE_MAP_NV = 34906;
    public static final int GL_DOT_PRODUCT_PASS_THROUGH_NV = 34907;
    public static final int GL_DOT_PRODUCT_TEXTURE_1D_NV = 34908;
    public static final int GL_DOT_PRODUCT_AFFINE_DEPTH_REPLACE_NV = 34909;
    public static final int GL_HILO8_NV = 34910;
    public static final int GL_SIGNED_HILO8_NV = 34911;
    public static final int GL_FORCE_BLUE_TO_ONE_NV = 34912;

    private NVTextureShader3() {
    }
}
