/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.LWJGLException;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.PointerBuffer;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.ContextGL;
import org.lwjgl.opengl.Display;
import org.lwjgl.opengl.DrawableLWJGL;
import org.lwjgl.opengl.GL11;
import org.lwjgl.opengl.GlobalLock;
import org.lwjgl.opengl.PeerInfo;
import org.lwjgl.opengl.PixelFormat;
import org.lwjgl.opengl.PixelFormatLWJGL;
import org.lwjgl.opengl.Util;

abstract class DrawableGL
implements DrawableLWJGL {
    protected PixelFormat pixel_format;
    protected PeerInfo peer_info;
    protected ContextGL context;

    protected DrawableGL() {
    }

    public void setPixelFormat(PixelFormatLWJGL pixelFormatLWJGL) {
        throw new UnsupportedOperationException();
    }

    public void setPixelFormat(PixelFormatLWJGL pixelFormatLWJGL, ContextAttribs contextAttribs) {
        this.pixel_format = (PixelFormat)pixelFormatLWJGL;
        this.peer_info = Display.getImplementation().createPeerInfo(this.pixel_format, contextAttribs);
    }

    public PixelFormatLWJGL getPixelFormat() {
        return this.pixel_format;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public ContextGL getContext() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            return this.context;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public ContextGL createSharedContext() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            this.checkDestroyed();
            return new ContextGL(this.peer_info, this.context.getContextAttribs(), this.context);
        }
    }

    public void checkGLError() {
        Util.checkGLError();
    }

    public void setSwapInterval(int n) {
        ContextGL.setSwapInterval(n);
    }

    public void swapBuffers() {
        ContextGL.swapBuffers();
    }

    public void initContext(float f, float f2, float f3) {
        GL11.glClearColor(f, f2, f3, 0.0f);
        GL11.glClear(16384);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public boolean isCurrent() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            this.checkDestroyed();
            return this.context.isCurrent();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void makeCurrent() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            this.checkDestroyed();
            this.context.makeCurrent();
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void releaseContext() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            this.checkDestroyed();
            if (this.context.isCurrent()) {
                this.context.releaseCurrent();
            }
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void destroy() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (this.context == null) {
                return;
            }
            try {
                this.releaseContext();
                this.context.forceDestroy();
                this.context = null;
                if (this.peer_info != null) {
                    this.peer_info.destroy();
                    this.peer_info = null;
                }
            }
            catch (LWJGLException lWJGLException) {
                LWJGLUtil.log("Exception occurred while destroying Drawable: " + lWJGLException);
            }
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void setCLSharingProperties(PointerBuffer pointerBuffer) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            this.checkDestroyed();
            this.context.setCLSharingProperties(pointerBuffer);
            return;
        }
    }

    protected final void checkDestroyed() {
        if (this.context == null) {
            throw new IllegalStateException("The Drawable has no context available.");
        }
    }
}
