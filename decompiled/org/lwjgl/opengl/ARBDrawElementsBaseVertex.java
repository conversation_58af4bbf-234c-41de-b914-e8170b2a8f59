/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.opengl.GL32;

public final class ARBDrawElementsBaseVertex {
    private ARBDrawElementsBaseVertex() {
    }

    public static void glDrawElementsBaseVertex(int n, ByteBuffer byteBuffer, int n2) {
        GL32.glDrawElementsBaseVertex(n, byteBuffer, n2);
    }

    public static void glDrawElementsBaseVertex(int n, IntBuffer intBuffer, int n2) {
        GL32.glDrawElementsBaseVertex(n, intBuffer, n2);
    }

    public static void glDrawElementsBaseVertex(int n, ShortBuffer shortBuffer, int n2) {
        GL32.glDrawElementsBaseVertex(n, shortBuffer, n2);
    }

    public static void glDrawElementsBaseVertex(int n, int n2, int n3, long l, int n4) {
        GL32.glDrawElementsBaseVertex(n, n2, n3, l, n4);
    }

    public static void glDrawRangeElementsBaseVertex(int n, int n2, int n3, ByteBuffer byteBuffer, int n4) {
        GL32.glDrawRangeElementsBaseVertex(n, n2, n3, byteBuffer, n4);
    }

    public static void glDrawRangeElementsBaseVertex(int n, int n2, int n3, IntBuffer intBuffer, int n4) {
        GL32.glDrawRangeElementsBaseVertex(n, n2, n3, intBuffer, n4);
    }

    public static void glDrawRangeElementsBaseVertex(int n, int n2, int n3, ShortBuffer shortBuffer, int n4) {
        GL32.glDrawRangeElementsBaseVertex(n, n2, n3, shortBuffer, n4);
    }

    public static void glDrawRangeElementsBaseVertex(int n, int n2, int n3, int n4, int n5, long l, int n6) {
        GL32.glDrawRangeElementsBaseVertex(n, n2, n3, n4, n5, l, n6);
    }

    public static void glDrawElementsInstancedBaseVertex(int n, ByteBuffer byteBuffer, int n2, int n3) {
        GL32.glDrawElementsInstancedBaseVertex(n, byteBuffer, n2, n3);
    }

    public static void glDrawElementsInstancedBaseVertex(int n, IntBuffer intBuffer, int n2, int n3) {
        GL32.glDrawElementsInstancedBaseVertex(n, intBuffer, n2, n3);
    }

    public static void glDrawElementsInstancedBaseVertex(int n, ShortBuffer shortBuffer, int n2, int n3) {
        GL32.glDrawElementsInstancedBaseVertex(n, shortBuffer, n2, n3);
    }

    public static void glDrawElementsInstancedBaseVertex(int n, int n2, int n3, long l, int n4, int n5) {
        GL32.glDrawElementsInstancedBaseVertex(n, n2, n3, l, n4, n5);
    }
}
