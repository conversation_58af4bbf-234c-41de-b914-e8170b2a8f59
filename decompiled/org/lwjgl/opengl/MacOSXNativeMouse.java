/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferUtils;
import org.lwjgl.LWJGLException;
import org.lwjgl.opengl.EventQueue;
import org.lwjgl.opengl.MacOSXDisplay;

final class Mac<PERSON><PERSON><PERSON><PERSON><PERSON>ouse
extends EventQueue {
    private static final int WHEEL_SCALE = 120;
    private static final int NUM_BUTTONS = 3;
    private ByteBuffer window_handle;
    private MacOSXDisplay display;
    private boolean grabbed;
    private float accum_dx;
    private float accum_dy;
    private int accum_dz;
    private float last_x;
    private float last_y;
    private boolean saved_control_state;
    private final ByteBuffer event = ByteBuffer.allocate(22);
    private IntBuffer delta_buffer = BufferUtils.createIntBuffer(2);
    private int skip_event;
    private final byte[] buttons = new byte[3];

    MacOSXNativeMouse(MacOSXDisplay macOSXDisplay, ByteBuffer byteBuffer) {
        super(22);
        this.display = macOSXDisplay;
        this.window_handle = byteBuffer;
    }

    private native void nSetCursorPosition(ByteBuffer var1, int var2, int var3);

    public static native void nGrabMouse(boolean var0);

    private native void nRegisterMouseListener(ByteBuffer var1);

    private native void nUnregisterMouseListener(ByteBuffer var1);

    private static native long nCreateCursor(int var0, int var1, int var2, int var3, int var4, IntBuffer var5, int var6, IntBuffer var7, int var8);

    private static native void nDestroyCursor(long var0);

    private static native void nSetCursor(long var0);

    public final synchronized void register() {
        MacOSXNativeMouse macOSXNativeMouse = this;
        macOSXNativeMouse.nRegisterMouseListener(macOSXNativeMouse.window_handle);
    }

    public static long createCursor(int n, int n2, int n3, int n4, int n5, IntBuffer intBuffer, IntBuffer intBuffer2) {
        try {
            IntBuffer intBuffer3 = intBuffer;
            IntBuffer intBuffer4 = intBuffer2;
            return MacOSXNativeMouse.nCreateCursor(n, n2, n3, n4, n5, intBuffer3, intBuffer3.position(), intBuffer4, intBuffer4 != null ? intBuffer2.position() : -1);
        }
        catch (LWJGLException lWJGLException) {
            LWJGLException lWJGLException2 = lWJGLException;
            throw lWJGLException;
        }
    }

    public static void destroyCursor(long l) {
        MacOSXNativeMouse.nDestroyCursor(l);
    }

    public static void setCursor(long l) {
        try {
            MacOSXNativeMouse.nSetCursor(l);
            return;
        }
        catch (LWJGLException lWJGLException) {
            LWJGLException lWJGLException2 = lWJGLException;
            throw lWJGLException;
        }
    }

    public final synchronized void setCursorPosition(int n, int n2) {
        MacOSXNativeMouse macOSXNativeMouse = this;
        macOSXNativeMouse.nSetCursorPosition(macOSXNativeMouse.window_handle, n, n2);
    }

    public final synchronized void unregister() {
        MacOSXNativeMouse macOSXNativeMouse = this;
        macOSXNativeMouse.nUnregisterMouseListener(macOSXNativeMouse.window_handle);
    }

    public final synchronized void setGrabbed(boolean bl) {
        this.grabbed = bl;
        MacOSXNativeMouse.nGrabMouse(bl);
        this.skip_event = 1;
        MacOSXNativeMouse macOSXNativeMouse = this;
        macOSXNativeMouse.accum_dy = 0.0f;
        macOSXNativeMouse.accum_dx = 0.0f;
    }

    public final synchronized boolean isGrabbed() {
        return this.grabbed;
    }

    protected final void resetCursorToCenter() {
        this.clearEvents();
        MacOSXNativeMouse macOSXNativeMouse = this;
        macOSXNativeMouse.accum_dy = 0.0f;
        macOSXNativeMouse.accum_dx = 0.0f;
        if (this.display != null) {
            this.last_x = this.display.getWidth() / 2;
            this.last_y = this.display.getHeight() / 2;
        }
    }

    private void putMouseEvent(byte by, byte by2, int n, long l) {
        if (this.grabbed) {
            this.putMouseEventWithCoords(by, by2, 0, 0, n, l);
            return;
        }
        this.putMouseEventWithCoords(by, by2, (int)this.last_x, (int)this.last_y, n, l);
    }

    protected final void putMouseEventWithCoords(byte by, byte by2, int n, int n2, int n3, long l) {
        this.event.clear();
        this.event.put(by).put(by2).putInt(n).putInt(n2).putInt(n3).putLong(l);
        this.event.flip();
        MacOSXNativeMouse macOSXNativeMouse = this;
        macOSXNativeMouse.putEvent(macOSXNativeMouse.event);
    }

    public final synchronized void poll(IntBuffer intBuffer, ByteBuffer byteBuffer) {
        if (this.grabbed) {
            intBuffer.put(0, (int)this.accum_dx);
            intBuffer.put(1, (int)this.accum_dy);
        } else {
            intBuffer.put(0, (int)this.last_x);
            intBuffer.put(1, (int)this.last_y);
        }
        intBuffer.put(2, this.accum_dz);
        MacOSXNativeMouse macOSXNativeMouse = this;
        this.accum_dz = 0;
        macOSXNativeMouse.accum_dy = 0.0f;
        macOSXNativeMouse.accum_dx = 0.0f;
        int n = byteBuffer.position();
        byteBuffer.put(this.buttons, 0, this.buttons.length);
        byteBuffer.position(n);
    }

    private void setCursorPos(float f, float f2, long l) {
        if (this.grabbed) {
            return;
        }
        float f3 = f - this.last_x;
        float f4 = f2 - this.last_y;
        this.addDelta(f3, f4);
        this.last_x = f;
        this.last_y = f2;
        this.putMouseEventWithCoords((byte)-1, (byte)0, (int)f, (int)f2, 0, l);
    }

    protected final void addDelta(float f, float f2) {
        this.accum_dx += f;
        this.accum_dy -= f2;
    }

    public final synchronized void setButton(int n, int n2, long l) {
        this.buttons[n] = (byte)n2;
        this.putMouseEvent((byte)n, (byte)n2, 0, l);
    }

    public final synchronized void mouseMoved(float f, float f2, float f3, float f4, float f5, long l) {
        if (this.skip_event > 0) {
            --this.skip_event;
            if (this.skip_event == 0) {
                this.last_x = f;
                this.last_y = f2;
            }
            return;
        }
        if (f5 != 0.0f) {
            if (f4 == 0.0f) {
                f4 = f3;
            }
            int n = (int)(f4 * 120.0f);
            this.accum_dz += n;
            this.putMouseEvent((byte)-1, (byte)0, n, l);
            return;
        }
        if (this.grabbed) {
            if (f3 != 0.0f || f4 != 0.0f) {
                this.putMouseEventWithCoords((byte)-1, (byte)0, (int)f3, (int)(-f4), 0, l);
                this.addDelta(f3, f4);
                return;
            }
        } else {
            this.setCursorPos(f, f2, l);
        }
    }
}
