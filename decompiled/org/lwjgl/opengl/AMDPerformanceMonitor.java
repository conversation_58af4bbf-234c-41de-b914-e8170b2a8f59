/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class AMDPerformanceMonitor {
    public static final int GL_COUNTER_TYPE_AMD = 35776;
    public static final int GL_COUNTER_RANGE_AMD = 35777;
    public static final int GL_UNSIGNED_INT64_AMD = 35778;
    public static final int GL_PERCENTAGE_AMD = 35779;
    public static final int GL_PERFMON_RESULT_AVAILABLE_AMD = 35780;
    public static final int GL_PERFMON_RESULT_SIZE_AMD = 35781;
    public static final int GL_PERFMON_RESULT_AMD = 35782;

    private AMDPerformanceMonitor() {
    }

    public static void glGetPerfMonitorGroupsAMD(IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPerfMonitorGroupsAMD;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(intBuffer2);
        AMDPerformanceMonitor.nglGetPerfMonitorGroupsAMD(MemoryUtil.getAddressSafe(intBuffer), intBuffer2.remaining(), MemoryUtil.getAddress(intBuffer2), l);
    }

    static native void nglGetPerfMonitorGroupsAMD(long var0, int var2, long var3, long var5);

    public static void glGetPerfMonitorCountersAMD(int n, IntBuffer intBuffer, IntBuffer intBuffer2, IntBuffer intBuffer3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPerfMonitorCountersAMD;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        BufferChecks.checkBuffer(intBuffer2, 1);
        if (intBuffer3 != null) {
            BufferChecks.checkDirect(intBuffer3);
        }
        AMDPerformanceMonitor.nglGetPerfMonitorCountersAMD(n, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), intBuffer3 == null ? 0 : intBuffer3.remaining(), MemoryUtil.getAddressSafe(intBuffer3), l);
    }

    static native void nglGetPerfMonitorCountersAMD(int var0, long var1, long var3, int var5, long var6, long var8);

    public static void glGetPerfMonitorGroupStringAMD(int n, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPerfMonitorGroupStringAMD;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        AMDPerformanceMonitor.nglGetPerfMonitorGroupStringAMD(n, byteBuffer == null ? 0 : byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddressSafe(byteBuffer), l);
    }

    static native void nglGetPerfMonitorGroupStringAMD(int var0, int var1, long var2, long var4, long var6);

    public static String glGetPerfMonitorGroupStringAMD(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPerfMonitorGroupStringAMD;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n2);
        AMDPerformanceMonitor.nglGetPerfMonitorGroupStringAMD(n, n2, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static void glGetPerfMonitorCounterStringAMD(int n, int n2, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPerfMonitorCounterStringAMD;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        AMDPerformanceMonitor.nglGetPerfMonitorCounterStringAMD(n, n2, byteBuffer == null ? 0 : byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddressSafe(byteBuffer), l);
    }

    static native void nglGetPerfMonitorCounterStringAMD(int var0, int var1, int var2, long var3, long var5, long var7);

    public static String glGetPerfMonitorCounterStringAMD(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPerfMonitorCounterStringAMD;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        AMDPerformanceMonitor.nglGetPerfMonitorCounterStringAMD(n, n2, n3, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static void glGetPerfMonitorCounterInfoAMD(int n, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPerfMonitorCounterInfoAMD;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 16);
        AMDPerformanceMonitor.nglGetPerfMonitorCounterInfoAMD(n, n2, n3, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetPerfMonitorCounterInfoAMD(int var0, int var1, int var2, long var3, long var5);

    public static void glGenPerfMonitorsAMD(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenPerfMonitorsAMD;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        AMDPerformanceMonitor.nglGenPerfMonitorsAMD(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenPerfMonitorsAMD(int var0, long var1, long var3);

    public static int glGenPerfMonitorsAMD() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenPerfMonitorsAMD;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        AMDPerformanceMonitor.nglGenPerfMonitorsAMD(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glDeletePerfMonitorsAMD(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeletePerfMonitorsAMD;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        AMDPerformanceMonitor.nglDeletePerfMonitorsAMD(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeletePerfMonitorsAMD(int var0, long var1, long var3);

    public static void glDeletePerfMonitorsAMD(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeletePerfMonitorsAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDPerformanceMonitor.nglDeletePerfMonitorsAMD(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static void glSelectPerfMonitorCountersAMD(int n, boolean bl, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSelectPerfMonitorCountersAMD;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        AMDPerformanceMonitor.nglSelectPerfMonitorCountersAMD(n, bl, n2, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglSelectPerfMonitorCountersAMD(int var0, boolean var1, int var2, int var3, long var4, long var6);

    public static void glSelectPerfMonitorCountersAMD(int n, boolean bl, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSelectPerfMonitorCountersAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDPerformanceMonitor.nglSelectPerfMonitorCountersAMD(n, bl, n2, 1, APIUtil.getInt(contextCapabilities, n3), l);
    }

    public static void glBeginPerfMonitorAMD(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBeginPerfMonitorAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDPerformanceMonitor.nglBeginPerfMonitorAMD(n, l);
    }

    static native void nglBeginPerfMonitorAMD(int var0, long var1);

    public static void glEndPerfMonitorAMD(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEndPerfMonitorAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDPerformanceMonitor.nglEndPerfMonitorAMD(n, l);
    }

    static native void nglEndPerfMonitorAMD(int var0, long var1);

    public static void glGetPerfMonitorCounterDataAMD(int n, int n2, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPerfMonitorCounterDataAMD;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        if (intBuffer2 != null) {
            BufferChecks.checkBuffer(intBuffer2, 1);
        }
        AMDPerformanceMonitor.nglGetPerfMonitorCounterDataAMD(n, n2, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddressSafe(intBuffer2), l);
    }

    static native void nglGetPerfMonitorCounterDataAMD(int var0, int var1, int var2, long var3, long var5, long var7);

    public static int glGetPerfMonitorCounterDataAMD(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetPerfMonitorCounterDataAMD;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        AMDPerformanceMonitor.nglGetPerfMonitorCounterDataAMD(n, n2, 4, MemoryUtil.getAddress((IntBuffer)object), 0L, l);
        return ((IntBuffer)object).get(0);
    }
}
