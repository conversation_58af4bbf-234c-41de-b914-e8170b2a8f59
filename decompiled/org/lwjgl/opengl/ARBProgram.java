/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public class ARBProgram {
    public static final int GL_PROGRAM_FORMAT_ASCII_ARB = 34933;
    public static final int GL_PROGRAM_LENGTH_ARB = 34343;
    public static final int GL_PROGRAM_FORMAT_ARB = 34934;
    public static final int GL_PROGRAM_BINDING_ARB = 34423;
    public static final int GL_PROGRAM_INSTRUCTIONS_ARB = 34976;
    public static final int GL_MAX_PROGRAM_INSTRUCTIONS_ARB = 34977;
    public static final int GL_PROGRAM_NATIVE_INSTRUCTIONS_ARB = 34978;
    public static final int GL_MAX_PROGRAM_NATIVE_INSTRUCTIONS_ARB = 34979;
    public static final int GL_PROGRAM_TEMPORARIES_ARB = 34980;
    public static final int GL_MAX_PROGRAM_TEMPORARIES_ARB = 34981;
    public static final int GL_PROGRAM_NATIVE_TEMPORARIES_ARB = 34982;
    public static final int GL_MAX_PROGRAM_NATIVE_TEMPORARIES_ARB = 34983;
    public static final int GL_PROGRAM_PARAMETERS_ARB = 34984;
    public static final int GL_MAX_PROGRAM_PARAMETERS_ARB = 34985;
    public static final int GL_PROGRAM_NATIVE_PARAMETERS_ARB = 34986;
    public static final int GL_MAX_PROGRAM_NATIVE_PARAMETERS_ARB = 34987;
    public static final int GL_PROGRAM_ATTRIBS_ARB = 34988;
    public static final int GL_MAX_PROGRAM_ATTRIBS_ARB = 34989;
    public static final int GL_PROGRAM_NATIVE_ATTRIBS_ARB = 34990;
    public static final int GL_MAX_PROGRAM_NATIVE_ATTRIBS_ARB = 34991;
    public static final int GL_MAX_PROGRAM_LOCAL_PARAMETERS_ARB = 34996;
    public static final int GL_MAX_PROGRAM_ENV_PARAMETERS_ARB = 34997;
    public static final int GL_PROGRAM_UNDER_NATIVE_LIMITS_ARB = 34998;
    public static final int GL_PROGRAM_STRING_ARB = 34344;
    public static final int GL_PROGRAM_ERROR_POSITION_ARB = 34379;
    public static final int GL_CURRENT_MATRIX_ARB = 34369;
    public static final int GL_TRANSPOSE_CURRENT_MATRIX_ARB = 34999;
    public static final int GL_CURRENT_MATRIX_STACK_DEPTH_ARB = 34368;
    public static final int GL_MAX_PROGRAM_MATRICES_ARB = 34351;
    public static final int GL_MAX_PROGRAM_MATRIX_STACK_DEPTH_ARB = 34350;
    public static final int GL_PROGRAM_ERROR_STRING_ARB = 34932;
    public static final int GL_MATRIX0_ARB = 35008;
    public static final int GL_MATRIX1_ARB = 35009;
    public static final int GL_MATRIX2_ARB = 35010;
    public static final int GL_MATRIX3_ARB = 35011;
    public static final int GL_MATRIX4_ARB = 35012;
    public static final int GL_MATRIX5_ARB = 35013;
    public static final int GL_MATRIX6_ARB = 35014;
    public static final int GL_MATRIX7_ARB = 35015;
    public static final int GL_MATRIX8_ARB = 35016;
    public static final int GL_MATRIX9_ARB = 35017;
    public static final int GL_MATRIX10_ARB = 35018;
    public static final int GL_MATRIX11_ARB = 35019;
    public static final int GL_MATRIX12_ARB = 35020;
    public static final int GL_MATRIX13_ARB = 35021;
    public static final int GL_MATRIX14_ARB = 35022;
    public static final int GL_MATRIX15_ARB = 35023;
    public static final int GL_MATRIX16_ARB = 35024;
    public static final int GL_MATRIX17_ARB = 35025;
    public static final int GL_MATRIX18_ARB = 35026;
    public static final int GL_MATRIX19_ARB = 35027;
    public static final int GL_MATRIX20_ARB = 35028;
    public static final int GL_MATRIX21_ARB = 35029;
    public static final int GL_MATRIX22_ARB = 35030;
    public static final int GL_MATRIX23_ARB = 35031;
    public static final int GL_MATRIX24_ARB = 35032;
    public static final int GL_MATRIX25_ARB = 35033;
    public static final int GL_MATRIX26_ARB = 35034;
    public static final int GL_MATRIX27_ARB = 35035;
    public static final int GL_MATRIX28_ARB = 35036;
    public static final int GL_MATRIX29_ARB = 35037;
    public static final int GL_MATRIX30_ARB = 35038;
    public static final int GL_MATRIX31_ARB = 35039;

    public static void glProgramStringARB(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramStringARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        ARBProgram.nglProgramStringARB(n, n2, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglProgramStringARB(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramStringARB(int n, int n2, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramStringARB;
        BufferChecks.checkFunctionAddress(l);
        ARBProgram.nglProgramStringARB(n, n2, charSequence.length(), APIUtil.getBuffer(contextCapabilities, charSequence), l);
    }

    public static void glBindProgramARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindProgramARB;
        BufferChecks.checkFunctionAddress(l);
        ARBProgram.nglBindProgramARB(n, n2, l);
    }

    static native void nglBindProgramARB(int var0, int var1, long var2);

    public static void glDeleteProgramsARB(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteProgramsARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBProgram.nglDeleteProgramsARB(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeleteProgramsARB(int var0, long var1, long var3);

    public static void glDeleteProgramsARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteProgramsARB;
        BufferChecks.checkFunctionAddress(l);
        ARBProgram.nglDeleteProgramsARB(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static void glGenProgramsARB(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenProgramsARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBProgram.nglGenProgramsARB(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenProgramsARB(int var0, long var1, long var3);

    public static int glGenProgramsARB() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenProgramsARB;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        ARBProgram.nglGenProgramsARB(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glProgramEnvParameter4fARB(int n, int n2, float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramEnvParameter4fARB;
        BufferChecks.checkFunctionAddress(l);
        ARBProgram.nglProgramEnvParameter4fARB(n, n2, f, f2, f3, f4, l);
    }

    static native void nglProgramEnvParameter4fARB(int var0, int var1, float var2, float var3, float var4, float var5, long var6);

    public static void glProgramEnvParameter4dARB(int n, int n2, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramEnvParameter4dARB;
        BufferChecks.checkFunctionAddress(l);
        ARBProgram.nglProgramEnvParameter4dARB(n, n2, d, d2, d3, d4, l);
    }

    static native void nglProgramEnvParameter4dARB(int var0, int var1, double var2, double var4, double var6, double var8, long var10);

    public static void glProgramEnvParameter4ARB(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramEnvParameter4fvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ARBProgram.nglProgramEnvParameter4fvARB(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramEnvParameter4fvARB(int var0, int var1, long var2, long var4);

    public static void glProgramEnvParameter4ARB(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramEnvParameter4dvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        ARBProgram.nglProgramEnvParameter4dvARB(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramEnvParameter4dvARB(int var0, int var1, long var2, long var4);

    public static void glProgramLocalParameter4fARB(int n, int n2, float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramLocalParameter4fARB;
        BufferChecks.checkFunctionAddress(l);
        ARBProgram.nglProgramLocalParameter4fARB(n, n2, f, f2, f3, f4, l);
    }

    static native void nglProgramLocalParameter4fARB(int var0, int var1, float var2, float var3, float var4, float var5, long var6);

    public static void glProgramLocalParameter4dARB(int n, int n2, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramLocalParameter4dARB;
        BufferChecks.checkFunctionAddress(l);
        ARBProgram.nglProgramLocalParameter4dARB(n, n2, d, d2, d3, d4, l);
    }

    static native void nglProgramLocalParameter4dARB(int var0, int var1, double var2, double var4, double var6, double var8, long var10);

    public static void glProgramLocalParameter4ARB(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramLocalParameter4fvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ARBProgram.nglProgramLocalParameter4fvARB(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramLocalParameter4fvARB(int var0, int var1, long var2, long var4);

    public static void glProgramLocalParameter4ARB(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramLocalParameter4dvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        ARBProgram.nglProgramLocalParameter4dvARB(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramLocalParameter4dvARB(int var0, int var1, long var2, long var4);

    public static void glGetProgramEnvParameterARB(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramEnvParameterfvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ARBProgram.nglGetProgramEnvParameterfvARB(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetProgramEnvParameterfvARB(int var0, int var1, long var2, long var4);

    public static void glGetProgramEnvParameterARB(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramEnvParameterdvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        ARBProgram.nglGetProgramEnvParameterdvARB(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetProgramEnvParameterdvARB(int var0, int var1, long var2, long var4);

    public static void glGetProgramLocalParameterARB(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramLocalParameterfvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ARBProgram.nglGetProgramLocalParameterfvARB(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetProgramLocalParameterfvARB(int var0, int var1, long var2, long var4);

    public static void glGetProgramLocalParameterARB(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramLocalParameterdvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        ARBProgram.nglGetProgramLocalParameterdvARB(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetProgramLocalParameterdvARB(int var0, int var1, long var2, long var4);

    public static void glGetProgramARB(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        ARBProgram.nglGetProgramivARB(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetProgramivARB(int var0, int var1, long var2, long var4);

    public static int glGetProgramARB(int n, int n2) {
        return ARBProgram.glGetProgramiARB(n, n2);
    }

    public static int glGetProgramiARB(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetProgramivARB;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        ARBProgram.nglGetProgramivARB(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetProgramStringARB(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramStringARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        ARBProgram.nglGetProgramStringARB(n, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetProgramStringARB(int var0, int var1, long var2, long var4);

    public static String glGetProgramStringARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramStringARB;
        BufferChecks.checkFunctionAddress(l);
        int n3 = ARBProgram.glGetProgramiARB(n, 34343);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        ARBProgram.nglGetProgramStringARB(n, n2, MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(n3);
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static boolean glIsProgramARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsProgramARB;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = ARBProgram.nglIsProgramARB(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsProgramARB(int var0, long var1);
}
