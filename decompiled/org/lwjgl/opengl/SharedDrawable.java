/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.opengl.ContextGL;
import org.lwjgl.opengl.Drawable;
import org.lwjgl.opengl.DrawableGL;
import org.lwjgl.opengl.DrawableLWJGL;

public final class SharedDrawable
extends DrawableGL {
    public SharedDrawable(Drawable drawable) {
        this.context = (ContextGL)((DrawableLWJGL)drawable).createSharedContext();
    }

    public final ContextGL createSharedContext() {
        throw new UnsupportedOperationException();
    }
}
