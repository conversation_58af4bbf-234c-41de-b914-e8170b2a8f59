/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.LongBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class GL33 {
    public static final int GL_SRC1_COLOR = 35065;
    public static final int GL_ONE_MINUS_SRC1_COLOR = 35066;
    public static final int GL_ONE_MINUS_SRC1_ALPHA = 35067;
    public static final int GL_MAX_DUAL_SOURCE_DRAW_BUFFERS = 35068;
    public static final int GL_ANY_SAMPLES_PASSED = 35887;
    public static final int GL_SAMPLER_BINDING = 35097;
    public static final int GL_RGB10_A2UI = 36975;
    public static final int GL_TEXTURE_SWIZZLE_R = 36418;
    public static final int GL_TEXTURE_SWIZZLE_G = 36419;
    public static final int GL_TEXTURE_SWIZZLE_B = 36420;
    public static final int GL_TEXTURE_SWIZZLE_A = 36421;
    public static final int GL_TEXTURE_SWIZZLE_RGBA = 36422;
    public static final int GL_TIME_ELAPSED = 35007;
    public static final int GL_TIMESTAMP = 36392;
    public static final int GL_VERTEX_ATTRIB_ARRAY_DIVISOR = 35070;
    public static final int GL_INT_2_10_10_10_REV = 36255;

    private GL33() {
    }

    public static void glBindFragDataLocationIndexed(int n, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindFragDataLocationIndexed;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        GL33.nglBindFragDataLocationIndexed(n, n2, n3, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglBindFragDataLocationIndexed(int var0, int var1, int var2, long var3, long var5);

    public static void glBindFragDataLocationIndexed(int n, int n2, int n3, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindFragDataLocationIndexed;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglBindFragDataLocationIndexed(n, n2, n3, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
    }

    public static int glGetFragDataIndex(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetFragDataIndex;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = GL33.nglGetFragDataIndex(n, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglGetFragDataIndex(int var0, long var1, long var3);

    public static int glGetFragDataIndex(int n, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetFragDataIndex;
        BufferChecks.checkFunctionAddress(l);
        n = GL33.nglGetFragDataIndex(n, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }

    public static void glGenSamplers(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenSamplers;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL33.nglGenSamplers(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenSamplers(int var0, long var1, long var3);

    public static int glGenSamplers() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenSamplers;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL33.nglGenSamplers(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glDeleteSamplers(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteSamplers;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL33.nglDeleteSamplers(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeleteSamplers(int var0, long var1, long var3);

    public static void glDeleteSamplers(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteSamplers;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglDeleteSamplers(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static boolean glIsSampler(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsSampler;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = GL33.nglIsSampler(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsSampler(int var0, long var1);

    public static void glBindSampler(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindSampler;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglBindSampler(n, n2, l);
    }

    static native void nglBindSampler(int var0, int var1, long var2);

    public static void glSamplerParameteri(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSamplerParameteri;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglSamplerParameteri(n, n2, n3, l);
    }

    static native void nglSamplerParameteri(int var0, int var1, int var2, long var3);

    public static void glSamplerParameterf(int n, int n2, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSamplerParameterf;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglSamplerParameterf(n, n2, f, l);
    }

    static native void nglSamplerParameterf(int var0, int var1, float var2, long var3);

    public static void glSamplerParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSamplerParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL33.nglSamplerParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglSamplerParameteriv(int var0, int var1, long var2, long var4);

    public static void glSamplerParameter(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSamplerParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL33.nglSamplerParameterfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglSamplerParameterfv(int var0, int var1, long var2, long var4);

    public static void glSamplerParameterI(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSamplerParameterIiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL33.nglSamplerParameterIiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglSamplerParameterIiv(int var0, int var1, long var2, long var4);

    public static void glSamplerParameterIu(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSamplerParameterIuiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL33.nglSamplerParameterIuiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglSamplerParameterIuiv(int var0, int var1, long var2, long var4);

    public static void glGetSamplerParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSamplerParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL33.nglGetSamplerParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetSamplerParameteriv(int var0, int var1, long var2, long var4);

    public static int glGetSamplerParameteri(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetSamplerParameteriv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL33.nglGetSamplerParameteriv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetSamplerParameter(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSamplerParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL33.nglGetSamplerParameterfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetSamplerParameterfv(int var0, int var1, long var2, long var4);

    public static float glGetSamplerParameterf(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetSamplerParameterfv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        GL33.nglGetSamplerParameterfv(n, n2, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetSamplerParameterI(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSamplerParameterIiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL33.nglGetSamplerParameterIiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetSamplerParameterIiv(int var0, int var1, long var2, long var4);

    public static int glGetSamplerParameterIi(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetSamplerParameterIiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL33.nglGetSamplerParameterIiv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetSamplerParameterIu(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSamplerParameterIuiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL33.nglGetSamplerParameterIuiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetSamplerParameterIuiv(int var0, int var1, long var2, long var4);

    public static int glGetSamplerParameterIui(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetSamplerParameterIuiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL33.nglGetSamplerParameterIuiv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glQueryCounter(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glQueryCounter;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglQueryCounter(n, n2, l);
    }

    static native void nglQueryCounter(int var0, int var1, long var2);

    public static void glGetQueryObject(int n, int n2, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetQueryObjecti64v;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 1);
        GL33.nglGetQueryObjecti64v(n, n2, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetQueryObjecti64v(int var0, int var1, long var2, long var4);

    public static long glGetQueryObject(int n, int n2) {
        return GL33.glGetQueryObjecti64(n, n2);
    }

    public static long glGetQueryObjecti64(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetQueryObjecti64v;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferLong((ContextCapabilities)object);
        GL33.nglGetQueryObjecti64v(n, n2, MemoryUtil.getAddress((LongBuffer)object), l);
        return ((LongBuffer)object).get(0);
    }

    public static void glGetQueryObjectu(int n, int n2, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetQueryObjectui64v;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 1);
        GL33.nglGetQueryObjectui64v(n, n2, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetQueryObjectui64v(int var0, int var1, long var2, long var4);

    public static long glGetQueryObjectu(int n, int n2) {
        return GL33.glGetQueryObjectui64(n, n2);
    }

    public static long glGetQueryObjectui64(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetQueryObjectui64v;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferLong((ContextCapabilities)object);
        GL33.nglGetQueryObjectui64v(n, n2, MemoryUtil.getAddress((LongBuffer)object), l);
        return ((LongBuffer)object).get(0);
    }

    public static void glVertexAttribDivisor(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribDivisor;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglVertexAttribDivisor(n, n2, l);
    }

    static native void nglVertexAttribDivisor(int var0, int var1, long var2);

    public static void glVertexP2ui(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexP2ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglVertexP2ui(n, n2, l);
    }

    static native void nglVertexP2ui(int var0, int var1, long var2);

    public static void glVertexP3ui(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexP3ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglVertexP3ui(n, n2, l);
    }

    static native void nglVertexP3ui(int var0, int var1, long var2);

    public static void glVertexP4ui(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexP4ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglVertexP4ui(n, n2, l);
    }

    static native void nglVertexP4ui(int var0, int var1, long var2);

    public static void glVertexP2u(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexP2uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 2);
        GL33.nglVertexP2uiv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexP2uiv(int var0, long var1, long var3);

    public static void glVertexP3u(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexP3uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 3);
        GL33.nglVertexP3uiv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexP3uiv(int var0, long var1, long var3);

    public static void glVertexP4u(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexP4uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL33.nglVertexP4uiv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexP4uiv(int var0, long var1, long var3);

    public static void glTexCoordP1ui(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoordP1ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglTexCoordP1ui(n, n2, l);
    }

    static native void nglTexCoordP1ui(int var0, int var1, long var2);

    public static void glTexCoordP2ui(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoordP2ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglTexCoordP2ui(n, n2, l);
    }

    static native void nglTexCoordP2ui(int var0, int var1, long var2);

    public static void glTexCoordP3ui(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoordP3ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglTexCoordP3ui(n, n2, l);
    }

    static native void nglTexCoordP3ui(int var0, int var1, long var2);

    public static void glTexCoordP4ui(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoordP4ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglTexCoordP4ui(n, n2, l);
    }

    static native void nglTexCoordP4ui(int var0, int var1, long var2);

    public static void glTexCoordP1u(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoordP1uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL33.nglTexCoordP1uiv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglTexCoordP1uiv(int var0, long var1, long var3);

    public static void glTexCoordP2u(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoordP2uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 2);
        GL33.nglTexCoordP2uiv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglTexCoordP2uiv(int var0, long var1, long var3);

    public static void glTexCoordP3u(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoordP3uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 3);
        GL33.nglTexCoordP3uiv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglTexCoordP3uiv(int var0, long var1, long var3);

    public static void glTexCoordP4u(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoordP4uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL33.nglTexCoordP4uiv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglTexCoordP4uiv(int var0, long var1, long var3);

    public static void glMultiTexCoordP1ui(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoordP1ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglMultiTexCoordP1ui(n, n2, n3, l);
    }

    static native void nglMultiTexCoordP1ui(int var0, int var1, int var2, long var3);

    public static void glMultiTexCoordP2ui(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoordP2ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglMultiTexCoordP2ui(n, n2, n3, l);
    }

    static native void nglMultiTexCoordP2ui(int var0, int var1, int var2, long var3);

    public static void glMultiTexCoordP3ui(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoordP3ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglMultiTexCoordP3ui(n, n2, n3, l);
    }

    static native void nglMultiTexCoordP3ui(int var0, int var1, int var2, long var3);

    public static void glMultiTexCoordP4ui(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoordP4ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglMultiTexCoordP4ui(n, n2, n3, l);
    }

    static native void nglMultiTexCoordP4ui(int var0, int var1, int var2, long var3);

    public static void glMultiTexCoordP1u(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoordP1uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL33.nglMultiTexCoordP1uiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglMultiTexCoordP1uiv(int var0, int var1, long var2, long var4);

    public static void glMultiTexCoordP2u(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoordP2uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 2);
        GL33.nglMultiTexCoordP2uiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglMultiTexCoordP2uiv(int var0, int var1, long var2, long var4);

    public static void glMultiTexCoordP3u(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoordP3uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 3);
        GL33.nglMultiTexCoordP3uiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglMultiTexCoordP3uiv(int var0, int var1, long var2, long var4);

    public static void glMultiTexCoordP4u(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoordP4uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL33.nglMultiTexCoordP4uiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglMultiTexCoordP4uiv(int var0, int var1, long var2, long var4);

    public static void glNormalP3ui(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormalP3ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglNormalP3ui(n, n2, l);
    }

    static native void nglNormalP3ui(int var0, int var1, long var2);

    public static void glNormalP3u(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormalP3uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 3);
        GL33.nglNormalP3uiv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglNormalP3uiv(int var0, long var1, long var3);

    public static void glColorP3ui(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorP3ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglColorP3ui(n, n2, l);
    }

    static native void nglColorP3ui(int var0, int var1, long var2);

    public static void glColorP4ui(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorP4ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglColorP4ui(n, n2, l);
    }

    static native void nglColorP4ui(int var0, int var1, long var2);

    public static void glColorP3u(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorP3uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 3);
        GL33.nglColorP3uiv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglColorP3uiv(int var0, long var1, long var3);

    public static void glColorP4u(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorP4uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL33.nglColorP4uiv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglColorP4uiv(int var0, long var1, long var3);

    public static void glSecondaryColorP3ui(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSecondaryColorP3ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglSecondaryColorP3ui(n, n2, l);
    }

    static native void nglSecondaryColorP3ui(int var0, int var1, long var2);

    public static void glSecondaryColorP3u(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSecondaryColorP3uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 3);
        GL33.nglSecondaryColorP3uiv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglSecondaryColorP3uiv(int var0, long var1, long var3);

    public static void glVertexAttribP1ui(int n, int n2, boolean bl, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribP1ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglVertexAttribP1ui(n, n2, bl, n3, l);
    }

    static native void nglVertexAttribP1ui(int var0, int var1, boolean var2, int var3, long var4);

    public static void glVertexAttribP2ui(int n, int n2, boolean bl, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribP2ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglVertexAttribP2ui(n, n2, bl, n3, l);
    }

    static native void nglVertexAttribP2ui(int var0, int var1, boolean var2, int var3, long var4);

    public static void glVertexAttribP3ui(int n, int n2, boolean bl, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribP3ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglVertexAttribP3ui(n, n2, bl, n3, l);
    }

    static native void nglVertexAttribP3ui(int var0, int var1, boolean var2, int var3, long var4);

    public static void glVertexAttribP4ui(int n, int n2, boolean bl, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribP4ui;
        BufferChecks.checkFunctionAddress(l);
        GL33.nglVertexAttribP4ui(n, n2, bl, n3, l);
    }

    static native void nglVertexAttribP4ui(int var0, int var1, boolean var2, int var3, long var4);

    public static void glVertexAttribP1u(int n, int n2, boolean bl, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribP1uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL33.nglVertexAttribP1uiv(n, n2, bl, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexAttribP1uiv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glVertexAttribP2u(int n, int n2, boolean bl, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribP2uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 2);
        GL33.nglVertexAttribP2uiv(n, n2, bl, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexAttribP2uiv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glVertexAttribP3u(int n, int n2, boolean bl, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribP3uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 3);
        GL33.nglVertexAttribP3uiv(n, n2, bl, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexAttribP3uiv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glVertexAttribP4u(int n, int n2, boolean bl, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribP4uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL33.nglVertexAttribP4uiv(n, n2, bl, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVertexAttribP4uiv(int var0, int var1, boolean var2, long var3, long var5);
}
