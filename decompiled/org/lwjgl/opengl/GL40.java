/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;

public final class GL40 {
    public static final int GL_DRAW_INDIRECT_BUFFER = 36671;
    public static final int GL_DRAW_INDIRECT_BUFFER_BINDING = 36675;
    public static final int GL_GEOMETRY_SHADER_INVOCATIONS = 34943;
    public static final int GL_MAX_GEOMETRY_SHADER_INVOCATIONS = 36442;
    public static final int GL_MIN_FRAGMENT_INTERPOLATION_OFFSET = 36443;
    public static final int GL_MAX_FRAGMENT_INTERPOLATION_OFFSET = 36444;
    public static final int GL_FRAGMENT_INTERPOLATION_OFFSET_BITS = 36445;
    public static final int GL_MAX_VERTEX_STREAMS = 36465;
    public static final int GL_DOUBLE_VEC2 = 36860;
    public static final int GL_DOUBLE_VEC3 = 36861;
    public static final int GL_DOUBLE_VEC4 = 36862;
    public static final int GL_DOUBLE_MAT2 = 36678;
    public static final int GL_DOUBLE_MAT3 = 36679;
    public static final int GL_DOUBLE_MAT4 = 36680;
    public static final int GL_DOUBLE_MAT2x3 = 36681;
    public static final int GL_DOUBLE_MAT2x4 = 36682;
    public static final int GL_DOUBLE_MAT3x2 = 36683;
    public static final int GL_DOUBLE_MAT3x4 = 36684;
    public static final int GL_DOUBLE_MAT4x2 = 36685;
    public static final int GL_DOUBLE_MAT4x3 = 36686;
    public static final int GL_SAMPLE_SHADING = 35894;
    public static final int GL_MIN_SAMPLE_SHADING_VALUE = 35895;
    public static final int GL_ACTIVE_SUBROUTINES = 36325;
    public static final int GL_ACTIVE_SUBROUTINE_UNIFORMS = 36326;
    public static final int GL_ACTIVE_SUBROUTINE_UNIFORM_LOCATIONS = 36423;
    public static final int GL_ACTIVE_SUBROUTINE_MAX_LENGTH = 36424;
    public static final int GL_ACTIVE_SUBROUTINE_UNIFORM_MAX_LENGTH = 36425;
    public static final int GL_MAX_SUBROUTINES = 36327;
    public static final int GL_MAX_SUBROUTINE_UNIFORM_LOCATIONS = 36328;
    public static final int GL_NUM_COMPATIBLE_SUBROUTINES = 36426;
    public static final int GL_COMPATIBLE_SUBROUTINES = 36427;
    public static final int GL_PATCHES = 14;
    public static final int GL_PATCH_VERTICES = 36466;
    public static final int GL_PATCH_DEFAULT_INNER_LEVEL = 36467;
    public static final int GL_PATCH_DEFAULT_OUTER_LEVEL = 36468;
    public static final int GL_TESS_CONTROL_OUTPUT_VERTICES = 36469;
    public static final int GL_TESS_GEN_MODE = 36470;
    public static final int GL_TESS_GEN_SPACING = 36471;
    public static final int GL_TESS_GEN_VERTEX_ORDER = 36472;
    public static final int GL_TESS_GEN_POINT_MODE = 36473;
    public static final int GL_ISOLINES = 36474;
    public static final int GL_FRACTIONAL_ODD = 36475;
    public static final int GL_FRACTIONAL_EVEN = 36476;
    public static final int GL_MAX_PATCH_VERTICES = 36477;
    public static final int GL_MAX_TESS_GEN_LEVEL = 36478;
    public static final int GL_MAX_TESS_CONTROL_UNIFORM_COMPONENTS = 36479;
    public static final int GL_MAX_TESS_EVALUATION_UNIFORM_COMPONENTS = 36480;
    public static final int GL_MAX_TESS_CONTROL_TEXTURE_IMAGE_UNITS = 36481;
    public static final int GL_MAX_TESS_EVALUATION_TEXTURE_IMAGE_UNITS = 36482;
    public static final int GL_MAX_TESS_CONTROL_OUTPUT_COMPONENTS = 36483;
    public static final int GL_MAX_TESS_PATCH_COMPONENTS = 36484;
    public static final int GL_MAX_TESS_CONTROL_TOTAL_OUTPUT_COMPONENTS = 36485;
    public static final int GL_MAX_TESS_EVALUATION_OUTPUT_COMPONENTS = 36486;
    public static final int GL_MAX_TESS_CONTROL_UNIFORM_BLOCKS = 36489;
    public static final int GL_MAX_TESS_EVALUATION_UNIFORM_BLOCKS = 36490;
    public static final int GL_MAX_TESS_CONTROL_INPUT_COMPONENTS = 34924;
    public static final int GL_MAX_TESS_EVALUATION_INPUT_COMPONENTS = 34925;
    public static final int GL_MAX_COMBINED_TESS_CONTROL_UNIFORM_COMPONENTS = 36382;
    public static final int GL_MAX_COMBINED_TESS_EVALUATION_UNIFORM_COMPONENTS = 36383;
    public static final int GL_UNIFORM_BLOCK_REFERENCED_BY_TESS_CONTROL_SHADER = 34032;
    public static final int GL_UNIFORM_BLOCK_REFERENCED_BY_TESS_EVALUATION_SHADER = 34033;
    public static final int GL_TESS_EVALUATION_SHADER = 36487;
    public static final int GL_TESS_CONTROL_SHADER = 36488;
    public static final int GL_TEXTURE_CUBE_MAP_ARRAY = 36873;
    public static final int GL_TEXTURE_BINDING_CUBE_MAP_ARRAY = 36874;
    public static final int GL_PROXY_TEXTURE_CUBE_MAP_ARRAY = 36875;
    public static final int GL_SAMPLER_CUBE_MAP_ARRAY = 36876;
    public static final int GL_SAMPLER_CUBE_MAP_ARRAY_SHADOW = 36877;
    public static final int GL_INT_SAMPLER_CUBE_MAP_ARRAY = 36878;
    public static final int GL_UNSIGNED_INT_SAMPLER_CUBE_MAP_ARRAY = 36879;
    public static final int GL_MIN_PROGRAM_TEXTURE_GATHER_OFFSET_ARB = 36446;
    public static final int GL_MAX_PROGRAM_TEXTURE_GATHER_OFFSET_ARB = 36447;
    public static final int GL_MAX_PROGRAM_TEXTURE_GATHER_COMPONENTS_ARB = 36767;
    public static final int GL_TRANSFORM_FEEDBACK = 36386;
    public static final int GL_TRANSFORM_FEEDBACK_PAUSED = 36387;
    public static final int GL_TRANSFORM_FEEDBACK_ACTIVE = 36388;
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_PAUSED = 36387;
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_ACTIVE = 36388;
    public static final int GL_TRANSFORM_FEEDBACK_BINDING = 36389;
    public static final int GL_MAX_TRANSFORM_FEEDBACK_BUFFERS = 36464;

    private GL40() {
    }

    public static void glBlendEquationi(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendEquationi;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglBlendEquationi(n, n2, l);
    }

    static native void nglBlendEquationi(int var0, int var1, long var2);

    public static void glBlendEquationSeparatei(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendEquationSeparatei;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglBlendEquationSeparatei(n, n2, n3, l);
    }

    static native void nglBlendEquationSeparatei(int var0, int var1, int var2, long var3);

    public static void glBlendFunci(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendFunci;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglBlendFunci(n, n2, n3, l);
    }

    static native void nglBlendFunci(int var0, int var1, int var2, long var3);

    public static void glBlendFuncSeparatei(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendFuncSeparatei;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglBlendFuncSeparatei(n, n2, n3, n4, n5, l);
    }

    static native void nglBlendFuncSeparatei(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glDrawArraysIndirect(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawArraysIndirect;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureIndirectBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, 16);
        GL40.nglDrawArraysIndirect(n, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglDrawArraysIndirect(int var0, long var1, long var3);

    public static void glDrawArraysIndirect(int n, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glDrawArraysIndirect;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureIndirectBOenabled(contextCapabilities);
        GL40.nglDrawArraysIndirectBO(n, l, l2);
    }

    static native void nglDrawArraysIndirectBO(int var0, long var1, long var3);

    public static void glDrawArraysIndirect(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawArraysIndirect;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureIndirectBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL40.nglDrawArraysIndirect(n, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glDrawElementsIndirect(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsIndirect;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureIndirectBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, 20);
        GL40.nglDrawElementsIndirect(n, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglDrawElementsIndirect(int var0, int var1, long var2, long var4);

    public static void glDrawElementsIndirect(int n, int n2, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glDrawElementsIndirect;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureIndirectBOenabled(contextCapabilities);
        GL40.nglDrawElementsIndirectBO(n, n2, l, l2);
    }

    static native void nglDrawElementsIndirectBO(int var0, int var1, long var2, long var4);

    public static void glDrawElementsIndirect(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsIndirect;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureIndirectBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(intBuffer, 5);
        GL40.nglDrawElementsIndirect(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glUniform1d(int n, double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform1d;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglUniform1d(n, d, l);
    }

    static native void nglUniform1d(int var0, double var1, long var3);

    public static void glUniform2d(int n, double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform2d;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglUniform2d(n, d, d2, l);
    }

    static native void nglUniform2d(int var0, double var1, double var3, long var5);

    public static void glUniform3d(int n, double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform3d;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglUniform3d(n, d, d2, d3, l);
    }

    static native void nglUniform3d(int var0, double var1, double var3, double var5, long var7);

    public static void glUniform4d(int n, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform4d;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglUniform4d(n, d, d2, d3, d4, l);
    }

    static native void nglUniform4d(int var0, double var1, double var3, double var5, double var7, long var9);

    public static void glUniform1(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform1dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL40.nglUniform1dv(n, doubleBuffer.remaining(), MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglUniform1dv(int var0, int var1, long var2, long var4);

    public static void glUniform2(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform2dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL40.nglUniform2dv(n, doubleBuffer.remaining() >> 1, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglUniform2dv(int var0, int var1, long var2, long var4);

    public static void glUniform3(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform3dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL40.nglUniform3dv(n, doubleBuffer.remaining() / 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglUniform3dv(int var0, int var1, long var2, long var4);

    public static void glUniform4(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform4dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL40.nglUniform4dv(n, doubleBuffer.remaining() >> 2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglUniform4dv(int var0, int var1, long var2, long var4);

    public static void glUniformMatrix2(int n, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix2dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL40.nglUniformMatrix2dv(n, doubleBuffer.remaining() >> 2, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglUniformMatrix2dv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glUniformMatrix3(int n, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix3dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL40.nglUniformMatrix3dv(n, doubleBuffer.remaining() / 9, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglUniformMatrix3dv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glUniformMatrix4(int n, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix4dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL40.nglUniformMatrix4dv(n, doubleBuffer.remaining() >> 4, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglUniformMatrix4dv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glUniformMatrix2x3(int n, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix2x3dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL40.nglUniformMatrix2x3dv(n, doubleBuffer.remaining() / 6, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglUniformMatrix2x3dv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glUniformMatrix2x4(int n, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix2x4dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL40.nglUniformMatrix2x4dv(n, doubleBuffer.remaining() >> 3, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglUniformMatrix2x4dv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glUniformMatrix3x2(int n, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix3x2dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL40.nglUniformMatrix3x2dv(n, doubleBuffer.remaining() / 6, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglUniformMatrix3x2dv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glUniformMatrix3x4(int n, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix3x4dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL40.nglUniformMatrix3x4dv(n, doubleBuffer.remaining() / 12, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglUniformMatrix3x4dv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glUniformMatrix4x2(int n, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix4x2dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL40.nglUniformMatrix4x2dv(n, doubleBuffer.remaining() >> 3, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglUniformMatrix4x2dv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glUniformMatrix4x3(int n, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix4x3dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL40.nglUniformMatrix4x3dv(n, doubleBuffer.remaining() / 12, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglUniformMatrix4x3dv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glGetUniform(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformdv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL40.nglGetUniformdv(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetUniformdv(int var0, int var1, long var2, long var4);

    public static void glMinSampleShading(float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMinSampleShading;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglMinSampleShading(f, l);
    }

    static native void nglMinSampleShading(float var0, long var1);

    public static int glGetSubroutineUniformLocation(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSubroutineUniformLocation;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = GL40.nglGetSubroutineUniformLocation(n, n2, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglGetSubroutineUniformLocation(int var0, int var1, long var2, long var4);

    public static int glGetSubroutineUniformLocation(int n, int n2, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSubroutineUniformLocation;
        BufferChecks.checkFunctionAddress(l);
        n = GL40.nglGetSubroutineUniformLocation(n, n2, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }

    public static int glGetSubroutineIndex(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSubroutineIndex;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = GL40.nglGetSubroutineIndex(n, n2, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglGetSubroutineIndex(int var0, int var1, long var2, long var4);

    public static int glGetSubroutineIndex(int n, int n2, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSubroutineIndex;
        BufferChecks.checkFunctionAddress(l);
        n = GL40.nglGetSubroutineIndex(n, n2, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }

    public static void glGetActiveSubroutineUniform(int n, int n2, int n3, int n4, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveSubroutineUniformiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL40.nglGetActiveSubroutineUniformiv(n, n2, n3, n4, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetActiveSubroutineUniformiv(int var0, int var1, int var2, int var3, long var4, long var6);

    public static int glGetActiveSubroutineUniform(int n, int n2, int n3, int n4) {
        return GL40.glGetActiveSubroutineUniformi(n, n2, n3, n4);
    }

    public static int glGetActiveSubroutineUniformi(int n, int n2, int n3, int n4) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetActiveSubroutineUniformiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL40.nglGetActiveSubroutineUniformiv(n, n2, n3, n4, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetActiveSubroutineUniformName(int n, int n2, int n3, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveSubroutineUniformName;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(byteBuffer);
        GL40.nglGetActiveSubroutineUniformName(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetActiveSubroutineUniformName(int var0, int var1, int var2, int var3, long var4, long var6, long var8);

    public static String glGetActiveSubroutineUniformName(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveSubroutineUniformName;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n4);
        GL40.nglGetActiveSubroutineUniformName(n, n2, n3, n4, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static void glGetActiveSubroutineName(int n, int n2, int n3, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveSubroutineName;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(byteBuffer);
        GL40.nglGetActiveSubroutineName(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetActiveSubroutineName(int var0, int var1, int var2, int var3, long var4, long var6, long var8);

    public static String glGetActiveSubroutineName(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveSubroutineName;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n4);
        GL40.nglGetActiveSubroutineName(n, n2, n3, n4, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static void glUniformSubroutinesu(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformSubroutinesuiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL40.nglUniformSubroutinesuiv(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglUniformSubroutinesuiv(int var0, int var1, long var2, long var4);

    public static void glGetUniformSubroutineu(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformSubroutineuiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL40.nglGetUniformSubroutineuiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetUniformSubroutineuiv(int var0, int var1, long var2, long var4);

    public static int glGetUniformSubroutineu(int n, int n2) {
        return GL40.glGetUniformSubroutineui(n, n2);
    }

    public static int glGetUniformSubroutineui(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetUniformSubroutineuiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL40.nglGetUniformSubroutineuiv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetProgramStage(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramStageiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL40.nglGetProgramStageiv(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetProgramStageiv(int var0, int var1, int var2, long var3, long var5);

    public static int glGetProgramStage(int n, int n2, int n3) {
        return GL40.glGetProgramStagei(n, n2, n3);
    }

    public static int glGetProgramStagei(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetProgramStageiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL40.nglGetProgramStageiv(n, n2, n3, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glPatchParameteri(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPatchParameteri;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglPatchParameteri(n, n2, l);
    }

    static native void nglPatchParameteri(int var0, int var1, long var2);

    public static void glPatchParameter(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPatchParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL40.nglPatchParameterfv(n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglPatchParameterfv(int var0, long var1, long var3);

    public static void glBindTransformFeedback(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindTransformFeedback;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglBindTransformFeedback(n, n2, l);
    }

    static native void nglBindTransformFeedback(int var0, int var1, long var2);

    public static void glDeleteTransformFeedbacks(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteTransformFeedbacks;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL40.nglDeleteTransformFeedbacks(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeleteTransformFeedbacks(int var0, long var1, long var3);

    public static void glDeleteTransformFeedbacks(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteTransformFeedbacks;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglDeleteTransformFeedbacks(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static void glGenTransformFeedbacks(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenTransformFeedbacks;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL40.nglGenTransformFeedbacks(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenTransformFeedbacks(int var0, long var1, long var3);

    public static int glGenTransformFeedbacks() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenTransformFeedbacks;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL40.nglGenTransformFeedbacks(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static boolean glIsTransformFeedback(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsTransformFeedback;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = GL40.nglIsTransformFeedback(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsTransformFeedback(int var0, long var1);

    public static void glPauseTransformFeedback() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPauseTransformFeedback;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglPauseTransformFeedback(l);
    }

    static native void nglPauseTransformFeedback(long var0);

    public static void glResumeTransformFeedback() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glResumeTransformFeedback;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglResumeTransformFeedback(l);
    }

    static native void nglResumeTransformFeedback(long var0);

    public static void glDrawTransformFeedback(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawTransformFeedback;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglDrawTransformFeedback(n, n2, l);
    }

    static native void nglDrawTransformFeedback(int var0, int var1, long var2);

    public static void glDrawTransformFeedbackStream(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawTransformFeedbackStream;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglDrawTransformFeedbackStream(n, n2, n3, l);
    }

    static native void nglDrawTransformFeedbackStream(int var0, int var1, int var2, long var3);

    public static void glBeginQueryIndexed(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBeginQueryIndexed;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglBeginQueryIndexed(n, n2, n3, l);
    }

    static native void nglBeginQueryIndexed(int var0, int var1, int var2, long var3);

    public static void glEndQueryIndexed(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEndQueryIndexed;
        BufferChecks.checkFunctionAddress(l);
        GL40.nglEndQueryIndexed(n, n2, l);
    }

    static native void nglEndQueryIndexed(int var0, int var1, long var2);

    public static void glGetQueryIndexed(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetQueryIndexediv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL40.nglGetQueryIndexediv(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetQueryIndexediv(int var0, int var1, int var2, long var3, long var5);

    public static int glGetQueryIndexed(int n, int n2, int n3) {
        return GL40.glGetQueryIndexedi(n, n2, n3);
    }

    public static int glGetQueryIndexedi(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetQueryIndexediv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL40.nglGetQueryIndexediv(n, n2, n3, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }
}
