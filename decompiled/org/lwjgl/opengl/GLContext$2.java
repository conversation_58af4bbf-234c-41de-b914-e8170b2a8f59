/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.lang.reflect.Method;
import java.security.PrivilegedExceptionAction;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
static final class GLContext.2
implements PrivilegedExceptionAction<Object> {
    final /* synthetic */ Class val$extension_class;

    GLContext.2(Class clazz) {
        this.val$extension_class = clazz;
    }

    @Override
    public final Object run() {
        Method method = this.val$extension_class.getDeclaredMethod("initNativeStubs", new Class[0]);
        method.invoke(null, new Object[0]);
        return null;
    }
}
