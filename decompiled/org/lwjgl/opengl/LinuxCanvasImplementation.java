/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Canvas;
import java.awt.GraphicsConfiguration;
import java.awt.GraphicsDevice;
import java.lang.reflect.Method;
import java.security.AccessController;
import java.security.PrivilegedExceptionAction;
import org.lwjgl.LWJGLException;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.opengl.AWTCanvasImplementation;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.LinuxAWTGLCanvasPeerInfo;
import org.lwjgl.opengl.PeerInfo;
import org.lwjgl.opengl.PixelFormat;

final class LinuxCanvasImplementation
implements AWTCanvasImplementation {
    LinuxCanvasImplementation() {
    }

    static int getScreenFromDevice(GraphicsDevice object) {
        try {
            Method method = AccessController.doPrivileged(new PrivilegedExceptionAction<Method>((GraphicsDevice)object){
                final /* synthetic */ GraphicsDevice val$device;
                {
                    this.val$device = graphicsDevice;
                }

                @Override
                public final Method run() {
                    return this.val$device.getClass().getMethod("getScreen", new Class[0]);
                }
            });
            object = (Integer)method.invoke(object, new Object[0]);
            return (Integer)object;
        }
        catch (Exception exception) {
            throw new LWJGLException(exception);
        }
    }

    private static int getVisualIDFromConfiguration(GraphicsConfiguration object) {
        try {
            Method method = AccessController.doPrivileged(new PrivilegedExceptionAction<Method>((GraphicsConfiguration)object){
                final /* synthetic */ GraphicsConfiguration val$configuration;
                {
                    this.val$configuration = graphicsConfiguration;
                }

                @Override
                public final Method run() {
                    return this.val$configuration.getClass().getMethod("getVisual", new Class[0]);
                }
            });
            object = (Integer)method.invoke(object, new Object[0]);
            return (Integer)object;
        }
        catch (Exception exception) {
            throw new LWJGLException(exception);
        }
    }

    public final PeerInfo createPeerInfo(Canvas canvas, PixelFormat pixelFormat, ContextAttribs contextAttribs) {
        return new LinuxAWTGLCanvasPeerInfo(canvas);
    }

    public final GraphicsConfiguration findConfiguration(GraphicsDevice graphicsConfigurationArray, PixelFormat pixelFormat) {
        try {
            int n = LinuxCanvasImplementation.getScreenFromDevice((GraphicsDevice)graphicsConfigurationArray);
            int n2 = LinuxCanvasImplementation.findVisualIDFromFormat(n, pixelFormat);
            GraphicsConfiguration[] graphicsConfigurationArray2 = graphicsConfigurationArray.getConfigurations();
            graphicsConfigurationArray = graphicsConfigurationArray2;
            graphicsConfigurationArray = graphicsConfigurationArray2;
            n = graphicsConfigurationArray2.length;
            for (int i = 0; i < n; ++i) {
                GraphicsConfiguration graphicsConfiguration = graphicsConfigurationArray[i];
                int n3 = LinuxCanvasImplementation.getVisualIDFromConfiguration(graphicsConfiguration);
                if (n3 != n2) continue;
                return graphicsConfiguration;
            }
        }
        catch (LWJGLException lWJGLException) {
            LWJGLUtil.log("Got exception while trying to determine configuration: " + lWJGLException);
        }
        return null;
    }

    /*
     * Exception decompiling
     */
    private static int findVisualIDFromFormat(int var0, PixelFormat var1_4) {
        /*
         * This method has failed to decompile.  When submitting a bug report, please provide this stack trace, and (if you hold appropriate legal rights) the relevant class file.
         * 
         * org.benf.cfr.reader.util.ConfusedCFRException: Started 2 blocks at once
         *     at org.benf.cfr.reader.bytecode.analysis.opgraph.Op04StructuredStatement.getStartingBlocks(Op04StructuredStatement.java:412)
         *     at org.benf.cfr.reader.bytecode.analysis.opgraph.Op04StructuredStatement.buildNestedBlocks(Op04StructuredStatement.java:487)
         *     at org.benf.cfr.reader.bytecode.analysis.opgraph.Op03SimpleStatement.createInitialStructuredBlock(Op03SimpleStatement.java:736)
         *     at org.benf.cfr.reader.bytecode.CodeAnalyser.getAnalysisInner(CodeAnalyser.java:850)
         *     at org.benf.cfr.reader.bytecode.CodeAnalyser.getAnalysisOrWrapFail(CodeAnalyser.java:278)
         *     at org.benf.cfr.reader.bytecode.CodeAnalyser.getAnalysis(CodeAnalyser.java:201)
         *     at org.benf.cfr.reader.entities.attributes.AttributeCode.analyse(AttributeCode.java:94)
         *     at org.benf.cfr.reader.entities.Method.analyse(Method.java:531)
         *     at org.benf.cfr.reader.entities.ClassFile.analyseMid(ClassFile.java:1055)
         *     at org.benf.cfr.reader.entities.ClassFile.analyseTop(ClassFile.java:942)
         *     at org.benf.cfr.reader.Driver.doClass(Driver.java:84)
         *     at org.benf.cfr.reader.CfrDriverImpl.analyse(CfrDriverImpl.java:78)
         *     at org.benf.cfr.reader.Main.main(Main.java:54)
         */
        throw new IllegalStateException("Decompilation failed");
    }

    private static native int nFindVisualIDFromFormat(long var0, int var2, PixelFormat var3);
}
