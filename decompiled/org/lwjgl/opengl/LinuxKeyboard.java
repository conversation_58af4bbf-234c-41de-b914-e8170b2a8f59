/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import org.lwjgl.BufferUtils;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.opengl.EventQueue;
import org.lwjgl.opengl.LinuxEvent;
import org.lwjgl.opengl.LinuxKeycodes;

final class LinuxKeyboard {
    private static final int LockMapIndex = 1;
    private static final long NoSymbol = 0L;
    private static final long ShiftMask = 1L;
    private static final long LockMask = 2L;
    private static final int XLookupChars = 2;
    private static final int XLookupBoth = 4;
    private static final int KEYBOARD_BUFFER_SIZE = 50;
    private final long xim;
    private final long xic;
    private final int numlock_mask;
    private final int modeswitch_mask;
    private final int caps_lock_mask;
    private final int shift_lock_mask;
    private final ByteBuffer compose_status;
    private final byte[] key_down_buffer = new byte[256];
    private final EventQueue event_queue = new EventQueue(18);
    private final ByteBuffer tmp_event = ByteBuffer.allocate(18);
    private final int[] temp_translation_buffer = new int[50];
    private final ByteBuffer native_translation_buffer = BufferUtils.createByteBuffer(50);
    private final CharsetDecoder utf8_decoder = Charset.forName("UTF-8").newDecoder();
    private final CharBuffer char_buffer = CharBuffer.allocate(50);
    private boolean has_deferred_event;
    private int deferred_keycode;
    private int deferred_event_keycode;
    private long deferred_nanos;
    private byte deferred_key_state;

    LinuxKeyboard(long l, long l2) {
        long l3 = LinuxKeyboard.getModifierMapping(l);
        int n = 0;
        int n2 = 0;
        int n3 = 0;
        int n4 = 0;
        if (l3 != 0L) {
            int n5 = LinuxKeyboard.getMaxKeyPerMod(l3);
            for (int i = 0; i < 8; ++i) {
                block7: for (int j = 0; j < n5; ++j) {
                    int n6 = LinuxKeyboard.lookupModifierMap(l3, i * n5 + j);
                    n6 = (int)LinuxKeyboard.keycodeToKeySym(l, n6);
                    int n7 = 1 << i;
                    switch (n6) {
                        case 65407: {
                            n |= n7;
                            continue block7;
                        }
                        case 65406: {
                            n2 |= n7;
                            continue block7;
                        }
                        case 65509: {
                            if (i != 1) continue block7;
                            n3 = n7;
                            n4 = 0;
                            continue block7;
                        }
                        case 65510: {
                            if (i != 1 || n3 != 0) continue block7;
                            n4 = n7;
                        }
                    }
                }
            }
            LinuxKeyboard.freeModifierMapping(l3);
        }
        this.numlock_mask = n;
        this.modeswitch_mask = n2;
        this.caps_lock_mask = n3;
        this.shift_lock_mask = n4;
        LinuxKeyboard.setDetectableKeyRepeat(l, true);
        this.xim = LinuxKeyboard.openIM(l);
        if (this.xim != 0L) {
            this.xic = LinuxKeyboard.createIC(this.xim, l2);
            if (this.xic != 0L) {
                LinuxKeyboard.setupIMEventMask(l, l2, this.xic);
            } else {
                this.destroy(l);
            }
        } else {
            this.xic = 0L;
        }
        this.compose_status = LinuxKeyboard.allocateComposeStatus();
    }

    private static native long getModifierMapping(long var0);

    private static native void freeModifierMapping(long var0);

    private static native int getMaxKeyPerMod(long var0);

    private static native int lookupModifierMap(long var0, int var2);

    private static native long keycodeToKeySym(long var0, int var2);

    private static native long openIM(long var0);

    private static native long createIC(long var0, long var2);

    private static native void setupIMEventMask(long var0, long var2, long var4);

    private static native ByteBuffer allocateComposeStatus();

    private static void setDetectableKeyRepeat(long l, boolean bl) {
        boolean bl2 = LinuxKeyboard.nSetDetectableKeyRepeat(l, bl);
        if (!bl2) {
            LWJGLUtil.log("Failed to set detectable key repeat to " + bl);
        }
    }

    private static native boolean nSetDetectableKeyRepeat(long var0, boolean var2);

    public final void destroy(long l) {
        if (this.xic != 0L) {
            LinuxKeyboard.destroyIC(this.xic);
        }
        if (this.xim != 0L) {
            LinuxKeyboard.closeIM(this.xim);
        }
        LinuxKeyboard.setDetectableKeyRepeat(l, false);
    }

    private static native void destroyIC(long var0);

    private static native void closeIM(long var0);

    public final void read(ByteBuffer byteBuffer) {
        this.flushDeferredEvent();
        this.event_queue.copyEvents(byteBuffer);
    }

    public final void poll(ByteBuffer byteBuffer) {
        this.flushDeferredEvent();
        int n = byteBuffer.position();
        byteBuffer.put(this.key_down_buffer);
        byteBuffer.position(n);
    }

    private void putKeyboardEvent(int n, byte by, int n2, long l, boolean bl) {
        this.tmp_event.clear();
        this.tmp_event.putInt(n).put(by).putInt(n2).putLong(l).put(bl ? (byte)1 : 0);
        this.tmp_event.flip();
        this.event_queue.putEvent(this.tmp_event);
    }

    private int lookupStringISO88591(long l, int[] nArray) {
        int n = LinuxKeyboard.lookupString(l, this.native_translation_buffer, this.compose_status);
        for (int i = 0; i < n; ++i) {
            nArray[i] = this.native_translation_buffer.get(i) & 0xFF;
        }
        return n;
    }

    private static native int lookupString(long var0, ByteBuffer var2, ByteBuffer var3);

    private int lookupStringUnicode(long l, int[] nArray) {
        int n = LinuxKeyboard.utf8LookupString(this.xic, l, this.native_translation_buffer, this.native_translation_buffer.position(), this.native_translation_buffer.remaining());
        if (n != 2 && n != 4) {
            return 0;
        }
        this.native_translation_buffer.flip();
        this.utf8_decoder.decode(this.native_translation_buffer, this.char_buffer, true);
        this.native_translation_buffer.compact();
        this.char_buffer.flip();
        n = 0;
        while (this.char_buffer.hasRemaining() && n < nArray.length) {
            nArray[n++] = this.char_buffer.get();
        }
        this.char_buffer.compact();
        return n;
    }

    private static native int utf8LookupString(long var0, long var2, ByteBuffer var4, int var5, int var6);

    private int lookupString(long l, int[] nArray) {
        if (this.xic != 0L) {
            return this.lookupStringUnicode(l, nArray);
        }
        return this.lookupStringISO88591(l, nArray);
    }

    private void translateEvent(long l, int n, byte by, long l2, boolean bl) {
        int n2 = this.lookupString(l, this.temp_translation_buffer);
        if (n2 > 0) {
            int n3 = this.temp_translation_buffer[0];
            this.putKeyboardEvent(n, by, n3, l2, bl);
            for (int i = 1; i < n2; ++i) {
                n3 = this.temp_translation_buffer[i];
                this.putKeyboardEvent(0, (byte)0, n3, l2, bl);
            }
        } else {
            this.putKeyboardEvent(n, by, 0, l2, bl);
        }
    }

    private static boolean isKeypadKeysym(long l) {
        return 65408L <= l && l <= 65469L || 0x11000000L <= l && l <= 0x1100FFFFL;
    }

    private static boolean isNoSymbolOrVendorSpecific(long l) {
        return l == 0L || (l & 0x10000000L) != 0L;
    }

    private static long getKeySym(long l, int n, int n2) {
        long l2 = LinuxKeyboard.lookupKeysym(l, (n << 1) + n2);
        if (LinuxKeyboard.isNoSymbolOrVendorSpecific(l2) && n2 == 1) {
            l2 = LinuxKeyboard.lookupKeysym(l, n << 1);
        }
        if (LinuxKeyboard.isNoSymbolOrVendorSpecific(l2) && n == 1) {
            l2 = LinuxKeyboard.getKeySym(l, 0, n2);
        }
        return l2;
    }

    private static native long lookupKeysym(long var0, int var2);

    private static native long toUpper(long var0);

    private long mapEventToKeySym(long l, int n) {
        long l2;
        int n2 = (n & this.modeswitch_mask) != 0 ? 1 : 0;
        if ((n & this.numlock_mask) != 0 && LinuxKeyboard.isKeypadKeysym(l2 = LinuxKeyboard.getKeySym(l, n2, 1))) {
            if (((long)n & (1L | (long)this.shift_lock_mask)) != 0L) {
                return LinuxKeyboard.getKeySym(l, n2, 0);
            }
            return l2;
        }
        if (((long)n & 3L) == 0L) {
            return LinuxKeyboard.getKeySym(l, n2, 0);
        }
        if (((long)n & 1L) == 0L) {
            l2 = LinuxKeyboard.getKeySym(l, n2, 0);
            if ((n & this.caps_lock_mask) != 0) {
                l2 = LinuxKeyboard.toUpper(l2);
            }
            return l2;
        }
        l2 = LinuxKeyboard.getKeySym(l, n2, 1);
        if ((n & this.caps_lock_mask) != 0) {
            l2 = LinuxKeyboard.toUpper(l2);
        }
        return l2;
    }

    private int getKeycode(long l, int n) {
        long l2 = this.mapEventToKeySym(l, n);
        if ((n = LinuxKeycodes.mapKeySymToLWJGLKeyCode(l2)) == 0) {
            l2 = LinuxKeyboard.lookupKeysym(l, 0);
            n = LinuxKeycodes.mapKeySymToLWJGLKeyCode(l2);
        }
        return n;
    }

    private static byte getKeyState(int n) {
        switch (n) {
            case 2: {
                return 1;
            }
            case 3: {
                return 0;
            }
        }
        throw new IllegalArgumentException("Unknown event_type: " + n);
    }

    final void releaseAll() {
        for (int i = 0; i < this.key_down_buffer.length; ++i) {
            if (this.key_down_buffer[i] == 0) continue;
            this.key_down_buffer[i] = 0;
            this.putKeyboardEvent(i, (byte)0, 0, 0L, false);
        }
    }

    private void handleKeyEvent(long l, long l2, int n, int n2, int n3) {
        n3 = this.getKeycode(l, n3);
        byte by = LinuxKeyboard.getKeyState(n);
        boolean bl = by == this.key_down_buffer[n3];
        if (bl && n == 3) {
            return;
        }
        this.key_down_buffer[n3] = by;
        long l3 = l2 * 1000000L;
        if (n == 2) {
            if (this.has_deferred_event) {
                if (l3 == this.deferred_nanos && n2 == this.deferred_event_keycode) {
                    this.has_deferred_event = false;
                    bl = true;
                } else {
                    this.flushDeferredEvent();
                }
            }
            this.translateEvent(l, n3, by, l3, bl);
            return;
        }
        this.flushDeferredEvent();
        this.has_deferred_event = true;
        this.deferred_keycode = n3;
        this.deferred_event_keycode = n2;
        this.deferred_nanos = l3;
        this.deferred_key_state = by;
    }

    private void flushDeferredEvent() {
        if (this.has_deferred_event) {
            LinuxKeyboard linuxKeyboard = this;
            linuxKeyboard.putKeyboardEvent(linuxKeyboard.deferred_keycode, this.deferred_key_state, 0, this.deferred_nanos, false);
            this.has_deferred_event = false;
        }
    }

    public final boolean filterEvent(LinuxEvent linuxEvent) {
        switch (linuxEvent.getType()) {
            case 2: 
            case 3: {
                this.handleKeyEvent(linuxEvent.getKeyAddress(), linuxEvent.getKeyTime(), linuxEvent.getKeyType(), linuxEvent.getKeyKeyCode(), linuxEvent.getKeyState());
                return true;
            }
        }
        return false;
    }
}
