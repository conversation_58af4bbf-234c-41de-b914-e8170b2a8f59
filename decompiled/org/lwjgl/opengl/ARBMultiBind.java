/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.PointerBuffer;
import org.lwjgl.opengl.GL44;

public final class ARBMultiBind {
    private ARBMultiBind() {
    }

    public static void glBindBuffersBase(int n, int n2, int n3, IntBuffer intBuffer) {
        GL44.glBindBuffersBase(n, n2, n3, intBuffer);
    }

    public static void glBindBuffersRange(int n, int n2, int n3, IntBuffer intBuffer, PointerBuffer pointerBuffer, PointerBuffer pointerBuffer2) {
        GL44.glBindBuffersRange(n, n2, n3, intBuffer, pointerBuffer, pointerBuffer2);
    }

    public static void glBindTextures(int n, int n2, IntBuffer intBuffer) {
        GL44.glBindTextures(n, n2, intBuffer);
    }

    public static void glBindSamplers(int n, int n2, IntBuffer intBuffer) {
        GL44.glBindSamplers(n, n2, intBuffer);
    }

    public static void glBindImageTextures(int n, int n2, IntBuffer intBuffer) {
        GL44.glBindImageTextures(n, n2, intBuffer);
    }

    public static void glBindVertexBuffers(int n, int n2, IntBuffer intBuffer, PointerBuffer pointerBuffer, IntBuffer intBuffer2) {
        GL44.glBindVertexBuffers(n, n2, intBuffer, pointerBuffer, intBuffer2);
    }
}
