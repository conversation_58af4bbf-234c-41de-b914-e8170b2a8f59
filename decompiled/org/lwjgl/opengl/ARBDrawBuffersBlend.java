/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ARBDrawBuffersBlend {
    private ARBDrawBuffersBlend() {
    }

    public static void glBlendEquationiARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendEquationiARB;
        BufferChecks.checkFunctionAddress(l);
        ARBDrawBuffersBlend.nglBlendEquationiARB(n, n2, l);
    }

    static native void nglBlendEquationiARB(int var0, int var1, long var2);

    public static void glBlendEquationSeparateiARB(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendEquationSeparateiARB;
        BufferChecks.checkFunctionAddress(l);
        ARBDrawBuffersBlend.nglBlendEquationSeparateiARB(n, n2, n3, l);
    }

    static native void nglBlendEquationSeparateiARB(int var0, int var1, int var2, long var3);

    public static void glBlendFunciARB(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendFunciARB;
        BufferChecks.checkFunctionAddress(l);
        ARBDrawBuffersBlend.nglBlendFunciARB(n, n2, n3, l);
    }

    static native void nglBlendFunciARB(int var0, int var1, int var2, long var3);

    public static void glBlendFuncSeparateiARB(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendFuncSeparateiARB;
        BufferChecks.checkFunctionAddress(l);
        ARBDrawBuffersBlend.nglBlendFuncSeparateiARB(n, n2, n3, n4, n5, l);
    }

    static native void nglBlendFuncSeparateiARB(int var0, int var1, int var2, int var3, int var4, long var5);
}
