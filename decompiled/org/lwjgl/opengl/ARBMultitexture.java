/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ARBMultitexture {
    public static final int GL_TEXTURE0_ARB = 33984;
    public static final int GL_TEXTURE1_ARB = 33985;
    public static final int GL_TEXTURE2_ARB = 33986;
    public static final int GL_TEXTURE3_ARB = 33987;
    public static final int GL_TEXTURE4_ARB = 33988;
    public static final int GL_TEXTURE5_ARB = 33989;
    public static final int GL_TEXTURE6_ARB = 33990;
    public static final int GL_TEXTURE7_ARB = 33991;
    public static final int GL_TEXTURE8_ARB = 33992;
    public static final int GL_TEXTURE9_ARB = 33993;
    public static final int GL_TEXTURE10_ARB = 33994;
    public static final int GL_TEXTURE11_ARB = 33995;
    public static final int GL_TEXTURE12_ARB = 33996;
    public static final int GL_TEXTURE13_ARB = 33997;
    public static final int GL_TEXTURE14_ARB = 33998;
    public static final int GL_TEXTURE15_ARB = 33999;
    public static final int GL_TEXTURE16_ARB = 34000;
    public static final int GL_TEXTURE17_ARB = 34001;
    public static final int GL_TEXTURE18_ARB = 34002;
    public static final int GL_TEXTURE19_ARB = 34003;
    public static final int GL_TEXTURE20_ARB = 34004;
    public static final int GL_TEXTURE21_ARB = 34005;
    public static final int GL_TEXTURE22_ARB = 34006;
    public static final int GL_TEXTURE23_ARB = 34007;
    public static final int GL_TEXTURE24_ARB = 34008;
    public static final int GL_TEXTURE25_ARB = 34009;
    public static final int GL_TEXTURE26_ARB = 34010;
    public static final int GL_TEXTURE27_ARB = 34011;
    public static final int GL_TEXTURE28_ARB = 34012;
    public static final int GL_TEXTURE29_ARB = 34013;
    public static final int GL_TEXTURE30_ARB = 34014;
    public static final int GL_TEXTURE31_ARB = 34015;
    public static final int GL_ACTIVE_TEXTURE_ARB = 34016;
    public static final int GL_CLIENT_ACTIVE_TEXTURE_ARB = 34017;
    public static final int GL_MAX_TEXTURE_UNITS_ARB = 34018;

    private ARBMultitexture() {
    }

    public static void glClientActiveTextureARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClientActiveTextureARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglClientActiveTextureARB(n, l);
    }

    static native void nglClientActiveTextureARB(int var0, long var1);

    public static void glActiveTextureARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glActiveTextureARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglActiveTextureARB(n, l);
    }

    static native void nglActiveTextureARB(int var0, long var1);

    public static void glMultiTexCoord1fARB(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord1fARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord1fARB(n, f, l);
    }

    static native void nglMultiTexCoord1fARB(int var0, float var1, long var2);

    public static void glMultiTexCoord1dARB(int n, double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord1dARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord1dARB(n, d, l);
    }

    static native void nglMultiTexCoord1dARB(int var0, double var1, long var3);

    public static void glMultiTexCoord1iARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord1iARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord1iARB(n, n2, l);
    }

    static native void nglMultiTexCoord1iARB(int var0, int var1, long var2);

    public static void glMultiTexCoord1sARB(int n, short s) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord1sARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord1sARB(n, s, l);
    }

    static native void nglMultiTexCoord1sARB(int var0, short var1, long var2);

    public static void glMultiTexCoord2fARB(int n, float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord2fARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord2fARB(n, f, f2, l);
    }

    static native void nglMultiTexCoord2fARB(int var0, float var1, float var2, long var3);

    public static void glMultiTexCoord2dARB(int n, double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord2dARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord2dARB(n, d, d2, l);
    }

    static native void nglMultiTexCoord2dARB(int var0, double var1, double var3, long var5);

    public static void glMultiTexCoord2iARB(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord2iARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord2iARB(n, n2, n3, l);
    }

    static native void nglMultiTexCoord2iARB(int var0, int var1, int var2, long var3);

    public static void glMultiTexCoord2sARB(int n, short s, short s2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord2sARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord2sARB(n, s, s2, l);
    }

    static native void nglMultiTexCoord2sARB(int var0, short var1, short var2, long var3);

    public static void glMultiTexCoord3fARB(int n, float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord3fARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord3fARB(n, f, f2, f3, l);
    }

    static native void nglMultiTexCoord3fARB(int var0, float var1, float var2, float var3, long var4);

    public static void glMultiTexCoord3dARB(int n, double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord3dARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord3dARB(n, d, d2, d3, l);
    }

    static native void nglMultiTexCoord3dARB(int var0, double var1, double var3, double var5, long var7);

    public static void glMultiTexCoord3iARB(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord3iARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord3iARB(n, n2, n3, n4, l);
    }

    static native void nglMultiTexCoord3iARB(int var0, int var1, int var2, int var3, long var4);

    public static void glMultiTexCoord3sARB(int n, short s, short s2, short s3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord3sARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord3sARB(n, s, s2, s3, l);
    }

    static native void nglMultiTexCoord3sARB(int var0, short var1, short var2, short var3, long var4);

    public static void glMultiTexCoord4fARB(int n, float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord4fARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord4fARB(n, f, f2, f3, f4, l);
    }

    static native void nglMultiTexCoord4fARB(int var0, float var1, float var2, float var3, float var4, long var5);

    public static void glMultiTexCoord4dARB(int n, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord4dARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord4dARB(n, d, d2, d3, d4, l);
    }

    static native void nglMultiTexCoord4dARB(int var0, double var1, double var3, double var5, double var7, long var9);

    public static void glMultiTexCoord4iARB(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord4iARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord4iARB(n, n2, n3, n4, n5, l);
    }

    static native void nglMultiTexCoord4iARB(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glMultiTexCoord4sARB(int n, short s, short s2, short s3, short s4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiTexCoord4sARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMultitexture.nglMultiTexCoord4sARB(n, s, s2, s3, s4, l);
    }

    static native void nglMultiTexCoord4sARB(int var0, short var1, short var2, short var3, short var4, long var5);
}
