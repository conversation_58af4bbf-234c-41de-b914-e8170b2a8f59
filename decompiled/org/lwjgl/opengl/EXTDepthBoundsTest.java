/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class EXTDepthBoundsTest {
    public static final int GL_DEPTH_BOUNDS_TEST_EXT = 34960;
    public static final int GL_DEPTH_BOUNDS_EXT = 34961;

    private EXTDepthBoundsTest() {
    }

    public static void glDepthBoundsEXT(double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDepthBoundsEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTDepthBoundsTest.nglDepthBoundsEXT(d, d2, l);
    }

    static native void nglDepthBoundsEXT(double var0, double var2, long var4);
}
