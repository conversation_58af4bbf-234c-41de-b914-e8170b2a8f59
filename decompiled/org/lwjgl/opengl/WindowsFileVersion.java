/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

final class WindowsFileVersion {
    private final int product_version_ms;
    private final int product_version_ls;

    WindowsFileVersion(int n, int n2) {
        this.product_version_ms = n;
        this.product_version_ls = n2;
    }

    public final String toString() {
        int n = this.product_version_ms >> 16 & 0xFFFF;
        int n2 = this.product_version_ms & 0xFFFF;
        int n3 = this.product_version_ls >> 16 & 0xFFFF;
        int n4 = this.product_version_ls & 0xFFFF;
        return n + "." + n2 + "." + n3 + "." + n4;
    }
}
