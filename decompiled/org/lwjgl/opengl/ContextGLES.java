/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.lwjgl.opengles.EGL
 *  org.lwjgl.opengles.EGLContext
 *  org.lwjgl.opengles.EGLDisplay
 *  org.lwjgl.opengles.GLContext
 *  org.lwjgl.opengles.GLES20
 *  org.lwjgl.opengles.PowerManagementEventException
 */
package org.lwjgl.opengl;

import org.lwjgl.LWJGLException;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.Sys;
import org.lwjgl.opengl.Context;
import org.lwjgl.opengl.DrawableGLES;
import org.lwjgl.opengl.OpenGLException;
import org.lwjgl.opengles.ContextAttribs;
import org.lwjgl.opengles.EGL;
import org.lwjgl.opengles.EGLContext;
import org.lwjgl.opengles.EGLDisplay;
import org.lwjgl.opengles.GLContext;
import org.lwjgl.opengles.GLES20;
import org.lwjgl.opengles.PowerManagementEventException;

final class ContextGLES
implements Context {
    private static final ThreadLocal<ContextGLES> current_context_local = new ThreadLocal();
    private final DrawableGLES drawable;
    private final EGLContext eglContext;
    private final ContextAttribs contextAttribs;
    private boolean destroyed;
    private boolean destroy_requested;
    private Thread thread;

    public final EGLContext getEGLContext() {
        return this.eglContext;
    }

    final ContextAttribs getContextAttribs() {
        return this.contextAttribs;
    }

    static ContextGLES getCurrentContext() {
        return current_context_local.get();
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    ContextGLES(DrawableGLES drawableGLES, ContextAttribs contextAttribs, ContextGLES contextGLES) {
        if (drawableGLES == null) {
            throw new IllegalArgumentException();
        }
        ContextGLES contextGLES2 = contextGLES != null ? contextGLES : this;
        ContextGLES contextGLES3 = contextGLES2;
        contextGLES3 = contextGLES2;
        synchronized (contextGLES2) {
            if (contextGLES != null && contextGLES.destroyed) {
                throw new IllegalArgumentException("Shared context is destroyed");
            }
            this.drawable = drawableGLES;
            this.contextAttribs = contextAttribs;
            this.eglContext = drawableGLES.getEGLDisplay().createContext(drawableGLES.getEGLConfig(), contextGLES == null ? null : contextGLES.eglContext, contextAttribs == null ? new ContextAttribs(2).getAttribList() : contextAttribs.getAttribList());
            // ** MonitorExit[var4_5] (shouldn't be in output)
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public final void releaseCurrent() {
        EGL.eglReleaseCurrent((EGLDisplay)this.drawable.getEGLDisplay());
        GLContext.useContext(null);
        current_context_local.set(null);
        ContextGLES contextGLES = this;
        synchronized (contextGLES) {
            this.thread = null;
            this.checkDestroy();
            return;
        }
    }

    public static void swapBuffers() {
        ContextGLES contextGLES = ContextGLES.getCurrentContext();
        if (contextGLES != null) {
            contextGLES.drawable.getEGLSurface().swapBuffers();
        }
    }

    private boolean canAccess() {
        return this.thread == null || Thread.currentThread() == this.thread;
    }

    private void checkAccess() {
        if (!this.canAccess()) {
            throw new IllegalStateException("From thread " + Thread.currentThread() + ": " + this.thread + " already has the context current");
        }
    }

    public final synchronized void makeCurrent() {
        this.checkAccess();
        if (this.destroyed) {
            throw new IllegalStateException("Context is destroyed");
        }
        this.thread = Thread.currentThread();
        current_context_local.set(this);
        this.eglContext.makeCurrent(this.drawable.getEGLSurface());
        GLContext.useContext((Object)this);
    }

    public final synchronized boolean isCurrent() {
        if (this.destroyed) {
            throw new IllegalStateException("Context is destroyed");
        }
        return EGL.eglIsCurrentContext((EGLContext)this.eglContext);
    }

    private void checkDestroy() {
        if (!this.destroyed && this.destroy_requested) {
            try {
                this.eglContext.destroy();
                this.destroyed = true;
                this.thread = null;
                return;
            }
            catch (LWJGLException lWJGLException) {
                LWJGLUtil.log("Exception occurred while destroying context: " + lWJGLException);
            }
        }
    }

    public static void setSwapInterval(int n) {
        ContextGLES contextGLES = ContextGLES.getCurrentContext();
        if (contextGLES != null) {
            try {
                contextGLES.drawable.getEGLDisplay().setSwapInterval(n);
                return;
            }
            catch (LWJGLException lWJGLException) {
                LWJGLUtil.log("Failed to set swap interval. Reason: " + lWJGLException.getMessage());
            }
        }
    }

    public final synchronized void forceDestroy() {
        this.checkAccess();
        this.destroy();
    }

    public final synchronized void destroy() {
        if (this.destroyed) {
            return;
        }
        this.destroy_requested = true;
        boolean bl = this.isCurrent();
        int n = 0;
        if (bl) {
            if (GLContext.getCapabilities() != null && GLContext.getCapabilities().OpenGLES20) {
                n = GLES20.glGetError();
            }
            try {
                this.releaseCurrent();
            }
            catch (PowerManagementEventException powerManagementEventException) {}
        }
        this.checkDestroy();
        if (bl && n != 0) {
            throw new OpenGLException(n);
        }
    }

    public final void releaseDrawable() {
    }

    static {
        Sys.initialize();
    }
}
