/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GL42;
import org.lwjgl.opengl.GLContext;

public final class ARBTextureStorage {
    public static final int GL_TEXTURE_IMMUTABLE_FORMAT = 37167;

    private ARBTextureStorage() {
    }

    public static void glTexStorage1D(int n, int n2, int n3, int n4) {
        GL42.glTexStorage1D(n, n2, n3, n4);
    }

    public static void glTexStorage2D(int n, int n2, int n3, int n4, int n5) {
        GL42.glTexStorage2D(n, n2, n3, n4, n5);
    }

    public static void glTexStorage3D(int n, int n2, int n3, int n4, int n5, int n6) {
        GL42.glTexStorage3D(n, n2, n3, n4, n5, n6);
    }

    public static void glTextureStorage1DEXT(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureStorage1DEXT;
        BufferChecks.checkFunctionAddress(l);
        ARBTextureStorage.nglTextureStorage1DEXT(n, n2, n3, n4, n5, l);
    }

    static native void nglTextureStorage1DEXT(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glTextureStorage2DEXT(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureStorage2DEXT;
        BufferChecks.checkFunctionAddress(l);
        ARBTextureStorage.nglTextureStorage2DEXT(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglTextureStorage2DEXT(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glTextureStorage3DEXT(int n, int n2, int n3, int n4, int n5, int n6, int n7) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureStorage3DEXT;
        BufferChecks.checkFunctionAddress(l);
        ARBTextureStorage.nglTextureStorage3DEXT(n, n2, n3, n4, n5, n6, n7, l);
    }

    static native void nglTextureStorage3DEXT(int var0, int var1, int var2, int var3, int var4, int var5, int var6, long var7);
}
