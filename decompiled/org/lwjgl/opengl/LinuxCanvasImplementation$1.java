/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.GraphicsDevice;
import java.lang.reflect.Method;
import java.security.PrivilegedExceptionAction;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
static final class LinuxCanvasImplementation.1
implements PrivilegedExceptionAction<Method> {
    final /* synthetic */ GraphicsDevice val$device;

    LinuxCanvasImplementation.1(GraphicsDevice graphicsDevice) {
        this.val$device = graphicsDevice;
    }

    @Override
    public final Method run() {
        return this.val$device.getClass().getMethod("getScreen", new Class[0]);
    }
}
