/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.LWJGLUtil;

final class WindowsEventDebug {
    private WindowsEventDebug() {
    }

    static int printMessage(String string, long l, long l2) {
        System.out.println(string + ": 0x" + Long.toHexString(l).toUpperCase() + " | " + Long.toHexString(l2).toUpperCase());
        return 0;
    }

    static int printMessage(int n, long l, long l2) {
        System.out.print(LWJGLUtil.toHexString(n) + ": ");
        switch (n) {
            case 0: {
                return WindowsEventDebug.printMessage("WM_NULL", l, l2);
            }
            case 1: {
                return WindowsEventDebug.printMessage("WM_CREATE", l, l2);
            }
            case 2: {
                return WindowsEventDebug.printMessage("WM_DESTROY", l, l2);
            }
            case 3: {
                return WindowsEventDebug.printMessage("WM_MOVE", l, l2);
            }
            case 5: {
                return WindowsEventDebug.printMessage("WM_SIZE", l, l2);
            }
            case 6: {
                return WindowsEventDebug.printMessage("WM_ACTIVATE", l, l2);
            }
            case 7: {
                return WindowsEventDebug.printMessage("WM_SETFOCUS", l, l2);
            }
            case 8: {
                return WindowsEventDebug.printMessage("WM_KILLFOCUS", l, l2);
            }
            case 10: {
                return WindowsEventDebug.printMessage("WM_ENABLE", l, l2);
            }
            case 11: {
                return WindowsEventDebug.printMessage("WM_SETREDRAW", l, l2);
            }
            case 12: {
                return WindowsEventDebug.printMessage("WM_SETTEXT", l, l2);
            }
            case 13: {
                return WindowsEventDebug.printMessage("WM_GETTEXT", l, l2);
            }
            case 14: {
                return WindowsEventDebug.printMessage("WM_GETTEXTLENGTH", l, l2);
            }
            case 15: {
                return WindowsEventDebug.printMessage("WM_PAINT", l, l2);
            }
            case 16: {
                return WindowsEventDebug.printMessage("WM_CLOSE", l, l2);
            }
            case 17: {
                return WindowsEventDebug.printMessage("WM_QUERYENDSESSION", l, l2);
            }
            case 19: {
                return WindowsEventDebug.printMessage("WM_QUERYOPEN", l, l2);
            }
            case 22: {
                return WindowsEventDebug.printMessage("WM_ENDSESSION", l, l2);
            }
            case 18: {
                return WindowsEventDebug.printMessage("WM_QUIT", l, l2);
            }
            case 20: {
                return WindowsEventDebug.printMessage("WM_ERASEBKGND", l, l2);
            }
            case 21: {
                return WindowsEventDebug.printMessage("WM_SYSCOLORCHANGE", l, l2);
            }
            case 24: {
                return WindowsEventDebug.printMessage("WM_SHOWWINDOW", l, l2);
            }
            case 26: {
                return WindowsEventDebug.printMessage("WM_WININICHANGE", l, l2);
            }
            case 27: {
                return WindowsEventDebug.printMessage("WM_DEVMODECHANGE", l, l2);
            }
            case 28: {
                return WindowsEventDebug.printMessage("WM_ACTIVATEAPP", l, l2);
            }
            case 29: {
                return WindowsEventDebug.printMessage("WM_FONTCHANGE", l, l2);
            }
            case 30: {
                return WindowsEventDebug.printMessage("WM_TIMECHANGE", l, l2);
            }
            case 31: {
                return WindowsEventDebug.printMessage("WM_CANCELMODE", l, l2);
            }
            case 32: {
                return WindowsEventDebug.printMessage("WM_SETCURSOR", l, l2);
            }
            case 33: {
                return WindowsEventDebug.printMessage("WM_MOUSEACTIVATE", l, l2);
            }
            case 34: {
                return WindowsEventDebug.printMessage("WM_CHILDACTIVATE", l, l2);
            }
            case 35: {
                return WindowsEventDebug.printMessage("WM_QUEUESYNC", l, l2);
            }
            case 36: {
                return WindowsEventDebug.printMessage("WM_GETMINMAXINFO", l, l2);
            }
            case 38: {
                return WindowsEventDebug.printMessage("WM_PAINTICON", l, l2);
            }
            case 39: {
                return WindowsEventDebug.printMessage("WM_ICONERASEBKGND", l, l2);
            }
            case 40: {
                return WindowsEventDebug.printMessage("WM_NEXTDLGCTL", l, l2);
            }
            case 42: {
                return WindowsEventDebug.printMessage("WM_SPOOLERSTATUS", l, l2);
            }
            case 43: {
                return WindowsEventDebug.printMessage("WM_DRAWITEM", l, l2);
            }
            case 44: {
                return WindowsEventDebug.printMessage("WM_MEASUREITEM", l, l2);
            }
            case 45: {
                return WindowsEventDebug.printMessage("WM_DELETEITEM", l, l2);
            }
            case 46: {
                return WindowsEventDebug.printMessage("WM_VKEYTOITEM", l, l2);
            }
            case 47: {
                return WindowsEventDebug.printMessage("WM_CHARTOITEM", l, l2);
            }
            case 48: {
                return WindowsEventDebug.printMessage("WM_SETFONT", l, l2);
            }
            case 49: {
                return WindowsEventDebug.printMessage("WM_GETFONT", l, l2);
            }
            case 50: {
                return WindowsEventDebug.printMessage("WM_SETHOTKEY", l, l2);
            }
            case 51: {
                return WindowsEventDebug.printMessage("WM_GETHOTKEY", l, l2);
            }
            case 55: {
                return WindowsEventDebug.printMessage("WM_QUERYDRAGICON", l, l2);
            }
            case 57: {
                return WindowsEventDebug.printMessage("WM_COMPAREITEM", l, l2);
            }
            case 61: {
                return WindowsEventDebug.printMessage("WM_GETOBJECT", l, l2);
            }
            case 65: {
                return WindowsEventDebug.printMessage("WM_COMPACTING", l, l2);
            }
            case 68: {
                return WindowsEventDebug.printMessage("WM_COMMNOTIFY", l, l2);
            }
            case 70: {
                return WindowsEventDebug.printMessage("WM_WINDOWPOSCHANGING", l, l2);
            }
            case 71: {
                return WindowsEventDebug.printMessage("WM_WINDOWPOSCHANGED", l, l2);
            }
            case 72: {
                return WindowsEventDebug.printMessage("WM_POWER", l, l2);
            }
            case 74: {
                return WindowsEventDebug.printMessage("WM_COPYDATA", l, l2);
            }
            case 75: {
                return WindowsEventDebug.printMessage("WM_CANCELJOURNAL", l, l2);
            }
            case 78: {
                return WindowsEventDebug.printMessage("WM_NOTIFY", l, l2);
            }
            case 80: {
                return WindowsEventDebug.printMessage("WM_INPUTLANGCHANGEREQUEST", l, l2);
            }
            case 81: {
                return WindowsEventDebug.printMessage("WM_INPUTLANGCHANGE", l, l2);
            }
            case 82: {
                return WindowsEventDebug.printMessage("WM_TCARD", l, l2);
            }
            case 83: {
                return WindowsEventDebug.printMessage("WM_HELP", l, l2);
            }
            case 84: {
                return WindowsEventDebug.printMessage("WM_USERCHANGED", l, l2);
            }
            case 85: {
                return WindowsEventDebug.printMessage("WM_NOTIFYFORMAT", l, l2);
            }
            case 123: {
                return WindowsEventDebug.printMessage("WM_CONTEXTMENU", l, l2);
            }
            case 124: {
                return WindowsEventDebug.printMessage("WM_STYLECHANGING", l, l2);
            }
            case 125: {
                return WindowsEventDebug.printMessage("WM_STYLECHANGED", l, l2);
            }
            case 126: {
                return WindowsEventDebug.printMessage("WM_DISPLAYCHANGE", l, l2);
            }
            case 127: {
                return WindowsEventDebug.printMessage("WM_GETICON", l, l2);
            }
            case 128: {
                return WindowsEventDebug.printMessage("WM_SETICON", l, l2);
            }
            case 129: {
                return WindowsEventDebug.printMessage("WM_NCCREATE", l, l2);
            }
            case 130: {
                return WindowsEventDebug.printMessage("WM_NCDESTROY", l, l2);
            }
            case 131: {
                return WindowsEventDebug.printMessage("WM_NCCALCSIZE", l, l2);
            }
            case 132: {
                return WindowsEventDebug.printMessage("WM_NCHITTEST", l, l2);
            }
            case 133: {
                return WindowsEventDebug.printMessage("WM_NCPAINT", l, l2);
            }
            case 134: {
                return WindowsEventDebug.printMessage("WM_NCACTIVATE", l, l2);
            }
            case 135: {
                return WindowsEventDebug.printMessage("WM_GETDLGCODE", l, l2);
            }
            case 136: {
                return WindowsEventDebug.printMessage("WM_SYNCPAINT", l, l2);
            }
            case 160: {
                return WindowsEventDebug.printMessage("WM_NCMOUSEMOVE", l, l2);
            }
            case 161: {
                return WindowsEventDebug.printMessage("WM_NCLBUTTONDOWN", l, l2);
            }
            case 162: {
                return WindowsEventDebug.printMessage("WM_NCLBUTTONUP", l, l2);
            }
            case 163: {
                return WindowsEventDebug.printMessage("WM_NCLBUTTONDBLCLK", l, l2);
            }
            case 164: {
                return WindowsEventDebug.printMessage("WM_NCRBUTTONDOWN", l, l2);
            }
            case 165: {
                return WindowsEventDebug.printMessage("WM_NCRBUTTONUP", l, l2);
            }
            case 166: {
                return WindowsEventDebug.printMessage("WM_NCRBUTTONDBLCLK", l, l2);
            }
            case 167: {
                return WindowsEventDebug.printMessage("WM_NCMBUTTONDOWN", l, l2);
            }
            case 168: {
                return WindowsEventDebug.printMessage("WM_NCMBUTTONUP", l, l2);
            }
            case 169: {
                return WindowsEventDebug.printMessage("WM_NCMBUTTONDBLCLK", l, l2);
            }
            case 171: {
                return WindowsEventDebug.printMessage("WM_NCXBUTTONDOWN", l, l2);
            }
            case 172: {
                return WindowsEventDebug.printMessage("WM_NCXBUTTONUP", l, l2);
            }
            case 173: {
                return WindowsEventDebug.printMessage("WM_NCXBUTTONDBLCLK", l, l2);
            }
            case 254: {
                return WindowsEventDebug.printMessage("WM_INPUT_DEVICE_CHANGE", l, l2);
            }
            case 255: {
                return WindowsEventDebug.printMessage("WM_INPUT", l, l2);
            }
            case 256: {
                return WindowsEventDebug.printMessage("WM_KEYDOWN", l, l2);
            }
            case 257: {
                return WindowsEventDebug.printMessage("WM_KEYUP", l, l2);
            }
            case 258: {
                return WindowsEventDebug.printMessage("WM_CHAR", l, l2);
            }
            case 259: {
                return WindowsEventDebug.printMessage("WM_DEADCHAR", l, l2);
            }
            case 260: {
                return WindowsEventDebug.printMessage("WM_SYSKEYDOWN", l, l2);
            }
            case 261: {
                return WindowsEventDebug.printMessage("WM_SYSKEYUP", l, l2);
            }
            case 262: {
                return WindowsEventDebug.printMessage("WM_SYSCHAR", l, l2);
            }
            case 263: {
                return WindowsEventDebug.printMessage("WM_SYSDEADCHAR", l, l2);
            }
            case 265: {
                return WindowsEventDebug.printMessage("WM_UNICHAR", l, l2);
            }
            case 65535: {
                return WindowsEventDebug.printMessage("UNICODE_NOCHAR", l, l2);
            }
            case 264: {
                return WindowsEventDebug.printMessage("WM_KEYLAST", l, l2);
            }
            case 269: {
                return WindowsEventDebug.printMessage("WM_IME_STARTCOMPOSITION", l, l2);
            }
            case 270: {
                return WindowsEventDebug.printMessage("WM_IME_ENDCOMPOSITION", l, l2);
            }
            case 271: {
                return WindowsEventDebug.printMessage("WM_IME_COMPOSITION", l, l2);
            }
            case 272: {
                return WindowsEventDebug.printMessage("WM_INITDIALOG", l, l2);
            }
            case 273: {
                return WindowsEventDebug.printMessage("WM_COMMAND", l, l2);
            }
            case 274: {
                return WindowsEventDebug.printMessage("WM_SYSCOMMAND", l, l2);
            }
            case 275: {
                return WindowsEventDebug.printMessage("WM_TIMER", l, l2);
            }
            case 276: {
                return WindowsEventDebug.printMessage("WM_HSCROLL", l, l2);
            }
            case 277: {
                return WindowsEventDebug.printMessage("WM_VSCROLL", l, l2);
            }
            case 278: {
                return WindowsEventDebug.printMessage("WM_INITMENU", l, l2);
            }
            case 279: {
                return WindowsEventDebug.printMessage("WM_INITMENUPOPUP", l, l2);
            }
            case 281: {
                return WindowsEventDebug.printMessage("WM_GESTURE", l, l2);
            }
            case 282: {
                return WindowsEventDebug.printMessage("WM_GESTURENOTIFY", l, l2);
            }
            case 287: {
                return WindowsEventDebug.printMessage("WM_MENUSELECT", l, l2);
            }
            case 288: {
                return WindowsEventDebug.printMessage("WM_MENUCHAR", l, l2);
            }
            case 289: {
                return WindowsEventDebug.printMessage("WM_ENTERIDLE", l, l2);
            }
            case 290: {
                return WindowsEventDebug.printMessage("WM_MENURBUTTONUP", l, l2);
            }
            case 291: {
                return WindowsEventDebug.printMessage("WM_MENUDRAG", l, l2);
            }
            case 292: {
                return WindowsEventDebug.printMessage("WM_MENUGETOBJECT", l, l2);
            }
            case 293: {
                return WindowsEventDebug.printMessage("WM_UNINITMENUPOPUP", l, l2);
            }
            case 294: {
                return WindowsEventDebug.printMessage("WM_MENUCOMMAND", l, l2);
            }
            case 295: {
                return WindowsEventDebug.printMessage("WM_CHANGEUISTATE", l, l2);
            }
            case 296: {
                return WindowsEventDebug.printMessage("WM_UPDATEUISTATE", l, l2);
            }
            case 297: {
                return WindowsEventDebug.printMessage("WM_QUERYUISTATE", l, l2);
            }
            case 306: {
                return WindowsEventDebug.printMessage("WM_CTLCOLORMSGBOX", l, l2);
            }
            case 307: {
                return WindowsEventDebug.printMessage("WM_CTLCOLOREDIT", l, l2);
            }
            case 308: {
                return WindowsEventDebug.printMessage("WM_CTLCOLORLISTBOX", l, l2);
            }
            case 309: {
                return WindowsEventDebug.printMessage("WM_CTLCOLORBTN", l, l2);
            }
            case 310: {
                return WindowsEventDebug.printMessage("WM_CTLCOLORDLG", l, l2);
            }
            case 311: {
                return WindowsEventDebug.printMessage("WM_CTLCOLORSCROLLBAR", l, l2);
            }
            case 312: {
                return WindowsEventDebug.printMessage("WM_CTLCOLORSTATIC", l, l2);
            }
            case 481: {
                return WindowsEventDebug.printMessage("MN_GETHMENU", l, l2);
            }
            case 512: {
                return WindowsEventDebug.printMessage("WM_MOUSEMOVE", l, l2);
            }
            case 513: {
                return WindowsEventDebug.printMessage("WM_LBUTTONDOWN", l, l2);
            }
            case 514: {
                return WindowsEventDebug.printMessage("WM_LBUTTONUP", l, l2);
            }
            case 515: {
                return WindowsEventDebug.printMessage("WM_LBUTTONDBLCLK", l, l2);
            }
            case 516: {
                return WindowsEventDebug.printMessage("WM_RBUTTONDOWN", l, l2);
            }
            case 517: {
                return WindowsEventDebug.printMessage("WM_RBUTTONUP", l, l2);
            }
            case 518: {
                return WindowsEventDebug.printMessage("WM_RBUTTONDBLCLK", l, l2);
            }
            case 519: {
                return WindowsEventDebug.printMessage("WM_MBUTTONDOWN", l, l2);
            }
            case 520: {
                return WindowsEventDebug.printMessage("WM_MBUTTONUP", l, l2);
            }
            case 521: {
                return WindowsEventDebug.printMessage("WM_MBUTTONDBLCLK", l, l2);
            }
            case 522: {
                return WindowsEventDebug.printMessage("WM_MOUSEWHEEL", l, l2);
            }
            case 523: {
                return WindowsEventDebug.printMessage("WM_XBUTTONDOWN", l, l2);
            }
            case 524: {
                return WindowsEventDebug.printMessage("WM_XBUTTONUP", l, l2);
            }
            case 525: {
                return WindowsEventDebug.printMessage("WM_XBUTTONDBLCLK", l, l2);
            }
            case 526: {
                return WindowsEventDebug.printMessage("WM_MOUSEHWHEEL", l, l2);
            }
            case 528: {
                return WindowsEventDebug.printMessage("WM_PARENTNOTIFY", l, l2);
            }
            case 529: {
                return WindowsEventDebug.printMessage("WM_ENTERMENULOOP", l, l2);
            }
            case 530: {
                return WindowsEventDebug.printMessage("WM_EXITMENULOOP", l, l2);
            }
            case 531: {
                return WindowsEventDebug.printMessage("WM_NEXTMENU", l, l2);
            }
            case 532: {
                return WindowsEventDebug.printMessage("WM_SIZING", l, l2);
            }
            case 533: {
                return WindowsEventDebug.printMessage("WM_CAPTURECHANGED", l, l2);
            }
            case 534: {
                return WindowsEventDebug.printMessage("WM_MOVING", l, l2);
            }
            case 536: {
                return WindowsEventDebug.printMessage("WM_POWERBROADCAST", l, l2);
            }
            case 32787: {
                return WindowsEventDebug.printMessage("PBT_POWERSETTINGCHANGE", l, l2);
            }
            case 537: {
                return WindowsEventDebug.printMessage("WM_DEVICECHANGE", l, l2);
            }
            case 544: {
                return WindowsEventDebug.printMessage("WM_MDICREATE", l, l2);
            }
            case 545: {
                return WindowsEventDebug.printMessage("WM_MDIDESTROY", l, l2);
            }
            case 546: {
                return WindowsEventDebug.printMessage("WM_MDIACTIVATE", l, l2);
            }
            case 547: {
                return WindowsEventDebug.printMessage("WM_MDIRESTORE", l, l2);
            }
            case 548: {
                return WindowsEventDebug.printMessage("WM_MDINEXT", l, l2);
            }
            case 549: {
                return WindowsEventDebug.printMessage("WM_MDIMAXIMIZE", l, l2);
            }
            case 550: {
                return WindowsEventDebug.printMessage("WM_MDITILE", l, l2);
            }
            case 551: {
                return WindowsEventDebug.printMessage("WM_MDICASCADE", l, l2);
            }
            case 552: {
                return WindowsEventDebug.printMessage("WM_MDIICONARRANGE", l, l2);
            }
            case 553: {
                return WindowsEventDebug.printMessage("WM_MDIGETACTIVE", l, l2);
            }
            case 560: {
                return WindowsEventDebug.printMessage("WM_MDISETMENU", l, l2);
            }
            case 561: {
                return WindowsEventDebug.printMessage("WM_ENTERSIZEMOVE", l, l2);
            }
            case 562: {
                return WindowsEventDebug.printMessage("WM_EXITSIZEMOVE", l, l2);
            }
            case 563: {
                return WindowsEventDebug.printMessage("WM_DROPFILES", l, l2);
            }
            case 564: {
                return WindowsEventDebug.printMessage("WM_MDIREFRESHMENU", l, l2);
            }
            case 576: {
                return WindowsEventDebug.printMessage("WM_TOUCH", l, l2);
            }
            case 641: {
                return WindowsEventDebug.printMessage("WM_IME_SETCONTEXT", l, l2);
            }
            case 642: {
                return WindowsEventDebug.printMessage("WM_IME_NOTIFY", l, l2);
            }
            case 643: {
                return WindowsEventDebug.printMessage("WM_IME_CONTROL", l, l2);
            }
            case 644: {
                return WindowsEventDebug.printMessage("WM_IME_COMPOSITIONFULL", l, l2);
            }
            case 645: {
                return WindowsEventDebug.printMessage("WM_IME_SELECT", l, l2);
            }
            case 646: {
                return WindowsEventDebug.printMessage("WM_IME_CHAR", l, l2);
            }
            case 648: {
                return WindowsEventDebug.printMessage("WM_IME_REQUEST", l, l2);
            }
            case 656: {
                return WindowsEventDebug.printMessage("WM_IME_KEYDOWN", l, l2);
            }
            case 657: {
                return WindowsEventDebug.printMessage("WM_IME_KEYUP", l, l2);
            }
            case 673: {
                return WindowsEventDebug.printMessage("WM_MOUSEHOVER", l, l2);
            }
            case 675: {
                return WindowsEventDebug.printMessage("WM_MOUSELEAVE", l, l2);
            }
            case 672: {
                return WindowsEventDebug.printMessage("WM_NCMOUSEHOVER", l, l2);
            }
            case 674: {
                return WindowsEventDebug.printMessage("WM_NCMOUSELEAVE", l, l2);
            }
            case 689: {
                return WindowsEventDebug.printMessage("WM_WTSSESSION_CHANGE", l, l2);
            }
            case 704: {
                return WindowsEventDebug.printMessage("WM_TABLET_FIRST", l, l2);
            }
            case 735: {
                return WindowsEventDebug.printMessage("WM_TABLET_LAST", l, l2);
            }
            case 768: {
                return WindowsEventDebug.printMessage("WM_CUT", l, l2);
            }
            case 769: {
                return WindowsEventDebug.printMessage("WM_COPY", l, l2);
            }
            case 770: {
                return WindowsEventDebug.printMessage("WM_PASTE", l, l2);
            }
            case 771: {
                return WindowsEventDebug.printMessage("WM_CLEAR", l, l2);
            }
            case 772: {
                return WindowsEventDebug.printMessage("WM_UNDO", l, l2);
            }
            case 773: {
                return WindowsEventDebug.printMessage("WM_RENDERFORMAT", l, l2);
            }
            case 774: {
                return WindowsEventDebug.printMessage("WM_RENDERALLFORMATS", l, l2);
            }
            case 775: {
                return WindowsEventDebug.printMessage("WM_DESTROYCLIPBOARD", l, l2);
            }
            case 776: {
                return WindowsEventDebug.printMessage("WM_DRAWCLIPBOARD", l, l2);
            }
            case 777: {
                return WindowsEventDebug.printMessage("WM_PAINTCLIPBOARD", l, l2);
            }
            case 778: {
                return WindowsEventDebug.printMessage("WM_VSCROLLCLIPBOARD", l, l2);
            }
            case 779: {
                return WindowsEventDebug.printMessage("WM_SIZECLIPBOARD", l, l2);
            }
            case 780: {
                return WindowsEventDebug.printMessage("WM_ASKCBFORMATNAME", l, l2);
            }
            case 781: {
                return WindowsEventDebug.printMessage("WM_CHANGECBCHAIN", l, l2);
            }
            case 782: {
                return WindowsEventDebug.printMessage("WM_HSCROLLCLIPBOARD", l, l2);
            }
            case 783: {
                return WindowsEventDebug.printMessage("WM_QUERYNEWPALETTE", l, l2);
            }
            case 784: {
                return WindowsEventDebug.printMessage("WM_PALETTEISCHANGING", l, l2);
            }
            case 785: {
                return WindowsEventDebug.printMessage("WM_PALETTECHANGED", l, l2);
            }
            case 786: {
                return WindowsEventDebug.printMessage("WM_HOTKEY", l, l2);
            }
            case 791: {
                return WindowsEventDebug.printMessage("WM_PRINT", l, l2);
            }
            case 792: {
                return WindowsEventDebug.printMessage("WM_PRINTCLIENT", l, l2);
            }
            case 793: {
                return WindowsEventDebug.printMessage("WM_APPCOMMAND", l, l2);
            }
            case 794: {
                return WindowsEventDebug.printMessage("WM_THEMECHANGED", l, l2);
            }
            case 797: {
                return WindowsEventDebug.printMessage("WM_CLIPBOARDUPDATE", l, l2);
            }
            case 798: {
                return WindowsEventDebug.printMessage("WM_DWMCOMPOSITIONCHANGED", l, l2);
            }
            case 799: {
                return WindowsEventDebug.printMessage("WM_DWMNCRENDERINGCHANGED", l, l2);
            }
            case 800: {
                return WindowsEventDebug.printMessage("WM_DWMCOLORIZATIONCOLORCHANGED", l, l2);
            }
            case 801: {
                return WindowsEventDebug.printMessage("WM_DWMWINDOWMAXIMIZEDCHANGE", l, l2);
            }
            case 803: {
                return WindowsEventDebug.printMessage("WM_DWMSENDICONICTHUMBNAIL", l, l2);
            }
            case 806: {
                return WindowsEventDebug.printMessage("WM_DWMSENDICONICLIVEPREVIEWBITMAP", l, l2);
            }
            case 831: {
                return WindowsEventDebug.printMessage("WM_GETTITLEBARINFOEX", l, l2);
            }
            case 856: {
                return WindowsEventDebug.printMessage("WM_HANDHELDFIRST", l, l2);
            }
            case 863: {
                return WindowsEventDebug.printMessage("WM_HANDHELDLAST", l, l2);
            }
            case 864: {
                return WindowsEventDebug.printMessage("WM_AFXFIRST", l, l2);
            }
            case 895: {
                return WindowsEventDebug.printMessage("WM_AFXLAST", l, l2);
            }
            case 896: {
                return WindowsEventDebug.printMessage("WM_PENWINFIRST", l, l2);
            }
            case 911: {
                return WindowsEventDebug.printMessage("WM_PENWINLAST", l, l2);
            }
            case 32768: {
                return WindowsEventDebug.printMessage("WM_APP", l, l2);
            }
        }
        return WindowsEventDebug.printMessage("<UNKNOWN>", l, l2);
    }
}
