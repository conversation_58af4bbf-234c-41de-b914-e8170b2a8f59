/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ATIEnvmapBumpmap {
    public static final int GL_BUMP_ROT_MATRIX_ATI = 34677;
    public static final int GL_BUMP_ROT_MATRIX_SIZE_ATI = 34678;
    public static final int GL_BUMP_NUM_TEX_UNITS_ATI = 34679;
    public static final int GL_BUMP_TEX_UNITS_ATI = 34680;
    public static final int GL_DUDV_ATI = 34681;
    public static final int GL_DU8DV8_ATI = 34682;
    public static final int GL_BUMP_ENVMAP_ATI = 34683;
    public static final int GL_BUMP_TARGET_ATI = 34684;

    private ATIEnvmapBumpmap() {
    }

    public static void glTexBumpParameterATI(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexBumpParameterfvATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ATIEnvmapBumpmap.nglTexBumpParameterfvATI(n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglTexBumpParameterfvATI(int var0, long var1, long var3);

    public static void glTexBumpParameterATI(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexBumpParameterivATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        ATIEnvmapBumpmap.nglTexBumpParameterivATI(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglTexBumpParameterivATI(int var0, long var1, long var3);

    public static void glGetTexBumpParameterATI(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexBumpParameterfvATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ATIEnvmapBumpmap.nglGetTexBumpParameterfvATI(n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetTexBumpParameterfvATI(int var0, long var1, long var3);

    public static void glGetTexBumpParameterATI(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexBumpParameterivATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        ATIEnvmapBumpmap.nglGetTexBumpParameterivATI(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetTexBumpParameterivATI(int var0, long var1, long var3);
}
