/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.FloatBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.StateTracker;

public final class EXTVertexWeighting {
    public static final int GL_MODELVIEW0_STACK_DEPTH_EXT = 2979;
    public static final int GL_MODELVIEW1_STACK_DEPTH_EXT = 34050;
    public static final int GL_MODELVIEW0_MATRIX_EXT = 2982;
    public static final int GL_MODELVIEW1_MATRIX_EXT = 34054;
    public static final int GL_VERTEX_WEIGHTING_EXT = 34057;
    public static final int GL_MODELVIEW0_EXT = 5888;
    public static final int GL_MODELVIEW1_EXT = 34058;
    public static final int GL_CURRENT_VERTEX_WEIGHT_EXT = 34059;
    public static final int GL_VERTEX_WEIGHT_ARRAY_EXT = 34060;
    public static final int GL_VERTEX_WEIGHT_ARRAY_SIZE_EXT = 34061;
    public static final int GL_VERTEX_WEIGHT_ARRAY_TYPE_EXT = 34062;
    public static final int GL_VERTEX_WEIGHT_ARRAY_STRIDE_EXT = 34063;
    public static final int GL_VERTEX_WEIGHT_ARRAY_POINTER_EXT = 34064;

    private EXTVertexWeighting() {
    }

    public static void glVertexWeightfEXT(float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexWeightfEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexWeighting.nglVertexWeightfEXT(f, l);
    }

    static native void nglVertexWeightfEXT(float var0, long var1);

    public static void glVertexWeightPointerEXT(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexWeightPointerEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).EXT_vertex_weighting_glVertexWeightPointerEXT_pPointer = floatBuffer;
        }
        EXTVertexWeighting.nglVertexWeightPointerEXT(n, 5126, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglVertexWeightPointerEXT(int var0, int var1, int var2, long var3, long var5);

    public static void glVertexWeightPointerEXT(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glVertexWeightPointerEXT;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        EXTVertexWeighting.nglVertexWeightPointerEXTBO(n, n2, n3, l, l2);
    }

    static native void nglVertexWeightPointerEXTBO(int var0, int var1, int var2, long var3, long var5);
}
