/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class APPLEVertexArrayRange {
    public static final int GL_VERTEX_ARRAY_RANGE_APPLE = 34077;
    public static final int GL_VERTEX_ARRAY_RANGE_LENGTH_APPLE = 34078;
    public static final int GL_MAX_VERTEX_ARRAY_RANGE_ELEMENT_APPLE = 34080;
    public static final int GL_VERTEX_ARRAY_RANGE_POINTER_APPLE = 34081;
    public static final int GL_VERTEX_ARRAY_STORAGE_HINT_APPLE = 34079;
    public static final int GL_STORAGE_CACHED_APPLE = 34238;
    public static final int GL_STORAGE_SHARED_APPLE = 34239;
    public static final int GL_DRAW_PIXELS_APPLE = 35338;
    public static final int GL_FENCE_APPLE = 35339;

    private APPLEVertexArrayRange() {
    }

    public static void glVertexArrayRangeAPPLE(ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexArrayRangeAPPLE;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        APPLEVertexArrayRange.nglVertexArrayRangeAPPLE(byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglVertexArrayRangeAPPLE(int var0, long var1, long var3);

    public static void glFlushVertexArrayRangeAPPLE(ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFlushVertexArrayRangeAPPLE;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        APPLEVertexArrayRange.nglFlushVertexArrayRangeAPPLE(byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglFlushVertexArrayRangeAPPLE(int var0, long var1, long var3);

    public static void glVertexArrayParameteriAPPLE(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexArrayParameteriAPPLE;
        BufferChecks.checkFunctionAddress(l);
        APPLEVertexArrayRange.nglVertexArrayParameteriAPPLE(n, n2, l);
    }

    static native void nglVertexArrayParameteriAPPLE(int var0, int var1, long var2);
}
