/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.References;

class ReferencesStack {
    private References[] references_stack;
    private int stack_pos;

    public References getReferences() {
        return this.references_stack[this.stack_pos];
    }

    public void pushState() {
        int n = ++this.stack_pos;
        if (this.stack_pos == this.references_stack.length) {
            this.growStack();
        }
        this.references_stack[n].copy(this.references_stack[n - 1], -1);
    }

    public References popState(int n) {
        References references = this.references_stack[this.stack_pos--];
        this.references_stack[this.stack_pos].copy(references, ~n);
        references.clear();
        return references;
    }

    private void growStack() {
        References[] referencesArray = new References[this.references_stack.length + 1];
        System.arraycopy(this.references_stack, 0, referencesArray, 0, this.references_stack.length);
        this.references_stack = referencesArray;
        this.references_stack[this.references_stack.length - 1] = new References(GLContext.getCapabilities());
    }

    ReferencesStack() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        this.references_stack = new References[1];
        this.stack_pos = 0;
        for (int i = 0; i < this.references_stack.length; ++i) {
            this.references_stack[i] = new References(contextCapabilities);
        }
    }
}
