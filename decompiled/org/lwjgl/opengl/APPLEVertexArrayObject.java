/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class APPLEVertexArrayObject {
    public static final int GL_VERTEX_ARRAY_BINDING_APPLE = 34229;

    private APPLEVertexArrayObject() {
    }

    public static void glBindVertexArrayAPPLE(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindVertexArrayAPPLE;
        BufferChecks.checkFunctionAddress(l);
        APPLEVertexArrayObject.nglBindVertexArrayAPPLE(n, l);
    }

    static native void nglBindVertexArrayAPPLE(int var0, long var1);

    public static void glDeleteVertexArraysAPPLE(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteVertexArraysAPPLE;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        APPLEVertexArrayObject.nglDeleteVertexArraysAPPLE(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeleteVertexArraysAPPLE(int var0, long var1, long var3);

    public static void glDeleteVertexArraysAPPLE(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteVertexArraysAPPLE;
        BufferChecks.checkFunctionAddress(l);
        APPLEVertexArrayObject.nglDeleteVertexArraysAPPLE(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static void glGenVertexArraysAPPLE(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenVertexArraysAPPLE;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        APPLEVertexArrayObject.nglGenVertexArraysAPPLE(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenVertexArraysAPPLE(int var0, long var1, long var3);

    public static int glGenVertexArraysAPPLE() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenVertexArraysAPPLE;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        APPLEVertexArrayObject.nglGenVertexArraysAPPLE(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static boolean glIsVertexArrayAPPLE(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsVertexArrayAPPLE;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = APPLEVertexArrayObject.nglIsVertexArrayAPPLE(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsVertexArrayAPPLE(int var0, long var1);
}
