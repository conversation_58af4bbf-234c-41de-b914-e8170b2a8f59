/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVTransformFeedback {
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_NV = 35982;
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_START_NV = 35972;
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_SIZE_NV = 35973;
    public static final int GL_TRANSFORM_FEEDBACK_RECORD_NV = 35974;
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_BINDING_NV = 35983;
    public static final int GL_INTERLEAVED_ATTRIBS_NV = 35980;
    public static final int GL_SEPARATE_ATTRIBS_NV = 35981;
    public static final int GL_PRIMITIVES_GENERATED_NV = 35975;
    public static final int GL_TRANSFORM_FEEDBACK_PRIMITIVES_WRITTEN_NV = 35976;
    public static final int GL_RASTERIZER_DISCARD_NV = 35977;
    public static final int GL_MAX_TRANSFORM_FEEDBACK_INTERLEAVED_COMPONENTS_NV = 35978;
    public static final int GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_ATTRIBS_NV = 35979;
    public static final int GL_MAX_TRANSFORM_FEEDBACK_SEPARATE_COMPONENTS_NV = 35968;
    public static final int GL_TRANSFORM_FEEDBACK_ATTRIBS_NV = 35966;
    public static final int GL_ACTIVE_VARYINGS_NV = 35969;
    public static final int GL_ACTIVE_VARYING_MAX_LENGTH_NV = 35970;
    public static final int GL_TRANSFORM_FEEDBACK_VARYINGS_NV = 35971;
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_MODE_NV = 35967;
    public static final int GL_BACK_PRIMARY_COLOR_NV = 35959;
    public static final int GL_BACK_SECONDARY_COLOR_NV = 35960;
    public static final int GL_TEXTURE_COORD_NV = 35961;
    public static final int GL_CLIP_DISTANCE_NV = 35962;
    public static final int GL_VERTEX_ID_NV = 35963;
    public static final int GL_PRIMITIVE_ID_NV = 35964;
    public static final int GL_GENERIC_ATTRIB_NV = 35965;
    public static final int GL_LAYER_NV = 36266;

    private NVTransformFeedback() {
    }

    public static void glBindBufferRangeNV(int n, int n2, int n3, long l, long l2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glBindBufferRangeNV;
        BufferChecks.checkFunctionAddress(l3);
        NVTransformFeedback.nglBindBufferRangeNV(n, n2, n3, l, l2, l3);
    }

    static native void nglBindBufferRangeNV(int var0, int var1, int var2, long var3, long var5, long var7);

    public static void glBindBufferOffsetNV(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glBindBufferOffsetNV;
        BufferChecks.checkFunctionAddress(l2);
        NVTransformFeedback.nglBindBufferOffsetNV(n, n2, n3, l, l2);
    }

    static native void nglBindBufferOffsetNV(int var0, int var1, int var2, long var3, long var5);

    public static void glBindBufferBaseNV(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindBufferBaseNV;
        BufferChecks.checkFunctionAddress(l);
        NVTransformFeedback.nglBindBufferBaseNV(n, n2, n3, l);
    }

    static native void nglBindBufferBaseNV(int var0, int var1, int var2, long var3);

    public static void glTransformFeedbackAttribsNV(IntBuffer intBuffer, int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTransformFeedbackAttribsNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 3);
        NVTransformFeedback.nglTransformFeedbackAttribsNV(intBuffer.remaining() / 3, MemoryUtil.getAddress(intBuffer), n, l);
    }

    static native void nglTransformFeedbackAttribsNV(int var0, long var1, int var3, long var4);

    public static void glTransformFeedbackVaryingsNV(int n, IntBuffer intBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTransformFeedbackVaryingsNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        NVTransformFeedback.nglTransformFeedbackVaryingsNV(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), n2, l);
    }

    static native void nglTransformFeedbackVaryingsNV(int var0, int var1, long var2, int var4, long var5);

    public static void glBeginTransformFeedbackNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBeginTransformFeedbackNV;
        BufferChecks.checkFunctionAddress(l);
        NVTransformFeedback.nglBeginTransformFeedbackNV(n, l);
    }

    static native void nglBeginTransformFeedbackNV(int var0, long var1);

    public static void glEndTransformFeedbackNV() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEndTransformFeedbackNV;
        BufferChecks.checkFunctionAddress(l);
        NVTransformFeedback.nglEndTransformFeedbackNV(l);
    }

    static native void nglEndTransformFeedbackNV(long var0);

    public static int glGetVaryingLocationNV(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVaryingLocationNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = NVTransformFeedback.nglGetVaryingLocationNV(n, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglGetVaryingLocationNV(int var0, long var1, long var3);

    public static int glGetVaryingLocationNV(int n, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVaryingLocationNV;
        BufferChecks.checkFunctionAddress(l);
        n = NVTransformFeedback.nglGetVaryingLocationNV(n, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }

    public static void glGetActiveVaryingNV(int n, int n2, IntBuffer intBuffer, IntBuffer intBuffer2, IntBuffer intBuffer3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveVaryingNV;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkBuffer(intBuffer2, 1);
        BufferChecks.checkBuffer(intBuffer3, 1);
        BufferChecks.checkDirect(byteBuffer);
        NVTransformFeedback.nglGetActiveVaryingNV(n, n2, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(intBuffer3), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetActiveVaryingNV(int var0, int var1, int var2, long var3, long var5, long var7, long var9, long var11);

    public static String glGetActiveVaryingNV(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveVaryingNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 2);
        IntBuffer intBuffer2 = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        IntBuffer intBuffer3 = intBuffer;
        NVTransformFeedback.nglGetActiveVaryingNV(n, n2, n3, MemoryUtil.getAddress0(intBuffer2), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer3, intBuffer3.position() + 1), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer2.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static String glGetActiveVaryingNV(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveVaryingNV;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        NVTransformFeedback.nglGetActiveVaryingNV(n, n2, n3, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress0(APIUtil.getBufferInt(contextCapabilities)), MemoryUtil.getAddress(APIUtil.getBufferInt(contextCapabilities), 1), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static int glGetActiveVaryingSizeNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveVaryingNV;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getBufferInt(contextCapabilities);
        NVTransformFeedback.nglGetActiveVaryingNV(n, n2, 0, 0L, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer, 1), APIUtil.getBufferByte0(contextCapabilities), l);
        return intBuffer.get(0);
    }

    public static int glGetActiveVaryingTypeNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveVaryingNV;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getBufferInt(contextCapabilities);
        NVTransformFeedback.nglGetActiveVaryingNV(n, n2, 0, 0L, MemoryUtil.getAddress(intBuffer, 1), MemoryUtil.getAddress(intBuffer), APIUtil.getBufferByte0(contextCapabilities), l);
        return intBuffer.get(0);
    }

    public static void glActiveVaryingNV(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glActiveVaryingNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        NVTransformFeedback.nglActiveVaryingNV(n, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglActiveVaryingNV(int var0, long var1, long var3);

    public static void glActiveVaryingNV(int n, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glActiveVaryingNV;
        BufferChecks.checkFunctionAddress(l);
        NVTransformFeedback.nglActiveVaryingNV(n, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
    }

    public static void glGetTransformFeedbackVaryingNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTransformFeedbackVaryingNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        NVTransformFeedback.nglGetTransformFeedbackVaryingNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetTransformFeedbackVaryingNV(int var0, int var1, long var2, long var4);

    public static int glGetTransformFeedbackVaryingNV(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTransformFeedbackVaryingNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        NVTransformFeedback.nglGetTransformFeedbackVaryingNV(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }
}
