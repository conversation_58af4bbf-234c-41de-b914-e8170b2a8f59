/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.Buffer;
import java.nio.FloatBuffer;
import org.lwjgl.BufferUtils;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.OpenGLException;
import org.lwjgl.opengl.StateTracker;

class GLChecks {
    private GLChecks() {
    }

    static void ensureArrayVBOdisabled(ContextCapabilities contextCapabilities) {
        if (LWJGLUtil.CHECKS && StateTracker.getReferences((ContextCapabilities)contextCapabilities).arrayBuffer != 0) {
            throw new OpenGLException("Cannot use Buffers when Array Buffer Object is enabled");
        }
    }

    static void ensureArrayVBOenabled(ContextCapabilities contextCapabilities) {
        if (LWJGLUtil.CHECKS && StateTracker.getReferences((ContextCapabilities)contextCapabilities).arrayBuffer == 0) {
            throw new OpenGLException("Cannot use offsets when Array Buffer Object is disabled");
        }
    }

    static void ensureElementVBOdisabled(ContextCapabilities contextCapabilities) {
        if (LWJGLUtil.CHECKS && StateTracker.getElementArrayBufferBound(contextCapabilities) != 0) {
            throw new OpenGLException("Cannot use Buffers when Element Array Buffer Object is enabled");
        }
    }

    static void ensureElementVBOenabled(ContextCapabilities contextCapabilities) {
        if (LWJGLUtil.CHECKS && StateTracker.getElementArrayBufferBound(contextCapabilities) == 0) {
            throw new OpenGLException("Cannot use offsets when Element Array Buffer Object is disabled");
        }
    }

    static void ensureIndirectBOdisabled(ContextCapabilities contextCapabilities) {
        if (LWJGLUtil.CHECKS && StateTracker.getReferences((ContextCapabilities)contextCapabilities).indirectBuffer != 0) {
            throw new OpenGLException("Cannot use Buffers when Draw Indirect Object is enabled");
        }
    }

    static void ensureIndirectBOenabled(ContextCapabilities contextCapabilities) {
        if (LWJGLUtil.CHECKS && StateTracker.getReferences((ContextCapabilities)contextCapabilities).indirectBuffer == 0) {
            throw new OpenGLException("Cannot use offsets when Draw Indirect Object is disabled");
        }
    }

    static void ensurePackPBOdisabled(ContextCapabilities contextCapabilities) {
        if (LWJGLUtil.CHECKS && StateTracker.getReferences((ContextCapabilities)contextCapabilities).pixelPackBuffer != 0) {
            throw new OpenGLException("Cannot use Buffers when Pixel Pack Buffer Object is enabled");
        }
    }

    static void ensurePackPBOenabled(ContextCapabilities contextCapabilities) {
        if (LWJGLUtil.CHECKS && StateTracker.getReferences((ContextCapabilities)contextCapabilities).pixelPackBuffer == 0) {
            throw new OpenGLException("Cannot use offsets when Pixel Pack Buffer Object is disabled");
        }
    }

    static void ensureUnpackPBOdisabled(ContextCapabilities contextCapabilities) {
        if (LWJGLUtil.CHECKS && StateTracker.getReferences((ContextCapabilities)contextCapabilities).pixelUnpackBuffer != 0) {
            throw new OpenGLException("Cannot use Buffers when Pixel Unpack Buffer Object is enabled");
        }
    }

    static void ensureUnpackPBOenabled(ContextCapabilities contextCapabilities) {
        if (LWJGLUtil.CHECKS && StateTracker.getReferences((ContextCapabilities)contextCapabilities).pixelUnpackBuffer == 0) {
            throw new OpenGLException("Cannot use offsets when Pixel Unpack Buffer Object is disabled");
        }
    }

    static int calculateImageStorage(Buffer buffer, int n, int n2, int n3, int n4, int n5) {
        if (LWJGLUtil.CHECKS) {
            return GLChecks.calculateImageStorage(n, n2, n3, n4, n5) >> BufferUtils.getElementSizeExponent(buffer);
        }
        return 0;
    }

    static int calculateTexImage1DStorage(Buffer buffer, int n, int n2, int n3) {
        if (LWJGLUtil.CHECKS) {
            return GLChecks.calculateTexImage1DStorage(n, n2, n3) >> BufferUtils.getElementSizeExponent(buffer);
        }
        return 0;
    }

    static int calculateTexImage2DStorage(Buffer buffer, int n, int n2, int n3, int n4) {
        if (LWJGLUtil.CHECKS) {
            return GLChecks.calculateTexImage2DStorage(n, n2, n3, n4) >> BufferUtils.getElementSizeExponent(buffer);
        }
        return 0;
    }

    static int calculateTexImage3DStorage(Buffer buffer, int n, int n2, int n3, int n4, int n5) {
        if (LWJGLUtil.CHECKS) {
            return GLChecks.calculateTexImage3DStorage(n, n2, n3, n4, n5) >> BufferUtils.getElementSizeExponent(buffer);
        }
        return 0;
    }

    private static int calculateImageStorage(int n, int n2, int n3, int n4, int n5) {
        return GLChecks.calculateBytesPerPixel(n, n2) * n3 * n4 * n5;
    }

    private static int calculateTexImage1DStorage(int n, int n2, int n3) {
        return GLChecks.calculateBytesPerPixel(n, n2) * n3;
    }

    private static int calculateTexImage2DStorage(int n, int n2, int n3, int n4) {
        return GLChecks.calculateTexImage1DStorage(n, n2, n3) * n4;
    }

    private static int calculateTexImage3DStorage(int n, int n2, int n3, int n4, int n5) {
        return GLChecks.calculateTexImage2DStorage(n, n2, n3, n4) * n5;
    }

    private static int calculateBytesPerPixel(int n, int n2) {
        switch (n2) {
            case 5120: 
            case 5121: {
                n2 = 1;
                break;
            }
            case 5122: 
            case 5123: {
                n2 = 2;
                break;
            }
            case 5124: 
            case 5125: 
            case 5126: {
                n2 = 4;
                break;
            }
            default: {
                return 0;
            }
        }
        switch (n) {
            case 6406: 
            case 6409: {
                n = 1;
                break;
            }
            case 6410: {
                n = 2;
                break;
            }
            case 6407: 
            case 32992: {
                n = 3;
                break;
            }
            case 6408: 
            case 32768: 
            case 32993: {
                n = 4;
                break;
            }
            default: {
                return 0;
            }
        }
        return n2 * n;
    }

    static int calculateBytesPerCharCode(int n) {
        switch (n) {
            case 5121: 
            case 37018: {
                return 1;
            }
            case 5123: 
            case 5127: 
            case 37019: {
                return 2;
            }
            case 5128: {
                return 3;
            }
            case 5129: {
                return 4;
            }
        }
        throw new IllegalArgumentException("Unsupported charcode type: " + n);
    }

    static int calculateBytesPerPathName(int n) {
        switch (n) {
            case 5120: 
            case 5121: 
            case 37018: {
                return 1;
            }
            case 5122: 
            case 5123: 
            case 5127: 
            case 37019: {
                return 2;
            }
            case 5128: {
                return 3;
            }
            case 5124: 
            case 5125: 
            case 5126: 
            case 5129: {
                return 4;
            }
        }
        throw new IllegalArgumentException("Unsupported path name type: " + n);
    }

    static int calculateTransformPathValues(int n) {
        switch (n) {
            case 0: {
                return 0;
            }
            case 37006: 
            case 37007: {
                return 1;
            }
            case 37008: {
                return 2;
            }
            case 37009: {
                return 3;
            }
            case 37010: 
            case 37014: {
                return 6;
            }
            case 37012: 
            case 37016: {
                return 12;
            }
        }
        throw new IllegalArgumentException("Unsupported transform type: " + n);
    }

    static int calculatePathColorGenCoeffsCount(int n, int n2) {
        n = GLChecks.calculatePathGenCoeffsPerComponent(n);
        switch (n2) {
            case 6407: {
                return 3 * n;
            }
            case 6408: {
                return 4 * n;
            }
        }
        return n;
    }

    static int calculatePathTextGenCoeffsPerComponent(FloatBuffer floatBuffer, int n) {
        if (n == 0) {
            return 0;
        }
        return floatBuffer.remaining() / GLChecks.calculatePathGenCoeffsPerComponent(n);
    }

    private static int calculatePathGenCoeffsPerComponent(int n) {
        switch (n) {
            case 0: {
                return 0;
            }
            case 9217: 
            case 37002: {
                return 3;
            }
            case 9216: {
                return 4;
            }
        }
        throw new IllegalArgumentException("Unsupported gen mode: " + n);
    }

    static int calculateMetricsSize(int n, int n2) {
        if (LWJGLUtil.DEBUG && (n2 < 0 || n2 % 4 != 0)) {
            throw new IllegalArgumentException("Invalid stride value: " + n2);
        }
        n = Integer.bitCount(n);
        if (LWJGLUtil.DEBUG && n2 >> 2 < n) {
            throw new IllegalArgumentException("The queried metrics do not fit in the specified stride: " + n2);
        }
        if (n2 == 0) {
            return n;
        }
        return n2 >> 2;
    }
}
