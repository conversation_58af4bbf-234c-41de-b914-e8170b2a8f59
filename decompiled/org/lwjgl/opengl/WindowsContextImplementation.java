/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.opengl.ContextGL;
import org.lwjgl.opengl.ContextImplementation;
import org.lwjgl.opengl.PeerInfo;
import org.lwjgl.opengl.Util;

final class WindowsContextImplementation
implements ContextImplementation {
    WindowsContextImplementation() {
    }

    public final ByteBuffer create(PeerInfo peerInfo, IntBuffer buffer, ByteBuffer byteBuffer) {
        ByteBuffer byteBuffer2 = peerInfo.lockAndGetHandle();
        try {
            buffer = WindowsContextImplementation.nCreate(byteBuffer2, buffer, byteBuffer);
            return buffer;
        }
        finally {
            peerInfo.unlock();
        }
    }

    private static native ByteBuffer nCreate(ByteBuffer var0, IntBuffer var1, ByteBuffer var2);

    final native long getHGLRC(ByteBuffer var1);

    final native long getHDC(ByteBuffer var1);

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public final void swapBuffers() {
        Object object = ContextGL.getCurrentContext();
        if (object == null) {
            throw new IllegalStateException("No context is current");
        }
        ContextGL contextGL = object;
        synchronized (contextGL) {
            object = ((ContextGL)object).getPeerInfo();
            ByteBuffer byteBuffer = ((PeerInfo)object).lockAndGetHandle();
            try {
                WindowsContextImplementation.nSwapBuffers(byteBuffer);
            }
            finally {
                ((PeerInfo)object).unlock();
            }
            return;
        }
    }

    private static native void nSwapBuffers(ByteBuffer var0);

    public final void releaseDrawable(ByteBuffer byteBuffer) {
    }

    public final void update(ByteBuffer byteBuffer) {
    }

    public final void releaseCurrentContext() {
        WindowsContextImplementation.nReleaseCurrentContext();
    }

    private static native void nReleaseCurrentContext();

    public final void makeCurrent(PeerInfo peerInfo, ByteBuffer byteBuffer) {
        ByteBuffer byteBuffer2 = peerInfo.lockAndGetHandle();
        try {
            WindowsContextImplementation.nMakeCurrent(byteBuffer2, byteBuffer);
            return;
        }
        finally {
            peerInfo.unlock();
        }
    }

    private static native void nMakeCurrent(ByteBuffer var0, ByteBuffer var1);

    public final boolean isCurrent(ByteBuffer byteBuffer) {
        boolean bl = WindowsContextImplementation.nIsCurrent(byteBuffer);
        return bl;
    }

    private static native boolean nIsCurrent(ByteBuffer var0);

    public final void setSwapInterval(int n) {
        boolean bl = WindowsContextImplementation.nSetSwapInterval(n);
        n = bl ? 1 : 0;
        if (!bl) {
            LWJGLUtil.log("Failed to set swap interval");
        }
        Util.checkGLError();
    }

    private static native boolean nSetSwapInterval(int var0);

    public final void destroy(PeerInfo peerInfo, ByteBuffer byteBuffer) {
        WindowsContextImplementation.nDestroy(byteBuffer);
    }

    private static native void nDestroy(ByteBuffer var0);
}
