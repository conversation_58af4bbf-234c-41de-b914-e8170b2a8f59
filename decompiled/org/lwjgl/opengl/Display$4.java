/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.security.PrivilegedAction;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
static final class Display.4
implements PrivilegedAction<Object> {
    Display.4() {
    }

    @Override
    public final Object run() {
        Runtime.getRuntime().addShutdownHook(shutdown_hook);
        return null;
    }
}
