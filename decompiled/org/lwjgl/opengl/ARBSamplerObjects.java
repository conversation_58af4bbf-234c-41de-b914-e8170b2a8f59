/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import org.lwjgl.opengl.GL33;

public final class ARBSamplerObjects {
    public static final int GL_SAMPLER_BINDING = 35097;

    private ARBSamplerObjects() {
    }

    public static void glGenSamplers(IntBuffer intBuffer) {
        GL33.glGenSamplers(intBuffer);
    }

    public static int glGenSamplers() {
        return GL33.glGenSamplers();
    }

    public static void glDeleteSamplers(IntBuffer intBuffer) {
        GL33.glDeleteSamplers(intBuffer);
    }

    public static void glDeleteSamplers(int n) {
        GL33.glDeleteSamplers(n);
    }

    public static boolean glIsSampler(int n) {
        return GL33.glIsSampler(n);
    }

    public static void glBindSampler(int n, int n2) {
        GL33.glBindSampler(n, n2);
    }

    public static void glSamplerParameteri(int n, int n2, int n3) {
        GL33.glSamplerParameteri(n, n2, n3);
    }

    public static void glSamplerParameterf(int n, int n2, float f) {
        GL33.glSamplerParameterf(n, n2, f);
    }

    public static void glSamplerParameter(int n, int n2, IntBuffer intBuffer) {
        GL33.glSamplerParameter(n, n2, intBuffer);
    }

    public static void glSamplerParameter(int n, int n2, FloatBuffer floatBuffer) {
        GL33.glSamplerParameter(n, n2, floatBuffer);
    }

    public static void glSamplerParameterI(int n, int n2, IntBuffer intBuffer) {
        GL33.glSamplerParameterI(n, n2, intBuffer);
    }

    public static void glSamplerParameterIu(int n, int n2, IntBuffer intBuffer) {
        GL33.glSamplerParameterIu(n, n2, intBuffer);
    }

    public static void glGetSamplerParameter(int n, int n2, IntBuffer intBuffer) {
        GL33.glGetSamplerParameter(n, n2, intBuffer);
    }

    public static int glGetSamplerParameteri(int n, int n2) {
        return GL33.glGetSamplerParameteri(n, n2);
    }

    public static void glGetSamplerParameter(int n, int n2, FloatBuffer floatBuffer) {
        GL33.glGetSamplerParameter(n, n2, floatBuffer);
    }

    public static float glGetSamplerParameterf(int n, int n2) {
        return GL33.glGetSamplerParameterf(n, n2);
    }

    public static void glGetSamplerParameterI(int n, int n2, IntBuffer intBuffer) {
        GL33.glGetSamplerParameterI(n, n2, intBuffer);
    }

    public static int glGetSamplerParameterIi(int n, int n2) {
        return GL33.glGetSamplerParameterIi(n, n2);
    }

    public static void glGetSamplerParameterIu(int n, int n2, IntBuffer intBuffer) {
        GL33.glGetSamplerParameterIu(n, n2, intBuffer);
    }

    public static int glGetSamplerParameterIui(int n, int n2) {
        return GL33.glGetSamplerParameterIui(n, n2);
    }
}
