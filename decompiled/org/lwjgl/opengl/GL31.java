/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;

public final class GL31 {
    public static final int GL_RED_SNORM = 36752;
    public static final int GL_RG_SNORM = 36753;
    public static final int GL_RGB_SNORM = 36754;
    public static final int GL_RGBA_SNORM = 36755;
    public static final int GL_R8_SNORM = 36756;
    public static final int GL_RG8_SNORM = 36757;
    public static final int GL_RGB8_SNORM = 36758;
    public static final int GL_RGBA8_SNORM = 36759;
    public static final int GL_R16_SNORM = 36760;
    public static final int GL_RG16_SNORM = 36761;
    public static final int GL_RGB16_SNORM = 36762;
    public static final int GL_RGBA16_SNORM = 36763;
    public static final int GL_SIGNED_NORMALIZED = 36764;
    public static final int GL_COPY_READ_BUFFER_BINDING = 36662;
    public static final int GL_COPY_WRITE_BUFFER_BINDING = 36663;
    public static final int GL_COPY_READ_BUFFER = 36662;
    public static final int GL_COPY_WRITE_BUFFER = 36663;
    public static final int GL_PRIMITIVE_RESTART = 36765;
    public static final int GL_PRIMITIVE_RESTART_INDEX = 36766;
    public static final int GL_TEXTURE_BUFFER = 35882;
    public static final int GL_MAX_TEXTURE_BUFFER_SIZE = 35883;
    public static final int GL_TEXTURE_BINDING_BUFFER = 35884;
    public static final int GL_TEXTURE_BUFFER_DATA_STORE_BINDING = 35885;
    public static final int GL_TEXTURE_BUFFER_FORMAT = 35886;
    public static final int GL_TEXTURE_RECTANGLE = 34037;
    public static final int GL_TEXTURE_BINDING_RECTANGLE = 34038;
    public static final int GL_PROXY_TEXTURE_RECTANGLE = 34039;
    public static final int GL_MAX_RECTANGLE_TEXTURE_SIZE = 34040;
    public static final int GL_SAMPLER_2D_RECT = 35683;
    public static final int GL_SAMPLER_2D_RECT_SHADOW = 35684;
    public static final int GL_UNIFORM_BUFFER = 35345;
    public static final int GL_UNIFORM_BUFFER_BINDING = 35368;
    public static final int GL_UNIFORM_BUFFER_START = 35369;
    public static final int GL_UNIFORM_BUFFER_SIZE = 35370;
    public static final int GL_MAX_VERTEX_UNIFORM_BLOCKS = 35371;
    public static final int GL_MAX_GEOMETRY_UNIFORM_BLOCKS = 35372;
    public static final int GL_MAX_FRAGMENT_UNIFORM_BLOCKS = 35373;
    public static final int GL_MAX_COMBINED_UNIFORM_BLOCKS = 35374;
    public static final int GL_MAX_UNIFORM_BUFFER_BINDINGS = 35375;
    public static final int GL_MAX_UNIFORM_BLOCK_SIZE = 35376;
    public static final int GL_MAX_COMBINED_VERTEX_UNIFORM_COMPONENTS = 35377;
    public static final int GL_MAX_COMBINED_GEOMETRY_UNIFORM_COMPONENTS = 35378;
    public static final int GL_MAX_COMBINED_FRAGMENT_UNIFORM_COMPONENTS = 35379;
    public static final int GL_UNIFORM_BUFFER_OFFSET_ALIGNMENT = 35380;
    public static final int GL_ACTIVE_UNIFORM_BLOCK_MAX_NAME_LENGTH = 35381;
    public static final int GL_ACTIVE_UNIFORM_BLOCKS = 35382;
    public static final int GL_UNIFORM_TYPE = 35383;
    public static final int GL_UNIFORM_SIZE = 35384;
    public static final int GL_UNIFORM_NAME_LENGTH = 35385;
    public static final int GL_UNIFORM_BLOCK_INDEX = 35386;
    public static final int GL_UNIFORM_OFFSET = 35387;
    public static final int GL_UNIFORM_ARRAY_STRIDE = 35388;
    public static final int GL_UNIFORM_MATRIX_STRIDE = 35389;
    public static final int GL_UNIFORM_IS_ROW_MAJOR = 35390;
    public static final int GL_UNIFORM_BLOCK_BINDING = 35391;
    public static final int GL_UNIFORM_BLOCK_DATA_SIZE = 35392;
    public static final int GL_UNIFORM_BLOCK_NAME_LENGTH = 35393;
    public static final int GL_UNIFORM_BLOCK_ACTIVE_UNIFORMS = 35394;
    public static final int GL_UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES = 35395;
    public static final int GL_UNIFORM_BLOCK_REFERENCED_BY_VERTEX_SHADER = 35396;
    public static final int GL_UNIFORM_BLOCK_REFERENCED_BY_GEOMETRY_SHADER = 35397;
    public static final int GL_UNIFORM_BLOCK_REFERENCED_BY_FRAGMENT_SHADER = 35398;
    public static final int GL_INVALID_INDEX = -1;

    private GL31() {
    }

    public static void glDrawArraysInstanced(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawArraysInstanced;
        BufferChecks.checkFunctionAddress(l);
        GL31.nglDrawArraysInstanced(n, n2, n3, n4, l);
    }

    static native void nglDrawArraysInstanced(int var0, int var1, int var2, int var3, long var4);

    public static void glDrawElementsInstanced(int n, ByteBuffer byteBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsInstanced;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL31.nglDrawElementsInstanced(n, byteBuffer.remaining(), 5121, MemoryUtil.getAddress(byteBuffer), n2, l);
    }

    public static void glDrawElementsInstanced(int n, IntBuffer intBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsInstanced;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        GL31.nglDrawElementsInstanced(n, intBuffer.remaining(), 5125, MemoryUtil.getAddress(intBuffer), n2, l);
    }

    public static void glDrawElementsInstanced(int n, ShortBuffer shortBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsInstanced;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        GL31.nglDrawElementsInstanced(n, shortBuffer.remaining(), 5123, MemoryUtil.getAddress(shortBuffer), n2, l);
    }

    static native void nglDrawElementsInstanced(int var0, int var1, int var2, long var3, int var5, long var6);

    public static void glDrawElementsInstanced(int n, int n2, int n3, long l, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glDrawElementsInstanced;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureElementVBOenabled(contextCapabilities);
        GL31.nglDrawElementsInstancedBO(n, n2, n3, l, n4, l2);
    }

    static native void nglDrawElementsInstancedBO(int var0, int var1, int var2, long var3, int var5, long var6);

    public static void glCopyBufferSubData(int n, int n2, long l, long l2, long l3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l4 = contextCapabilities.glCopyBufferSubData;
        BufferChecks.checkFunctionAddress(l4);
        GL31.nglCopyBufferSubData(n, n2, l, l2, l3, l4);
    }

    static native void nglCopyBufferSubData(int var0, int var1, long var2, long var4, long var6, long var8);

    public static void glPrimitiveRestartIndex(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPrimitiveRestartIndex;
        BufferChecks.checkFunctionAddress(l);
        GL31.nglPrimitiveRestartIndex(n, l);
    }

    static native void nglPrimitiveRestartIndex(int var0, long var1);

    public static void glTexBuffer(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexBuffer;
        BufferChecks.checkFunctionAddress(l);
        GL31.nglTexBuffer(n, n2, n3, l);
    }

    static native void nglTexBuffer(int var0, int var1, int var2, long var3);

    public static void glGetUniformIndices(int n, ByteBuffer byteBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformIndices;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer, intBuffer.remaining());
        BufferChecks.checkDirect(intBuffer);
        GL31.nglGetUniformIndices(n, intBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetUniformIndices(int var0, int var1, long var2, long var4, long var6);

    public static void glGetUniformIndices(int n, CharSequence[] charSequenceArray, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformIndices;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkArray(charSequenceArray);
        BufferChecks.checkBuffer(intBuffer, charSequenceArray.length);
        GL31.nglGetUniformIndices(n, charSequenceArray.length, APIUtil.getBufferNT(contextCapabilities, charSequenceArray), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetActiveUniforms(int n, IntBuffer intBuffer, int n2, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniformsiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkBuffer(intBuffer2, intBuffer.remaining());
        GL31.nglGetActiveUniformsiv(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), n2, MemoryUtil.getAddress(intBuffer2), l);
    }

    static native void nglGetActiveUniformsiv(int var0, int var1, long var2, int var4, long var5, long var7);

    public static int glGetActiveUniforms(int n, int n2, int n3) {
        return GL31.glGetActiveUniformsi(n, n2, n3);
    }

    public static int glGetActiveUniformsi(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetActiveUniformsiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL31.nglGetActiveUniformsiv(n, 1, MemoryUtil.getAddress(((IntBuffer)object).put(1, n2), 1), n3, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetActiveUniformName(int n, int n2, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniformName;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(byteBuffer);
        GL31.nglGetActiveUniformName(n, n2, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetActiveUniformName(int var0, int var1, int var2, long var3, long var5, long var7);

    public static String glGetActiveUniformName(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniformName;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        GL31.nglGetActiveUniformName(n, n2, n3, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static int glGetUniformBlockIndex(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformBlockIndex;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = GL31.nglGetUniformBlockIndex(n, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglGetUniformBlockIndex(int var0, long var1, long var3);

    public static int glGetUniformBlockIndex(int n, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformBlockIndex;
        BufferChecks.checkFunctionAddress(l);
        n = GL31.nglGetUniformBlockIndex(n, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }

    public static void glGetActiveUniformBlock(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniformBlockiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 16);
        GL31.nglGetActiveUniformBlockiv(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetActiveUniformBlockiv(int var0, int var1, int var2, long var3, long var5);

    public static int glGetActiveUniformBlock(int n, int n2, int n3) {
        return GL31.glGetActiveUniformBlocki(n, n2, n3);
    }

    public static int glGetActiveUniformBlocki(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetActiveUniformBlockiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL31.nglGetActiveUniformBlockiv(n, n2, n3, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetActiveUniformBlockName(int n, int n2, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniformBlockName;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(byteBuffer);
        GL31.nglGetActiveUniformBlockName(n, n2, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetActiveUniformBlockName(int var0, int var1, int var2, long var3, long var5, long var7);

    public static String glGetActiveUniformBlockName(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniformBlockName;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        GL31.nglGetActiveUniformBlockName(n, n2, n3, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static void glUniformBlockBinding(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformBlockBinding;
        BufferChecks.checkFunctionAddress(l);
        GL31.nglUniformBlockBinding(n, n2, n3, l);
    }

    static native void nglUniformBlockBinding(int var0, int var1, int var2, long var3);
}
