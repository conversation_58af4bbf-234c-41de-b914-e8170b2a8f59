/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.opengl.ATIVertexArrayObject;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ATIMapObjectBuffer {
    private ATIMapObjectBuffer() {
    }

    public static ByteBuffer glMapObjectBufferATI(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMapObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        int n2 = n;
        ByteBuffer byteBuffer2 = ATIMapObjectBuffer.nglMapObjectBufferATI(n2, ATIVertexArrayObject.glGetObjectBufferiATI(n2, 34660), byteBuffer, l);
        if (LWJGLUtil.CHECKS && byteBuffer2 == null) {
            return null;
        }
        return byteBuffer2.order(ByteOrder.nativeOrder());
    }

    public static ByteBuffer glMapObjectBufferATI(int n, long l, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMapObjectBufferATI;
        BufferChecks.checkFunctionAddress(l2);
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        ByteBuffer byteBuffer2 = ATIMapObjectBuffer.nglMapObjectBufferATI(n, l, byteBuffer, l2);
        if (LWJGLUtil.CHECKS && byteBuffer2 == null) {
            return null;
        }
        return byteBuffer2.order(ByteOrder.nativeOrder());
    }

    static native ByteBuffer nglMapObjectBufferATI(int var0, long var1, ByteBuffer var3, long var4);

    public static void glUnmapObjectBufferATI(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUnmapObjectBufferATI;
        BufferChecks.checkFunctionAddress(l);
        ATIMapObjectBuffer.nglUnmapObjectBufferATI(n, l);
    }

    static native void nglUnmapObjectBufferATI(int var0, long var1);
}
