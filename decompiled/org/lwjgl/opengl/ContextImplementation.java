/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.opengl.PeerInfo;

interface ContextImplementation {
    public ByteBuffer create(PeerInfo var1, IntBuffer var2, ByteBuffer var3);

    public void swapBuffers();

    public void releaseDrawable(ByteBuffer var1);

    public void releaseCurrentContext();

    public void update(ByteBuffer var1);

    public void makeCurrent(PeerInfo var1, ByteBuffer var2);

    public boolean isCurrent(ByteBuffer var1);

    public void setSwapInterval(int var1);

    public void destroy(PeerInfo var1, ByteBuffer var2);
}
