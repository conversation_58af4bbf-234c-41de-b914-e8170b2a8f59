/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.LongBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.PointerBuffer;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class GL44 {
    public static final int GL_MAX_VERTEX_ATTRIB_STRIDE = 33509;
    public static final int GL_MAP_PERSISTENT_BIT = 64;
    public static final int GL_MAP_COHERENT_BIT = 128;
    public static final int GL_DYNAMIC_STORAGE_BIT = 256;
    public static final int GL_CLIENT_STORAGE_BIT = 512;
    public static final int GL_BUFFER_IMMUTABLE_STORAGE = 33311;
    public static final int GL_BUFFER_STORAGE_FLAGS = 33312;
    public static final int GL_CLIENT_MAPPED_BUFFER_BARRIER_BIT = 16384;
    public static final int GL_CLEAR_TEXTURE = 37733;
    public static final int GL_LOCATION_COMPONENT = 37706;
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_INDEX = 37707;
    public static final int GL_TRANSFORM_FEEDBACK_BUFFER_STRIDE = 37708;
    public static final int GL_QUERY_RESULT_NO_WAIT = 37268;
    public static final int GL_QUERY_BUFFER = 37266;
    public static final int GL_QUERY_BUFFER_BINDING = 37267;
    public static final int GL_QUERY_BUFFER_BARRIER_BIT = 32768;
    public static final int GL_MIRROR_CLAMP_TO_EDGE = 34627;

    private GL44() {
    }

    public static void glBufferStorage(int n, ByteBuffer byteBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBufferStorage;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        GL44.nglBufferStorage(n, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), n2, l);
    }

    public static void glBufferStorage(int n, DoubleBuffer doubleBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBufferStorage;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL44.nglBufferStorage(n, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), n2, l);
    }

    public static void glBufferStorage(int n, FloatBuffer floatBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBufferStorage;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL44.nglBufferStorage(n, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), n2, l);
    }

    public static void glBufferStorage(int n, IntBuffer intBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBufferStorage;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL44.nglBufferStorage(n, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), n2, l);
    }

    public static void glBufferStorage(int n, ShortBuffer shortBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBufferStorage;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        GL44.nglBufferStorage(n, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), n2, l);
    }

    public static void glBufferStorage(int n, LongBuffer longBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBufferStorage;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(longBuffer);
        GL44.nglBufferStorage(n, longBuffer.remaining() << 3, MemoryUtil.getAddress(longBuffer), n2, l);
    }

    static native void nglBufferStorage(int var0, long var1, long var3, int var5, long var6);

    public static void glBufferStorage(int n, long l, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glBufferStorage;
        BufferChecks.checkFunctionAddress(l2);
        GL44.nglBufferStorage(n, l, 0L, n2, l2);
    }

    public static void glClearTexImage(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearTexImage;
        BufferChecks.checkFunctionAddress(l);
        if (byteBuffer != null) {
            BufferChecks.checkBuffer(byteBuffer, 1);
        }
        GL44.nglClearTexImage(n, n2, n3, n4, MemoryUtil.getAddressSafe(byteBuffer), l);
    }

    public static void glClearTexImage(int n, int n2, int n3, int n4, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearTexImage;
        BufferChecks.checkFunctionAddress(l);
        if (doubleBuffer != null) {
            BufferChecks.checkBuffer(doubleBuffer, 1);
        }
        GL44.nglClearTexImage(n, n2, n3, n4, MemoryUtil.getAddressSafe(doubleBuffer), l);
    }

    public static void glClearTexImage(int n, int n2, int n3, int n4, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearTexImage;
        BufferChecks.checkFunctionAddress(l);
        if (floatBuffer != null) {
            BufferChecks.checkBuffer(floatBuffer, 1);
        }
        GL44.nglClearTexImage(n, n2, n3, n4, MemoryUtil.getAddressSafe(floatBuffer), l);
    }

    public static void glClearTexImage(int n, int n2, int n3, int n4, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearTexImage;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        GL44.nglClearTexImage(n, n2, n3, n4, MemoryUtil.getAddressSafe(intBuffer), l);
    }

    public static void glClearTexImage(int n, int n2, int n3, int n4, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearTexImage;
        BufferChecks.checkFunctionAddress(l);
        if (shortBuffer != null) {
            BufferChecks.checkBuffer(shortBuffer, 1);
        }
        GL44.nglClearTexImage(n, n2, n3, n4, MemoryUtil.getAddressSafe(shortBuffer), l);
    }

    public static void glClearTexImage(int n, int n2, int n3, int n4, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearTexImage;
        BufferChecks.checkFunctionAddress(l);
        if (longBuffer != null) {
            BufferChecks.checkBuffer(longBuffer, 1);
        }
        GL44.nglClearTexImage(n, n2, n3, n4, MemoryUtil.getAddressSafe(longBuffer), l);
    }

    static native void nglClearTexImage(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glClearTexSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearTexSubImage;
        BufferChecks.checkFunctionAddress(l);
        if (byteBuffer != null) {
            BufferChecks.checkBuffer(byteBuffer, 1);
        }
        GL44.nglClearTexSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, MemoryUtil.getAddressSafe(byteBuffer), l);
    }

    public static void glClearTexSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearTexSubImage;
        BufferChecks.checkFunctionAddress(l);
        if (doubleBuffer != null) {
            BufferChecks.checkBuffer(doubleBuffer, 1);
        }
        GL44.nglClearTexSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, MemoryUtil.getAddressSafe(doubleBuffer), l);
    }

    public static void glClearTexSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearTexSubImage;
        BufferChecks.checkFunctionAddress(l);
        if (floatBuffer != null) {
            BufferChecks.checkBuffer(floatBuffer, 1);
        }
        GL44.nglClearTexSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, MemoryUtil.getAddressSafe(floatBuffer), l);
    }

    public static void glClearTexSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearTexSubImage;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        GL44.nglClearTexSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, MemoryUtil.getAddressSafe(intBuffer), l);
    }

    public static void glClearTexSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearTexSubImage;
        BufferChecks.checkFunctionAddress(l);
        if (shortBuffer != null) {
            BufferChecks.checkBuffer(shortBuffer, 1);
        }
        GL44.nglClearTexSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, MemoryUtil.getAddressSafe(shortBuffer), l);
    }

    public static void glClearTexSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearTexSubImage;
        BufferChecks.checkFunctionAddress(l);
        if (longBuffer != null) {
            BufferChecks.checkBuffer(longBuffer, 1);
        }
        GL44.nglClearTexSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, MemoryUtil.getAddressSafe(longBuffer), l);
    }

    static native void nglClearTexSubImage(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9, long var10, long var12);

    public static void glBindBuffersBase(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindBuffersBase;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, n3);
        }
        GL44.nglBindBuffersBase(n, n2, n3, MemoryUtil.getAddressSafe(intBuffer), l);
    }

    static native void nglBindBuffersBase(int var0, int var1, int var2, long var3, long var5);

    public static void glBindBuffersRange(int n, int n2, int n3, IntBuffer intBuffer, PointerBuffer pointerBuffer, PointerBuffer pointerBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindBuffersRange;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, n3);
        }
        if (pointerBuffer != null) {
            BufferChecks.checkBuffer(pointerBuffer, n3);
        }
        if (pointerBuffer2 != null) {
            BufferChecks.checkBuffer(pointerBuffer2, n3);
        }
        GL44.nglBindBuffersRange(n, n2, n3, MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddressSafe(pointerBuffer), MemoryUtil.getAddressSafe(pointerBuffer2), l);
    }

    static native void nglBindBuffersRange(int var0, int var1, int var2, long var3, long var5, long var7, long var9);

    public static void glBindTextures(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindTextures;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, n2);
        }
        GL44.nglBindTextures(n, n2, MemoryUtil.getAddressSafe(intBuffer), l);
    }

    static native void nglBindTextures(int var0, int var1, long var2, long var4);

    public static void glBindSamplers(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindSamplers;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, n2);
        }
        GL44.nglBindSamplers(n, n2, MemoryUtil.getAddressSafe(intBuffer), l);
    }

    static native void nglBindSamplers(int var0, int var1, long var2, long var4);

    public static void glBindImageTextures(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindImageTextures;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, n2);
        }
        GL44.nglBindImageTextures(n, n2, MemoryUtil.getAddressSafe(intBuffer), l);
    }

    static native void nglBindImageTextures(int var0, int var1, long var2, long var4);

    public static void glBindVertexBuffers(int n, int n2, IntBuffer intBuffer, PointerBuffer pointerBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindVertexBuffers;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, n2);
        }
        if (pointerBuffer != null) {
            BufferChecks.checkBuffer(pointerBuffer, n2);
        }
        if (intBuffer2 != null) {
            BufferChecks.checkBuffer(intBuffer2, n2);
        }
        GL44.nglBindVertexBuffers(n, n2, MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddressSafe(pointerBuffer), MemoryUtil.getAddressSafe(intBuffer2), l);
    }

    static native void nglBindVertexBuffers(int var0, int var1, long var2, long var4, long var6, long var8);
}
