/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.opengl.GL40;

public final class ARBShaderSubroutine {
    public static final int GL_ACTIVE_SUBROUTINES = 36325;
    public static final int GL_ACTIVE_SUBROUTINE_UNIFORMS = 36326;
    public static final int GL_ACTIVE_SUBROUTINE_UNIFORM_LOCATIONS = 36423;
    public static final int GL_ACTIVE_SUBROUTINE_MAX_LENGTH = 36424;
    public static final int GL_ACTIVE_SUBROUTINE_UNIFORM_MAX_LENGTH = 36425;
    public static final int GL_MAX_SUBROUTINES = 36327;
    public static final int GL_MAX_SUBROUTINE_UNIFORM_LOCATIONS = 36328;
    public static final int GL_NUM_COMPATIBLE_SUBROUTINES = 36426;
    public static final int GL_COMPATIBLE_SUBROUTINES = 36427;

    private ARBShaderSubroutine() {
    }

    public static int glGetSubroutineUniformLocation(int n, int n2, ByteBuffer byteBuffer) {
        return GL40.glGetSubroutineUniformLocation(n, n2, byteBuffer);
    }

    public static int glGetSubroutineUniformLocation(int n, int n2, CharSequence charSequence) {
        return GL40.glGetSubroutineUniformLocation(n, n2, charSequence);
    }

    public static int glGetSubroutineIndex(int n, int n2, ByteBuffer byteBuffer) {
        return GL40.glGetSubroutineIndex(n, n2, byteBuffer);
    }

    public static int glGetSubroutineIndex(int n, int n2, CharSequence charSequence) {
        return GL40.glGetSubroutineIndex(n, n2, charSequence);
    }

    public static void glGetActiveSubroutineUniform(int n, int n2, int n3, int n4, IntBuffer intBuffer) {
        GL40.glGetActiveSubroutineUniform(n, n2, n3, n4, intBuffer);
    }

    public static int glGetActiveSubroutineUniformi(int n, int n2, int n3, int n4) {
        return GL40.glGetActiveSubroutineUniformi(n, n2, n3, n4);
    }

    public static void glGetActiveSubroutineUniformName(int n, int n2, int n3, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        GL40.glGetActiveSubroutineUniformName(n, n2, n3, intBuffer, byteBuffer);
    }

    public static String glGetActiveSubroutineUniformName(int n, int n2, int n3, int n4) {
        return GL40.glGetActiveSubroutineUniformName(n, n2, n3, n4);
    }

    public static void glGetActiveSubroutineName(int n, int n2, int n3, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        GL40.glGetActiveSubroutineName(n, n2, n3, intBuffer, byteBuffer);
    }

    public static String glGetActiveSubroutineName(int n, int n2, int n3, int n4) {
        return GL40.glGetActiveSubroutineName(n, n2, n3, n4);
    }

    public static void glUniformSubroutinesu(int n, IntBuffer intBuffer) {
        GL40.glUniformSubroutinesu(n, intBuffer);
    }

    public static void glGetUniformSubroutineu(int n, int n2, IntBuffer intBuffer) {
        GL40.glGetUniformSubroutineu(n, n2, intBuffer);
    }

    public static int glGetUniformSubroutineui(int n, int n2) {
        return GL40.glGetUniformSubroutineui(n, n2);
    }

    public static void glGetProgramStage(int n, int n2, int n3, IntBuffer intBuffer) {
        GL40.glGetProgramStage(n, n2, n3, intBuffer);
    }

    public static int glGetProgramStagei(int n, int n2, int n3) {
        return GL40.glGetProgramStagei(n, n2, n3);
    }
}
