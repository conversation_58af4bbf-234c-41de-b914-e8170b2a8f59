/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Component;
import java.awt.Point;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionListener;
import java.awt.event.MouseWheelEvent;
import java.awt.event.MouseWheelListener;
import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.opengl.AWTUtil;
import org.lwjgl.opengl.EventQueue;

class MouseEventQueue
extends EventQueue
implements MouseListener,
MouseMotionListener,
MouseWheelListener {
    private static final int WHEEL_SCALE = 120;
    public static final int NUM_BUTTONS = 3;
    private final Component component;
    private boolean grabbed;
    private int accum_dx;
    private int accum_dy;
    private int accum_dz;
    private int last_x;
    private int last_y;
    private boolean saved_control_state;
    private final ByteBuffer event = ByteBuffer.allocate(22);
    private final byte[] buttons = new byte[3];

    MouseEventQueue(Component component) {
        super(22);
        this.component = component;
    }

    public synchronized void register() {
        this.resetCursorToCenter();
        if (this.component != null) {
            this.component.addMouseListener(this);
            this.component.addMouseMotionListener(this);
            this.component.addMouseWheelListener(this);
        }
    }

    public synchronized void unregister() {
        if (this.component != null) {
            this.component.removeMouseListener(this);
            this.component.removeMouseMotionListener(this);
            this.component.removeMouseWheelListener(this);
        }
    }

    protected Component getComponent() {
        return this.component;
    }

    public synchronized void setGrabbed(boolean bl) {
        this.grabbed = bl;
        this.resetCursorToCenter();
    }

    public synchronized boolean isGrabbed() {
        return this.grabbed;
    }

    protected int transformY(int n) {
        if (this.component != null) {
            return this.component.getHeight() - 1 - n;
        }
        return n;
    }

    protected void resetCursorToCenter() {
        Point point;
        this.clearEvents();
        MouseEventQueue mouseEventQueue = this;
        mouseEventQueue.accum_dy = 0;
        mouseEventQueue.accum_dx = 0;
        if (this.component != null && (point = AWTUtil.getCursorPosition(this.component)) != null) {
            this.last_x = point.x;
            this.last_y = point.y;
        }
    }

    private void putMouseEvent(byte by, byte by2, int n, long l) {
        if (this.grabbed) {
            this.putMouseEventWithCoords(by, by2, 0, 0, n, l);
            return;
        }
        this.putMouseEventWithCoords(by, by2, this.last_x, this.last_y, n, l);
    }

    protected void putMouseEventWithCoords(byte by, byte by2, int n, int n2, int n3, long l) {
        this.event.clear();
        this.event.put(by).put(by2).putInt(n).putInt(n2).putInt(n3).putLong(l);
        this.event.flip();
        MouseEventQueue mouseEventQueue = this;
        mouseEventQueue.putEvent(mouseEventQueue.event);
    }

    public synchronized void poll(IntBuffer intBuffer, ByteBuffer byteBuffer) {
        if (this.grabbed) {
            intBuffer.put(0, this.accum_dx);
            intBuffer.put(1, this.accum_dy);
        } else {
            intBuffer.put(0, this.last_x);
            intBuffer.put(1, this.last_y);
        }
        intBuffer.put(2, this.accum_dz);
        MouseEventQueue mouseEventQueue = this;
        this.accum_dz = 0;
        mouseEventQueue.accum_dy = 0;
        mouseEventQueue.accum_dx = 0;
        int n = byteBuffer.position();
        byteBuffer.put(this.buttons, 0, this.buttons.length);
        byteBuffer.position(n);
    }

    private void setCursorPos(int n, int n2, long l) {
        n2 = this.transformY(n2);
        if (this.grabbed) {
            return;
        }
        int n3 = n - this.last_x;
        int n4 = n2 - this.last_y;
        this.addDelta(n3, n4);
        this.last_x = n;
        this.last_y = n2;
        this.putMouseEventWithCoords((byte)-1, (byte)0, n, n2, 0, l);
    }

    protected void addDelta(int n, int n2) {
        this.accum_dx += n;
        this.accum_dy += n2;
    }

    public void mouseClicked(MouseEvent mouseEvent) {
    }

    public void mouseEntered(MouseEvent mouseEvent) {
    }

    public void mouseExited(MouseEvent mouseEvent) {
    }

    private void handleButton(MouseEvent mouseEvent) {
        byte by;
        byte by2;
        switch (mouseEvent.getID()) {
            case 501: {
                by2 = 1;
                break;
            }
            case 502: {
                by2 = 0;
                break;
            }
            default: {
                throw new IllegalArgumentException("Not a valid event ID: " + mouseEvent.getID());
            }
        }
        switch (mouseEvent.getButton()) {
            case 0: {
                return;
            }
            case 1: {
                if (by2 == 1) {
                    this.saved_control_state = mouseEvent.isControlDown();
                }
                if (this.saved_control_state) {
                    if (this.buttons[1] == by2) {
                        return;
                    }
                    by = 1;
                    break;
                }
                by = 0;
                break;
            }
            case 2: {
                by = 2;
                break;
            }
            case 3: {
                if (this.buttons[1] == by2) {
                    return;
                }
                by = 1;
                break;
            }
            default: {
                throw new IllegalArgumentException("Not a valid button: " + mouseEvent.getButton());
            }
        }
        this.setButton(by, by2, mouseEvent.getWhen() * 1000000L);
    }

    public synchronized void mousePressed(MouseEvent mouseEvent) {
        this.handleButton(mouseEvent);
    }

    private void setButton(byte by, byte by2, long l) {
        this.buttons[by] = by2;
        this.putMouseEvent(by, by2, 0, l);
    }

    public synchronized void mouseReleased(MouseEvent mouseEvent) {
        this.handleButton(mouseEvent);
    }

    private void handleMotion(MouseEvent mouseEvent) {
        if (this.grabbed) {
            this.updateDeltas(mouseEvent.getWhen() * 1000000L);
            return;
        }
        this.setCursorPos(mouseEvent.getX(), mouseEvent.getY(), mouseEvent.getWhen() * 1000000L);
    }

    public synchronized void mouseDragged(MouseEvent mouseEvent) {
        this.handleMotion(mouseEvent);
    }

    public synchronized void mouseMoved(MouseEvent mouseEvent) {
        this.handleMotion(mouseEvent);
    }

    private void handleWheel(int n, long l) {
        this.accum_dz += n;
        this.putMouseEvent((byte)-1, (byte)0, n, l);
    }

    protected void updateDeltas(long l) {
    }

    public synchronized void mouseWheelMoved(MouseWheelEvent mouseWheelEvent) {
        int n = -mouseWheelEvent.getWheelRotation() * 120;
        this.handleWheel(n, mouseWheelEvent.getWhen() * 1000000L);
    }
}
