/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.NVProgram;

public final class NVFragmentProgram
extends NVProgram {
    public static final int GL_FRAGMENT_PROGRAM_NV = 34928;
    public static final int GL_MAX_TEXTURE_COORDS_NV = 34929;
    public static final int GL_MAX_TEXTURE_IMAGE_UNITS_NV = 34930;
    public static final int GL_FRAGMENT_PROGRAM_BINDING_NV = 34931;
    public static final int GL_MAX_FRAGMENT_PROGRAM_LOCAL_PARAMETERS_NV = 34920;

    private NVFragmentProgram() {
    }

    public static void glProgramNamedParameter4fNV(int n, ByteBuffer byteBuffer, float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramNamedParameter4fNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        NVFragmentProgram.nglProgramNamedParameter4fNV(n, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), f, f2, f3, f4, l);
    }

    static native void nglProgramNamedParameter4fNV(int var0, int var1, long var2, float var4, float var5, float var6, float var7, long var8);

    public static void glProgramNamedParameter4dNV(int n, ByteBuffer byteBuffer, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramNamedParameter4dNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        NVFragmentProgram.nglProgramNamedParameter4dNV(n, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), d, d2, d3, d4, l);
    }

    static native void nglProgramNamedParameter4dNV(int var0, int var1, long var2, double var4, double var6, double var8, double var10, long var12);

    public static void glGetProgramNamedParameterNV(int n, ByteBuffer byteBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramNamedParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVFragmentProgram.nglGetProgramNamedParameterfvNV(n, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetProgramNamedParameterfvNV(int var0, int var1, long var2, long var4, long var6);

    public static void glGetProgramNamedParameterNV(int n, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramNamedParameterdvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        NVFragmentProgram.nglGetProgramNamedParameterdvNV(n, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetProgramNamedParameterdvNV(int var0, int var1, long var2, long var4, long var6);
}
