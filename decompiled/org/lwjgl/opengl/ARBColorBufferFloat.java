/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ARBColorBufferFloat {
    public static final int GL_RGBA_FLOAT_MODE_ARB = 34848;
    public static final int GL_CLAMP_VERTEX_COLOR_ARB = 35098;
    public static final int GL_CLAMP_FRAGMENT_COLOR_ARB = 35099;
    public static final int GL_CLAMP_READ_COLOR_ARB = 35100;
    public static final int GL_FIXED_ONLY_ARB = 35101;
    public static final int WGL_TYPE_RGBA_FLOAT_ARB = 8608;
    public static final int GLX_RGBA_FLOAT_TYPE = 8377;
    public static final int GLX_RGBA_FLOAT_BIT = 4;

    private ARBColorBufferFloat() {
    }

    public static void glClampColorARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClampColorARB;
        BufferChecks.checkFunctionAddress(l);
        ARBColorBufferFloat.nglClampColorARB(n, n2, l);
    }

    static native void nglClampColorARB(int var0, int var1, long var2);
}
