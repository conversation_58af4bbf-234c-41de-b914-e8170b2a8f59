/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVDrawTexture {
    private NVDrawTexture() {
    }

    public static void glDrawTextureNV(int n, int n2, float f, float f2, float f3, float f4, float f5, float f6, float f7, float f8, float f9) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawTextureNV;
        BufferChecks.checkFunctionAddress(l);
        NVDrawTexture.nglDrawTextureNV(n, n2, f, f2, f3, f4, f5, f6, f7, f8, f9, l);
    }

    static native void nglDrawTextureNV(int var0, int var1, float var2, float var3, float var4, float var5, float var6, float var7, float var8, float var9, float var10, long var11);
}
