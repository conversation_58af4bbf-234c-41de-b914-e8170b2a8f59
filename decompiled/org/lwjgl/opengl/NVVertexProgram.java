/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.NVProgram;
import org.lwjgl.opengl.StateTracker;

public final class NVVertexProgram
extends NVProgram {
    public static final int GL_VERTEX_PROGRAM_NV = 34336;
    public static final int GL_VERTEX_PROGRAM_POINT_SIZE_NV = 34370;
    public static final int GL_VERTEX_PROGRAM_TWO_SIDE_NV = 34371;
    public static final int GL_VERTEX_STATE_PROGRAM_NV = 34337;
    public static final int GL_ATTRIB_ARRAY_SIZE_NV = 34339;
    public static final int GL_ATTRIB_ARRAY_STRIDE_NV = 34340;
    public static final int GL_ATTRIB_ARRAY_TYPE_NV = 34341;
    public static final int GL_CURRENT_ATTRIB_NV = 34342;
    public static final int GL_PROGRAM_PARAMETER_NV = 34372;
    public static final int GL_ATTRIB_ARRAY_POINTER_NV = 34373;
    public static final int GL_TRACK_MATRIX_NV = 34376;
    public static final int GL_TRACK_MATRIX_TRANSFORM_NV = 34377;
    public static final int GL_MAX_TRACK_MATRIX_STACK_DEPTH_NV = 34350;
    public static final int GL_MAX_TRACK_MATRICES_NV = 34351;
    public static final int GL_CURRENT_MATRIX_STACK_DEPTH_NV = 34368;
    public static final int GL_CURRENT_MATRIX_NV = 34369;
    public static final int GL_VERTEX_PROGRAM_BINDING_NV = 34378;
    public static final int GL_MODELVIEW_PROJECTION_NV = 34345;
    public static final int GL_MATRIX0_NV = 34352;
    public static final int GL_MATRIX1_NV = 34353;
    public static final int GL_MATRIX2_NV = 34354;
    public static final int GL_MATRIX3_NV = 34355;
    public static final int GL_MATRIX4_NV = 34356;
    public static final int GL_MATRIX5_NV = 34357;
    public static final int GL_MATRIX6_NV = 34358;
    public static final int GL_MATRIX7_NV = 34359;
    public static final int GL_IDENTITY_NV = 34346;
    public static final int GL_INVERSE_NV = 34347;
    public static final int GL_TRANSPOSE_NV = 34348;
    public static final int GL_INVERSE_TRANSPOSE_NV = 34349;
    public static final int GL_VERTEX_ATTRIB_ARRAY0_NV = 34384;
    public static final int GL_VERTEX_ATTRIB_ARRAY1_NV = 34385;
    public static final int GL_VERTEX_ATTRIB_ARRAY2_NV = 34386;
    public static final int GL_VERTEX_ATTRIB_ARRAY3_NV = 34387;
    public static final int GL_VERTEX_ATTRIB_ARRAY4_NV = 34388;
    public static final int GL_VERTEX_ATTRIB_ARRAY5_NV = 34389;
    public static final int GL_VERTEX_ATTRIB_ARRAY6_NV = 34390;
    public static final int GL_VERTEX_ATTRIB_ARRAY7_NV = 34391;
    public static final int GL_VERTEX_ATTRIB_ARRAY8_NV = 34392;
    public static final int GL_VERTEX_ATTRIB_ARRAY9_NV = 34393;
    public static final int GL_VERTEX_ATTRIB_ARRAY10_NV = 34394;
    public static final int GL_VERTEX_ATTRIB_ARRAY11_NV = 34395;
    public static final int GL_VERTEX_ATTRIB_ARRAY12_NV = 34396;
    public static final int GL_VERTEX_ATTRIB_ARRAY13_NV = 34397;
    public static final int GL_VERTEX_ATTRIB_ARRAY14_NV = 34398;
    public static final int GL_VERTEX_ATTRIB_ARRAY15_NV = 34399;
    public static final int GL_MAP1_VERTEX_ATTRIB0_4_NV = 34400;
    public static final int GL_MAP1_VERTEX_ATTRIB1_4_NV = 34401;
    public static final int GL_MAP1_VERTEX_ATTRIB2_4_NV = 34402;
    public static final int GL_MAP1_VERTEX_ATTRIB3_4_NV = 34403;
    public static final int GL_MAP1_VERTEX_ATTRIB4_4_NV = 34404;
    public static final int GL_MAP1_VERTEX_ATTRIB5_4_NV = 34405;
    public static final int GL_MAP1_VERTEX_ATTRIB6_4_NV = 34406;
    public static final int GL_MAP1_VERTEX_ATTRIB7_4_NV = 34407;
    public static final int GL_MAP1_VERTEX_ATTRIB8_4_NV = 34408;
    public static final int GL_MAP1_VERTEX_ATTRIB9_4_NV = 34409;
    public static final int GL_MAP1_VERTEX_ATTRIB10_4_NV = 34410;
    public static final int GL_MAP1_VERTEX_ATTRIB11_4_NV = 34411;
    public static final int GL_MAP1_VERTEX_ATTRIB12_4_NV = 34412;
    public static final int GL_MAP1_VERTEX_ATTRIB13_4_NV = 34413;
    public static final int GL_MAP1_VERTEX_ATTRIB14_4_NV = 34414;
    public static final int GL_MAP1_VERTEX_ATTRIB15_4_NV = 34415;
    public static final int GL_MAP2_VERTEX_ATTRIB0_4_NV = 34416;
    public static final int GL_MAP2_VERTEX_ATTRIB1_4_NV = 34417;
    public static final int GL_MAP2_VERTEX_ATTRIB2_4_NV = 34418;
    public static final int GL_MAP2_VERTEX_ATTRIB3_4_NV = 34419;
    public static final int GL_MAP2_VERTEX_ATTRIB4_4_NV = 34420;
    public static final int GL_MAP2_VERTEX_ATTRIB5_4_NV = 34421;
    public static final int GL_MAP2_VERTEX_ATTRIB6_4_NV = 34422;
    public static final int GL_MAP2_VERTEX_ATTRIB7_4_NV = 34423;
    public static final int GL_MAP2_VERTEX_ATTRIB8_4_NV = 34424;
    public static final int GL_MAP2_VERTEX_ATTRIB9_4_NV = 34425;
    public static final int GL_MAP2_VERTEX_ATTRIB10_4_NV = 34426;
    public static final int GL_MAP2_VERTEX_ATTRIB11_4_NV = 34427;
    public static final int GL_MAP2_VERTEX_ATTRIB12_4_NV = 34428;
    public static final int GL_MAP2_VERTEX_ATTRIB13_4_NV = 34429;
    public static final int GL_MAP2_VERTEX_ATTRIB14_4_NV = 34430;
    public static final int GL_MAP2_VERTEX_ATTRIB15_4_NV = 34431;

    private NVVertexProgram() {
    }

    public static void glExecuteProgramNV(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glExecuteProgramNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVVertexProgram.nglExecuteProgramNV(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglExecuteProgramNV(int var0, int var1, long var2, long var4);

    public static void glGetProgramParameterNV(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVVertexProgram.nglGetProgramParameterfvNV(n, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetProgramParameterfvNV(int var0, int var1, int var2, long var3, long var5);

    public static void glGetProgramParameterNV(int n, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramParameterdvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        NVVertexProgram.nglGetProgramParameterdvNV(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetProgramParameterdvNV(int var0, int var1, int var2, long var3, long var5);

    public static void glGetTrackMatrixNV(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTrackMatrixivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVVertexProgram.nglGetTrackMatrixivNV(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetTrackMatrixivNV(int var0, int var1, int var2, long var3, long var5);

    public static void glGetVertexAttribNV(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVVertexProgram.nglGetVertexAttribfvNV(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetVertexAttribfvNV(int var0, int var1, long var2, long var4);

    public static void glGetVertexAttribNV(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribdvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        NVVertexProgram.nglGetVertexAttribdvNV(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetVertexAttribdvNV(int var0, int var1, long var2, long var4);

    public static void glGetVertexAttribNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVVertexProgram.nglGetVertexAttribivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetVertexAttribivNV(int var0, int var1, long var2, long var4);

    public static ByteBuffer glGetVertexAttribPointerNV(int n, int n2, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetVertexAttribPointervNV;
        BufferChecks.checkFunctionAddress(l2);
        ByteBuffer byteBuffer = NVVertexProgram.nglGetVertexAttribPointervNV(n, n2, l, l2);
        if (LWJGLUtil.CHECKS && byteBuffer == null) {
            return null;
        }
        return byteBuffer.order(ByteOrder.nativeOrder());
    }

    static native ByteBuffer nglGetVertexAttribPointervNV(int var0, int var1, long var2, long var4);

    public static void glProgramParameter4fNV(int n, int n2, float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramParameter4fNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglProgramParameter4fNV(n, n2, f, f2, f3, f4, l);
    }

    static native void nglProgramParameter4fNV(int var0, int var1, float var2, float var3, float var4, float var5, long var6);

    public static void glProgramParameter4dNV(int n, int n2, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramParameter4dNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglProgramParameter4dNV(n, n2, d, d2, d3, d4, l);
    }

    static native void nglProgramParameter4dNV(int var0, int var1, double var2, double var4, double var6, double var8, long var10);

    public static void glProgramParameters4NV(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramParameters4fvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        NVVertexProgram.nglProgramParameters4fvNV(n, n2, floatBuffer.remaining() >> 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramParameters4fvNV(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramParameters4NV(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramParameters4dvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        NVVertexProgram.nglProgramParameters4dvNV(n, n2, doubleBuffer.remaining() >> 2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramParameters4dvNV(int var0, int var1, int var2, long var3, long var5);

    public static void glTrackMatrixNV(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTrackMatrixNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglTrackMatrixNV(n, n2, n3, n4, l);
    }

    static native void nglTrackMatrixNV(int var0, int var1, int var2, int var3, long var4);

    public static void glVertexAttribPointerNV(int n, int n2, int n3, int n4, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointerNV;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = doubleBuffer;
        }
        NVVertexProgram.nglVertexAttribPointerNV(n, n2, n3, n4, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glVertexAttribPointerNV(int n, int n2, int n3, int n4, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointerNV;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = floatBuffer;
        }
        NVVertexProgram.nglVertexAttribPointerNV(n, n2, n3, n4, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glVertexAttribPointerNV(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointerNV;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = byteBuffer;
        }
        NVVertexProgram.nglVertexAttribPointerNV(n, n2, n3, n4, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glVertexAttribPointerNV(int n, int n2, int n3, int n4, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointerNV;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = intBuffer;
        }
        NVVertexProgram.nglVertexAttribPointerNV(n, n2, n3, n4, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glVertexAttribPointerNV(int n, int n2, int n3, int n4, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointerNV;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = shortBuffer;
        }
        NVVertexProgram.nglVertexAttribPointerNV(n, n2, n3, n4, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexAttribPointerNV(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glVertexAttribPointerNV(int n, int n2, int n3, int n4, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glVertexAttribPointerNV;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        NVVertexProgram.nglVertexAttribPointerNVBO(n, n2, n3, n4, l, l2);
    }

    static native void nglVertexAttribPointerNVBO(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glVertexAttrib1sNV(int n, short s) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib1sNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglVertexAttrib1sNV(n, s, l);
    }

    static native void nglVertexAttrib1sNV(int var0, short var1, long var2);

    public static void glVertexAttrib1fNV(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib1fNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglVertexAttrib1fNV(n, f, l);
    }

    static native void nglVertexAttrib1fNV(int var0, float var1, long var2);

    public static void glVertexAttrib1dNV(int n, double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib1dNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglVertexAttrib1dNV(n, d, l);
    }

    static native void nglVertexAttrib1dNV(int var0, double var1, long var3);

    public static void glVertexAttrib2sNV(int n, short s, short s2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib2sNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglVertexAttrib2sNV(n, s, s2, l);
    }

    static native void nglVertexAttrib2sNV(int var0, short var1, short var2, long var3);

    public static void glVertexAttrib2fNV(int n, float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib2fNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglVertexAttrib2fNV(n, f, f2, l);
    }

    static native void nglVertexAttrib2fNV(int var0, float var1, float var2, long var3);

    public static void glVertexAttrib2dNV(int n, double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib2dNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglVertexAttrib2dNV(n, d, d2, l);
    }

    static native void nglVertexAttrib2dNV(int var0, double var1, double var3, long var5);

    public static void glVertexAttrib3sNV(int n, short s, short s2, short s3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib3sNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglVertexAttrib3sNV(n, s, s2, s3, l);
    }

    static native void nglVertexAttrib3sNV(int var0, short var1, short var2, short var3, long var4);

    public static void glVertexAttrib3fNV(int n, float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib3fNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglVertexAttrib3fNV(n, f, f2, f3, l);
    }

    static native void nglVertexAttrib3fNV(int var0, float var1, float var2, float var3, long var4);

    public static void glVertexAttrib3dNV(int n, double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib3dNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglVertexAttrib3dNV(n, d, d2, d3, l);
    }

    static native void nglVertexAttrib3dNV(int var0, double var1, double var3, double var5, long var7);

    public static void glVertexAttrib4sNV(int n, short s, short s2, short s3, short s4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib4sNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglVertexAttrib4sNV(n, s, s2, s3, s4, l);
    }

    static native void nglVertexAttrib4sNV(int var0, short var1, short var2, short var3, short var4, long var5);

    public static void glVertexAttrib4fNV(int n, float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib4fNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglVertexAttrib4fNV(n, f, f2, f3, f4, l);
    }

    static native void nglVertexAttrib4fNV(int var0, float var1, float var2, float var3, float var4, long var5);

    public static void glVertexAttrib4dNV(int n, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib4dNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglVertexAttrib4dNV(n, d, d2, d3, d4, l);
    }

    static native void nglVertexAttrib4dNV(int var0, double var1, double var3, double var5, double var7, long var9);

    public static void glVertexAttrib4ubNV(int n, byte by, byte by2, byte by3, byte by4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib4ubNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexProgram.nglVertexAttrib4ubNV(n, by, by2, by3, by4, l);
    }

    static native void nglVertexAttrib4ubNV(int var0, byte var1, byte var2, byte var3, byte var4, long var5);

    public static void glVertexAttribs1NV(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs1svNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        NVVertexProgram.nglVertexAttribs1svNV(n, shortBuffer.remaining(), MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexAttribs1svNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribs1NV(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs1fvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        NVVertexProgram.nglVertexAttribs1fvNV(n, floatBuffer.remaining(), MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglVertexAttribs1fvNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribs1NV(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs1dvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        NVVertexProgram.nglVertexAttribs1dvNV(n, doubleBuffer.remaining(), MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVertexAttribs1dvNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribs2NV(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs2svNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        NVVertexProgram.nglVertexAttribs2svNV(n, shortBuffer.remaining() >> 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexAttribs2svNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribs2NV(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs2fvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        NVVertexProgram.nglVertexAttribs2fvNV(n, floatBuffer.remaining() >> 1, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglVertexAttribs2fvNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribs2NV(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs2dvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        NVVertexProgram.nglVertexAttribs2dvNV(n, doubleBuffer.remaining() >> 1, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVertexAttribs2dvNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribs3NV(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs3svNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        NVVertexProgram.nglVertexAttribs3svNV(n, shortBuffer.remaining() / 3, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexAttribs3svNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribs3NV(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs3fvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        NVVertexProgram.nglVertexAttribs3fvNV(n, floatBuffer.remaining() / 3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglVertexAttribs3fvNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribs3NV(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs3dvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        NVVertexProgram.nglVertexAttribs3dvNV(n, doubleBuffer.remaining() / 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVertexAttribs3dvNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribs4NV(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs4svNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        NVVertexProgram.nglVertexAttribs4svNV(n, shortBuffer.remaining() >> 2, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexAttribs4svNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribs4NV(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs4fvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        NVVertexProgram.nglVertexAttribs4fvNV(n, floatBuffer.remaining() >> 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglVertexAttribs4fvNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribs4NV(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribs4dvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        NVVertexProgram.nglVertexAttribs4dvNV(n, doubleBuffer.remaining() >> 2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVertexAttribs4dvNV(int var0, int var1, long var2, long var4);
}
