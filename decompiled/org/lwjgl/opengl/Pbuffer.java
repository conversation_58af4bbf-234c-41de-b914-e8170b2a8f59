/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.BufferUtils;
import org.lwjgl.Sys;
import org.lwjgl.opengl.Context;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.ContextGL;
import org.lwjgl.opengl.Display;
import org.lwjgl.opengl.Drawable;
import org.lwjgl.opengl.DrawableGL;
import org.lwjgl.opengl.DrawableLWJGL;
import org.lwjgl.opengl.PeerInfo;
import org.lwjgl.opengl.PixelFormat;
import org.lwjgl.opengl.RenderTexture;

public final class Pbuffer
extends DrawableGL {
    public static final int PBUFFER_SUPPORTED = 1;
    public static final int RENDER_TEXTURE_SUPPORTED = 2;
    public static final int RENDER_TEXTURE_RECTANGLE_SUPPORTED = 4;
    public static final int RENDER_DEPTH_TEXTURE_SUPPORTED = 8;
    public static final int MIPMAP_LEVEL = 8315;
    public static final int CUBE_MAP_FACE = 8316;
    public static final int TEXTURE_CUBE_MAP_POSITIVE_X = 8317;
    public static final int TEXTURE_CUBE_MAP_NEGATIVE_X = 8318;
    public static final int TEXTURE_CUBE_MAP_POSITIVE_Y = 8319;
    public static final int TEXTURE_CUBE_MAP_NEGATIVE_Y = 8320;
    public static final int TEXTURE_CUBE_MAP_POSITIVE_Z = 8321;
    public static final int TEXTURE_CUBE_MAP_NEGATIVE_Z = 8322;
    public static final int FRONT_LEFT_BUFFER = 8323;
    public static final int FRONT_RIGHT_BUFFER = 8324;
    public static final int BACK_LEFT_BUFFER = 8325;
    public static final int BACK_RIGHT_BUFFER = 8326;
    public static final int DEPTH_BUFFER = 8359;
    private final int width;
    private final int height;

    public Pbuffer(int n, int n2, PixelFormat pixelFormat, Drawable drawable) {
        this(n, n2, pixelFormat, null, drawable);
    }

    public Pbuffer(int n, int n2, PixelFormat pixelFormat, RenderTexture renderTexture, Drawable drawable) {
        this(n, n2, pixelFormat, renderTexture, drawable, null);
    }

    public Pbuffer(int n, int n2, PixelFormat pixelFormat, RenderTexture renderTexture, Drawable drawable, ContextAttribs contextAttribs) {
        if (pixelFormat == null) {
            throw new NullPointerException("Pixel format must be non-null");
        }
        this.width = n;
        this.height = n2;
        this.peer_info = Pbuffer.createPbuffer(n, n2, pixelFormat, contextAttribs, renderTexture);
        Context context = null;
        if (drawable == null) {
            drawable = Display.getDrawable();
        }
        if (drawable != null) {
            context = ((DrawableLWJGL)drawable).getContext();
        }
        this.context = new ContextGL(this.peer_info, contextAttribs, (ContextGL)context);
    }

    private static PeerInfo createPbuffer(int n, int n2, PixelFormat pixelFormat, ContextAttribs contextAttribs, RenderTexture object) {
        if (object == null) {
            object = BufferUtils.createIntBuffer(1);
            return Display.getImplementation().createPbuffer(n, n2, pixelFormat, contextAttribs, null, (IntBuffer)object);
        }
        return Display.getImplementation().createPbuffer(n, n2, pixelFormat, contextAttribs, ((RenderTexture)object).pixelFormatCaps, ((RenderTexture)object).pBufferAttribs);
    }

    public final synchronized boolean isBufferLost() {
        this.checkDestroyed();
        return Display.getImplementation().isBufferLost(this.peer_info);
    }

    public static int getCapabilities() {
        return Display.getImplementation().getPbufferCapabilities();
    }

    public final synchronized void setAttrib(int n, int n2) {
        this.checkDestroyed();
        Display.getImplementation().setPbufferAttrib(this.peer_info, n, n2);
    }

    public final synchronized void bindTexImage(int n) {
        this.checkDestroyed();
        Display.getImplementation().bindTexImageToPbuffer(this.peer_info, n);
    }

    public final synchronized void releaseTexImage(int n) {
        this.checkDestroyed();
        Display.getImplementation().releaseTexImageFromPbuffer(this.peer_info, n);
    }

    public final synchronized int getHeight() {
        this.checkDestroyed();
        return this.height;
    }

    public final synchronized int getWidth() {
        this.checkDestroyed();
        return this.width;
    }

    static {
        Sys.initialize();
    }
}
