/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.StateTracker;

public final class GL15 {
    public static final int GL_ARRAY_BUFFER = 34962;
    public static final int GL_ELEMENT_ARRAY_BUFFER = 34963;
    public static final int GL_ARRAY_BUFFER_BINDING = 34964;
    public static final int GL_ELEMENT_ARRAY_BUFFER_BINDING = 34965;
    public static final int GL_VERTEX_ARRAY_BUFFER_BINDING = 34966;
    public static final int GL_NORMAL_ARRAY_BUFFER_BINDING = 34967;
    public static final int GL_COLOR_ARRAY_BUFFER_BINDING = 34968;
    public static final int GL_INDEX_ARRAY_BUFFER_BINDING = 34969;
    public static final int GL_TEXTURE_COORD_ARRAY_BUFFER_BINDING = 34970;
    public static final int GL_EDGE_FLAG_ARRAY_BUFFER_BINDING = 34971;
    public static final int GL_SECONDARY_COLOR_ARRAY_BUFFER_BINDING = 34972;
    public static final int GL_FOG_COORDINATE_ARRAY_BUFFER_BINDING = 34973;
    public static final int GL_WEIGHT_ARRAY_BUFFER_BINDING = 34974;
    public static final int GL_VERTEX_ATTRIB_ARRAY_BUFFER_BINDING = 34975;
    public static final int GL_STREAM_DRAW = 35040;
    public static final int GL_STREAM_READ = 35041;
    public static final int GL_STREAM_COPY = 35042;
    public static final int GL_STATIC_DRAW = 35044;
    public static final int GL_STATIC_READ = 35045;
    public static final int GL_STATIC_COPY = 35046;
    public static final int GL_DYNAMIC_DRAW = 35048;
    public static final int GL_DYNAMIC_READ = 35049;
    public static final int GL_DYNAMIC_COPY = 35050;
    public static final int GL_READ_ONLY = 35000;
    public static final int GL_WRITE_ONLY = 35001;
    public static final int GL_READ_WRITE = 35002;
    public static final int GL_BUFFER_SIZE = 34660;
    public static final int GL_BUFFER_USAGE = 34661;
    public static final int GL_BUFFER_ACCESS = 35003;
    public static final int GL_BUFFER_MAPPED = 35004;
    public static final int GL_BUFFER_MAP_POINTER = 35005;
    public static final int GL_FOG_COORD_SRC = 33872;
    public static final int GL_FOG_COORD = 33873;
    public static final int GL_CURRENT_FOG_COORD = 33875;
    public static final int GL_FOG_COORD_ARRAY_TYPE = 33876;
    public static final int GL_FOG_COORD_ARRAY_STRIDE = 33877;
    public static final int GL_FOG_COORD_ARRAY_POINTER = 33878;
    public static final int GL_FOG_COORD_ARRAY = 33879;
    public static final int GL_FOG_COORD_ARRAY_BUFFER_BINDING = 34973;
    public static final int GL_SRC0_RGB = 34176;
    public static final int GL_SRC1_RGB = 34177;
    public static final int GL_SRC2_RGB = 34178;
    public static final int GL_SRC0_ALPHA = 34184;
    public static final int GL_SRC1_ALPHA = 34185;
    public static final int GL_SRC2_ALPHA = 34186;
    public static final int GL_SAMPLES_PASSED = 35092;
    public static final int GL_QUERY_COUNTER_BITS = 34916;
    public static final int GL_CURRENT_QUERY = 34917;
    public static final int GL_QUERY_RESULT = 34918;
    public static final int GL_QUERY_RESULT_AVAILABLE = 34919;

    private GL15() {
    }

    public static void glBindBuffer(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindBuffer;
        BufferChecks.checkFunctionAddress(l);
        StateTracker.bindBuffer(contextCapabilities, n, n2);
        GL15.nglBindBuffer(n, n2, l);
    }

    static native void nglBindBuffer(int var0, int var1, long var2);

    public static void glDeleteBuffers(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteBuffers;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL15.nglDeleteBuffers(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeleteBuffers(int var0, long var1, long var3);

    public static void glDeleteBuffers(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteBuffers;
        BufferChecks.checkFunctionAddress(l);
        GL15.nglDeleteBuffers(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static void glGenBuffers(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenBuffers;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL15.nglGenBuffers(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenBuffers(int var0, long var1, long var3);

    public static int glGenBuffers() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenBuffers;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL15.nglGenBuffers(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static boolean glIsBuffer(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsBuffer;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = GL15.nglIsBuffer(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsBuffer(int var0, long var1);

    public static void glBufferData(int n, long l, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glBufferData;
        BufferChecks.checkFunctionAddress(l2);
        GL15.nglBufferData(n, l, 0L, n2, l2);
    }

    public static void glBufferData(int n, ByteBuffer byteBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBufferData;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        GL15.nglBufferData(n, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), n2, l);
    }

    public static void glBufferData(int n, DoubleBuffer doubleBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBufferData;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL15.nglBufferData(n, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), n2, l);
    }

    public static void glBufferData(int n, FloatBuffer floatBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBufferData;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL15.nglBufferData(n, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), n2, l);
    }

    public static void glBufferData(int n, IntBuffer intBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBufferData;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL15.nglBufferData(n, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), n2, l);
    }

    public static void glBufferData(int n, ShortBuffer shortBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBufferData;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        GL15.nglBufferData(n, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), n2, l);
    }

    static native void nglBufferData(int var0, long var1, long var3, int var5, long var6);

    public static void glBufferSubData(int n, long l, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(byteBuffer);
        GL15.nglBufferSubData(n, l, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l2);
    }

    public static void glBufferSubData(int n, long l, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(doubleBuffer);
        GL15.nglBufferSubData(n, l, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l2);
    }

    public static void glBufferSubData(int n, long l, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(floatBuffer);
        GL15.nglBufferSubData(n, l, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l2);
    }

    public static void glBufferSubData(int n, long l, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(intBuffer);
        GL15.nglBufferSubData(n, l, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l2);
    }

    public static void glBufferSubData(int n, long l, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(shortBuffer);
        GL15.nglBufferSubData(n, l, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l2);
    }

    static native void nglBufferSubData(int var0, long var1, long var3, long var5, long var7);

    public static void glGetBufferSubData(int n, long l, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(byteBuffer);
        GL15.nglGetBufferSubData(n, l, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l2);
    }

    public static void glGetBufferSubData(int n, long l, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(doubleBuffer);
        GL15.nglGetBufferSubData(n, l, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l2);
    }

    public static void glGetBufferSubData(int n, long l, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(floatBuffer);
        GL15.nglGetBufferSubData(n, l, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l2);
    }

    public static void glGetBufferSubData(int n, long l, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(intBuffer);
        GL15.nglGetBufferSubData(n, l, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l2);
    }

    public static void glGetBufferSubData(int n, long l, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(shortBuffer);
        GL15.nglGetBufferSubData(n, l, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l2);
    }

    static native void nglGetBufferSubData(int var0, long var1, long var3, long var5, long var7);

    public static ByteBuffer glMapBuffer(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMapBuffer;
        BufferChecks.checkFunctionAddress(l);
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        ByteBuffer byteBuffer2 = GL15.nglMapBuffer(n, n2, GL15.glGetBufferParameteri(n, 34660), byteBuffer, l);
        if (LWJGLUtil.CHECKS && byteBuffer2 == null) {
            return null;
        }
        return byteBuffer2.order(ByteOrder.nativeOrder());
    }

    public static ByteBuffer glMapBuffer(int n, int n2, long l, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMapBuffer;
        BufferChecks.checkFunctionAddress(l2);
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        ByteBuffer byteBuffer2 = GL15.nglMapBuffer(n, n2, l, byteBuffer, l2);
        if (LWJGLUtil.CHECKS && byteBuffer2 == null) {
            return null;
        }
        return byteBuffer2.order(ByteOrder.nativeOrder());
    }

    static native ByteBuffer nglMapBuffer(int var0, int var1, long var2, ByteBuffer var4, long var5);

    public static boolean glUnmapBuffer(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUnmapBuffer;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = GL15.nglUnmapBuffer(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglUnmapBuffer(int var0, long var1);

    public static void glGetBufferParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetBufferParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL15.nglGetBufferParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetBufferParameteriv(int var0, int var1, long var2, long var4);

    public static int glGetBufferParameter(int n, int n2) {
        return GL15.glGetBufferParameteri(n, n2);
    }

    public static int glGetBufferParameteri(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetBufferParameteriv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL15.nglGetBufferParameteriv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static ByteBuffer glGetBufferPointer(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetBufferPointerv;
        BufferChecks.checkFunctionAddress(l);
        ByteBuffer byteBuffer = GL15.nglGetBufferPointerv(n, n2, GL15.glGetBufferParameteri(n, 34660), l);
        if (LWJGLUtil.CHECKS && byteBuffer == null) {
            return null;
        }
        return byteBuffer.order(ByteOrder.nativeOrder());
    }

    static native ByteBuffer nglGetBufferPointerv(int var0, int var1, long var2, long var4);

    public static void glGenQueries(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenQueries;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL15.nglGenQueries(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenQueries(int var0, long var1, long var3);

    public static int glGenQueries() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenQueries;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL15.nglGenQueries(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glDeleteQueries(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteQueries;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL15.nglDeleteQueries(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeleteQueries(int var0, long var1, long var3);

    public static void glDeleteQueries(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteQueries;
        BufferChecks.checkFunctionAddress(l);
        GL15.nglDeleteQueries(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static boolean glIsQuery(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsQuery;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = GL15.nglIsQuery(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsQuery(int var0, long var1);

    public static void glBeginQuery(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBeginQuery;
        BufferChecks.checkFunctionAddress(l);
        GL15.nglBeginQuery(n, n2, l);
    }

    static native void nglBeginQuery(int var0, int var1, long var2);

    public static void glEndQuery(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEndQuery;
        BufferChecks.checkFunctionAddress(l);
        GL15.nglEndQuery(n, l);
    }

    static native void nglEndQuery(int var0, long var1);

    public static void glGetQuery(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetQueryiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL15.nglGetQueryiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetQueryiv(int var0, int var1, long var2, long var4);

    public static int glGetQuery(int n, int n2) {
        return GL15.glGetQueryi(n, n2);
    }

    public static int glGetQueryi(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetQueryiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL15.nglGetQueryiv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetQueryObject(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetQueryObjectiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL15.nglGetQueryObjectiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetQueryObjectiv(int var0, int var1, long var2, long var4);

    public static int glGetQueryObjecti(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetQueryObjectiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL15.nglGetQueryObjectiv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetQueryObjectu(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetQueryObjectuiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL15.nglGetQueryObjectuiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetQueryObjectuiv(int var0, int var1, long var2, long var4);

    public static int glGetQueryObjectui(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetQueryObjectuiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL15.nglGetQueryObjectuiv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }
}
