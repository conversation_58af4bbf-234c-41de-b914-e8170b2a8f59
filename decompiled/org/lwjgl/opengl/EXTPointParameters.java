/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.FloatBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class EXTPointParameters {
    public static final int GL_POINT_SIZE_MIN_EXT = 33062;
    public static final int GL_POINT_SIZE_MAX_EXT = 33063;
    public static final int GL_POINT_FADE_THRESHOLD_SIZE_EXT = 33064;
    public static final int GL_DISTANCE_ATTENUATION_EXT = 33065;

    private EXTPointParameters() {
    }

    public static void glPointParameterfEXT(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPointParameterfEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTPointParameters.nglPointParameterfEXT(n, f, l);
    }

    static native void nglPointParameterfEXT(int var0, float var1, long var2);

    public static void glPointParameterEXT(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPointParameterfvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        EXTPointParameters.nglPointParameterfvEXT(n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglPointParameterfvEXT(int var0, long var1, long var3);
}
