/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;

public final class EXTPalettedTexture {
    public static final int GL_COLOR_INDEX1_EXT = 32994;
    public static final int GL_COLOR_INDEX2_EXT = 32995;
    public static final int GL_COLOR_INDEX4_EXT = 32996;
    public static final int GL_COLOR_INDEX8_EXT = 32997;
    public static final int GL_COLOR_INDEX12_EXT = 32998;
    public static final int GL_COLOR_INDEX16_EXT = 32999;
    public static final int GL_COLOR_TABLE_FORMAT_EXT = 32984;
    public static final int GL_COLOR_TABLE_WIDTH_EXT = 32985;
    public static final int GL_COLOR_TABLE_RED_SIZE_EXT = 32986;
    public static final int GL_COLOR_TABLE_GREEN_SIZE_EXT = 32987;
    public static final int GL_COLOR_TABLE_BLUE_SIZE_EXT = 32988;
    public static final int GL_COLOR_TABLE_ALPHA_SIZE_EXT = 32989;
    public static final int GL_COLOR_TABLE_LUMINANCE_SIZE_EXT = 32990;
    public static final int GL_COLOR_TABLE_INTENSITY_SIZE_EXT = 32991;
    public static final int GL_TEXTURE_INDEX_SIZE_EXT = 33005;

    private EXTPalettedTexture() {
    }

    public static void glColorTableEXT(int n, int n2, int n3, int n4, int n5, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorTableEXT;
        BufferChecks.checkFunctionAddress(l);
        ByteBuffer byteBuffer2 = byteBuffer;
        BufferChecks.checkBuffer(byteBuffer2, GLChecks.calculateImageStorage(byteBuffer2, n4, n5, n3, 1, 1));
        EXTPalettedTexture.nglColorTableEXT(n, n2, n3, n4, n5, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glColorTableEXT(int n, int n2, int n3, int n4, int n5, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorTableEXT;
        BufferChecks.checkFunctionAddress(l);
        DoubleBuffer doubleBuffer2 = doubleBuffer;
        BufferChecks.checkBuffer(doubleBuffer2, GLChecks.calculateImageStorage(doubleBuffer2, n4, n5, n3, 1, 1));
        EXTPalettedTexture.nglColorTableEXT(n, n2, n3, n4, n5, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glColorTableEXT(int n, int n2, int n3, int n4, int n5, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorTableEXT;
        BufferChecks.checkFunctionAddress(l);
        FloatBuffer floatBuffer2 = floatBuffer;
        BufferChecks.checkBuffer(floatBuffer2, GLChecks.calculateImageStorage(floatBuffer2, n4, n5, n3, 1, 1));
        EXTPalettedTexture.nglColorTableEXT(n, n2, n3, n4, n5, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glColorTableEXT(int n, int n2, int n3, int n4, int n5, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorTableEXT;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer2 = intBuffer;
        BufferChecks.checkBuffer(intBuffer2, GLChecks.calculateImageStorage(intBuffer2, n4, n5, n3, 1, 1));
        EXTPalettedTexture.nglColorTableEXT(n, n2, n3, n4, n5, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glColorTableEXT(int n, int n2, int n3, int n4, int n5, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorTableEXT;
        BufferChecks.checkFunctionAddress(l);
        ShortBuffer shortBuffer2 = shortBuffer;
        BufferChecks.checkBuffer(shortBuffer2, GLChecks.calculateImageStorage(shortBuffer2, n4, n5, n3, 1, 1));
        EXTPalettedTexture.nglColorTableEXT(n, n2, n3, n4, n5, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglColorTableEXT(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glColorSubTableEXT(int n, int n2, int n3, int n4, int n5, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorSubTableEXT;
        BufferChecks.checkFunctionAddress(l);
        ByteBuffer byteBuffer2 = byteBuffer;
        BufferChecks.checkBuffer(byteBuffer2, GLChecks.calculateImageStorage(byteBuffer2, n4, n5, n3, 1, 1));
        EXTPalettedTexture.nglColorSubTableEXT(n, n2, n3, n4, n5, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glColorSubTableEXT(int n, int n2, int n3, int n4, int n5, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorSubTableEXT;
        BufferChecks.checkFunctionAddress(l);
        DoubleBuffer doubleBuffer2 = doubleBuffer;
        BufferChecks.checkBuffer(doubleBuffer2, GLChecks.calculateImageStorage(doubleBuffer2, n4, n5, n3, 1, 1));
        EXTPalettedTexture.nglColorSubTableEXT(n, n2, n3, n4, n5, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glColorSubTableEXT(int n, int n2, int n3, int n4, int n5, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorSubTableEXT;
        BufferChecks.checkFunctionAddress(l);
        FloatBuffer floatBuffer2 = floatBuffer;
        BufferChecks.checkBuffer(floatBuffer2, GLChecks.calculateImageStorage(floatBuffer2, n4, n5, n3, 1, 1));
        EXTPalettedTexture.nglColorSubTableEXT(n, n2, n3, n4, n5, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glColorSubTableEXT(int n, int n2, int n3, int n4, int n5, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorSubTableEXT;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer2 = intBuffer;
        BufferChecks.checkBuffer(intBuffer2, GLChecks.calculateImageStorage(intBuffer2, n4, n5, n3, 1, 1));
        EXTPalettedTexture.nglColorSubTableEXT(n, n2, n3, n4, n5, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glColorSubTableEXT(int n, int n2, int n3, int n4, int n5, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorSubTableEXT;
        BufferChecks.checkFunctionAddress(l);
        ShortBuffer shortBuffer2 = shortBuffer;
        BufferChecks.checkBuffer(shortBuffer2, GLChecks.calculateImageStorage(shortBuffer2, n4, n5, n3, 1, 1));
        EXTPalettedTexture.nglColorSubTableEXT(n, n2, n3, n4, n5, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglColorSubTableEXT(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glGetColorTableEXT(int n, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetColorTableEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        EXTPalettedTexture.nglGetColorTableEXT(n, n2, n3, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetColorTableEXT(int n, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetColorTableEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        EXTPalettedTexture.nglGetColorTableEXT(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetColorTableEXT(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetColorTableEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        EXTPalettedTexture.nglGetColorTableEXT(n, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetColorTableEXT(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetColorTableEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        EXTPalettedTexture.nglGetColorTableEXT(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetColorTableEXT(int n, int n2, int n3, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetColorTableEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        EXTPalettedTexture.nglGetColorTableEXT(n, n2, n3, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetColorTableEXT(int var0, int var1, int var2, long var3, long var5);

    public static void glGetColorTableParameterEXT(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetColorTableParameterivEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        EXTPalettedTexture.nglGetColorTableParameterivEXT(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetColorTableParameterivEXT(int var0, int var1, long var2, long var4);

    public static void glGetColorTableParameterEXT(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetColorTableParameterfvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        EXTPalettedTexture.nglGetColorTableParameterfvEXT(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetColorTableParameterfvEXT(int var0, int var1, long var2, long var4);
}
