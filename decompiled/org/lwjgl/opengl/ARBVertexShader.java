/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.StateTracker;

public final class ARBVertexShader {
    public static final int GL_VERTEX_SHADER_ARB = 35633;
    public static final int GL_MAX_VERTEX_UNIFORM_COMPONENTS_ARB = 35658;
    public static final int GL_MAX_VARYING_FLOATS_ARB = 35659;
    public static final int GL_MAX_VERTEX_ATTRIBS_ARB = 34921;
    public static final int GL_MAX_TEXTURE_IMAGE_UNITS_ARB = 34930;
    public static final int GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS_ARB = 35660;
    public static final int GL_MAX_COMBINED_TEXTURE_IMAGE_UNITS_ARB = 35661;
    public static final int GL_MAX_TEXTURE_COORDS_ARB = 34929;
    public static final int GL_VERTEX_PROGRAM_POINT_SIZE_ARB = 34370;
    public static final int GL_VERTEX_PROGRAM_TWO_SIDE_ARB = 34371;
    public static final int GL_OBJECT_ACTIVE_ATTRIBUTES_ARB = 35721;
    public static final int GL_OBJECT_ACTIVE_ATTRIBUTE_MAX_LENGTH_ARB = 35722;
    public static final int GL_VERTEX_ATTRIB_ARRAY_ENABLED_ARB = 34338;
    public static final int GL_VERTEX_ATTRIB_ARRAY_SIZE_ARB = 34339;
    public static final int GL_VERTEX_ATTRIB_ARRAY_STRIDE_ARB = 34340;
    public static final int GL_VERTEX_ATTRIB_ARRAY_TYPE_ARB = 34341;
    public static final int GL_VERTEX_ATTRIB_ARRAY_NORMALIZED_ARB = 34922;
    public static final int GL_CURRENT_VERTEX_ATTRIB_ARB = 34342;
    public static final int GL_VERTEX_ATTRIB_ARRAY_POINTER_ARB = 34373;
    public static final int GL_FLOAT_VEC2_ARB = 35664;
    public static final int GL_FLOAT_VEC3_ARB = 35665;
    public static final int GL_FLOAT_VEC4_ARB = 35666;
    public static final int GL_FLOAT_MAT2_ARB = 35674;
    public static final int GL_FLOAT_MAT3_ARB = 35675;
    public static final int GL_FLOAT_MAT4_ARB = 35676;

    private ARBVertexShader() {
    }

    public static void glVertexAttrib1sARB(int n, short s) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib1sARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglVertexAttrib1sARB(n, s, l);
    }

    static native void nglVertexAttrib1sARB(int var0, short var1, long var2);

    public static void glVertexAttrib1fARB(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib1fARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglVertexAttrib1fARB(n, f, l);
    }

    static native void nglVertexAttrib1fARB(int var0, float var1, long var2);

    public static void glVertexAttrib1dARB(int n, double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib1dARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglVertexAttrib1dARB(n, d, l);
    }

    static native void nglVertexAttrib1dARB(int var0, double var1, long var3);

    public static void glVertexAttrib2sARB(int n, short s, short s2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib2sARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglVertexAttrib2sARB(n, s, s2, l);
    }

    static native void nglVertexAttrib2sARB(int var0, short var1, short var2, long var3);

    public static void glVertexAttrib2fARB(int n, float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib2fARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglVertexAttrib2fARB(n, f, f2, l);
    }

    static native void nglVertexAttrib2fARB(int var0, float var1, float var2, long var3);

    public static void glVertexAttrib2dARB(int n, double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib2dARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglVertexAttrib2dARB(n, d, d2, l);
    }

    static native void nglVertexAttrib2dARB(int var0, double var1, double var3, long var5);

    public static void glVertexAttrib3sARB(int n, short s, short s2, short s3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib3sARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglVertexAttrib3sARB(n, s, s2, s3, l);
    }

    static native void nglVertexAttrib3sARB(int var0, short var1, short var2, short var3, long var4);

    public static void glVertexAttrib3fARB(int n, float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib3fARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglVertexAttrib3fARB(n, f, f2, f3, l);
    }

    static native void nglVertexAttrib3fARB(int var0, float var1, float var2, float var3, long var4);

    public static void glVertexAttrib3dARB(int n, double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib3dARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglVertexAttrib3dARB(n, d, d2, d3, l);
    }

    static native void nglVertexAttrib3dARB(int var0, double var1, double var3, double var5, long var7);

    public static void glVertexAttrib4sARB(int n, short s, short s2, short s3, short s4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib4sARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglVertexAttrib4sARB(n, s, s2, s3, s4, l);
    }

    static native void nglVertexAttrib4sARB(int var0, short var1, short var2, short var3, short var4, long var5);

    public static void glVertexAttrib4fARB(int n, float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib4fARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglVertexAttrib4fARB(n, f, f2, f3, f4, l);
    }

    static native void nglVertexAttrib4fARB(int var0, float var1, float var2, float var3, float var4, long var5);

    public static void glVertexAttrib4dARB(int n, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib4dARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglVertexAttrib4dARB(n, d, d2, d3, d4, l);
    }

    static native void nglVertexAttrib4dARB(int var0, double var1, double var3, double var5, double var7, long var9);

    public static void glVertexAttrib4NubARB(int n, byte by, byte by2, byte by3, byte by4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib4NubARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglVertexAttrib4NubARB(n, by, by2, by3, by4, l);
    }

    static native void nglVertexAttrib4NubARB(int var0, byte var1, byte var2, byte var3, byte var4, long var5);

    public static void glVertexAttribPointerARB(int n, int n2, boolean bl, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointerARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = doubleBuffer;
        }
        ARBVertexShader.nglVertexAttribPointerARB(n, n2, 5130, bl, n3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glVertexAttribPointerARB(int n, int n2, boolean bl, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointerARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = floatBuffer;
        }
        ARBVertexShader.nglVertexAttribPointerARB(n, n2, 5126, bl, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glVertexAttribPointerARB(int n, int n2, boolean bl, boolean bl2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointerARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = byteBuffer;
        }
        ARBVertexShader.nglVertexAttribPointerARB(n, n2, bl ? 5121 : 5120, bl2, n3, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glVertexAttribPointerARB(int n, int n2, boolean bl, boolean bl2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointerARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = intBuffer;
        }
        ARBVertexShader.nglVertexAttribPointerARB(n, n2, bl ? 5125 : 5124, bl2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glVertexAttribPointerARB(int n, int n2, boolean bl, boolean bl2, int n3, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointerARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = shortBuffer;
        }
        ARBVertexShader.nglVertexAttribPointerARB(n, n2, bl ? 5123 : 5122, bl2, n3, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexAttribPointerARB(int var0, int var1, int var2, boolean var3, int var4, long var5, long var7);

    public static void glVertexAttribPointerARB(int n, int n2, int n3, boolean bl, int n4, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glVertexAttribPointerARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        ARBVertexShader.nglVertexAttribPointerARBBO(n, n2, n3, bl, n4, l, l2);
    }

    static native void nglVertexAttribPointerARBBO(int var0, int var1, int var2, boolean var3, int var4, long var5, long var7);

    public static void glVertexAttribPointerARB(int n, int n2, int n3, boolean bl, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointerARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = byteBuffer;
        }
        ARBVertexShader.nglVertexAttribPointerARB(n, n2, n3, bl, n4, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glEnableVertexAttribArrayARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEnableVertexAttribArrayARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglEnableVertexAttribArrayARB(n, l);
    }

    static native void nglEnableVertexAttribArrayARB(int var0, long var1);

    public static void glDisableVertexAttribArrayARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDisableVertexAttribArrayARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglDisableVertexAttribArrayARB(n, l);
    }

    static native void nglDisableVertexAttribArrayARB(int var0, long var1);

    public static void glBindAttribLocationARB(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindAttribLocationARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        ARBVertexShader.nglBindAttribLocationARB(n, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglBindAttribLocationARB(int var0, int var1, long var2, long var4);

    public static void glBindAttribLocationARB(int n, int n2, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindAttribLocationARB;
        BufferChecks.checkFunctionAddress(l);
        ARBVertexShader.nglBindAttribLocationARB(n, n2, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
    }

    public static void glGetActiveAttribARB(int n, int n2, IntBuffer intBuffer, IntBuffer intBuffer2, IntBuffer intBuffer3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveAttribARB;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkBuffer(intBuffer2, 1);
        BufferChecks.checkBuffer(intBuffer3, 1);
        BufferChecks.checkDirect(byteBuffer);
        ARBVertexShader.nglGetActiveAttribARB(n, n2, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(intBuffer3), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetActiveAttribARB(int var0, int var1, int var2, long var3, long var5, long var7, long var9, long var11);

    public static String glGetActiveAttribARB(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveAttribARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 2);
        IntBuffer intBuffer2 = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        IntBuffer intBuffer3 = intBuffer;
        ARBVertexShader.nglGetActiveAttribARB(n, n2, n3, MemoryUtil.getAddress0(intBuffer2), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer3, intBuffer3.position() + 1), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer2.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static String glGetActiveAttribARB(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveAttribARB;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        ARBVertexShader.nglGetActiveAttribARB(n, n2, n3, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress0(APIUtil.getBufferInt(contextCapabilities)), MemoryUtil.getAddress(APIUtil.getBufferInt(contextCapabilities), 1), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static int glGetActiveAttribSizeARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveAttribARB;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getBufferInt(contextCapabilities);
        ARBVertexShader.nglGetActiveAttribARB(n, n2, 0, 0L, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer, 1), APIUtil.getBufferByte0(contextCapabilities), l);
        return intBuffer.get(0);
    }

    public static int glGetActiveAttribTypeARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveAttribARB;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getBufferInt(contextCapabilities);
        ARBVertexShader.nglGetActiveAttribARB(n, n2, 0, 0L, MemoryUtil.getAddress(intBuffer, 1), MemoryUtil.getAddress(intBuffer), APIUtil.getBufferByte0(contextCapabilities), l);
        return intBuffer.get(0);
    }

    public static int glGetAttribLocationARB(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetAttribLocationARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = ARBVertexShader.nglGetAttribLocationARB(n, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglGetAttribLocationARB(int var0, long var1, long var3);

    public static int glGetAttribLocationARB(int n, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetAttribLocationARB;
        BufferChecks.checkFunctionAddress(l);
        n = ARBVertexShader.nglGetAttribLocationARB(n, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }

    public static void glGetVertexAttribARB(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribfvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ARBVertexShader.nglGetVertexAttribfvARB(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetVertexAttribfvARB(int var0, int var1, long var2, long var4);

    public static void glGetVertexAttribARB(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribdvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        ARBVertexShader.nglGetVertexAttribdvARB(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetVertexAttribdvARB(int var0, int var1, long var2, long var4);

    public static void glGetVertexAttribARB(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        ARBVertexShader.nglGetVertexAttribivARB(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetVertexAttribivARB(int var0, int var1, long var2, long var4);

    public static ByteBuffer glGetVertexAttribPointerARB(int n, int n2, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetVertexAttribPointervARB;
        BufferChecks.checkFunctionAddress(l2);
        ByteBuffer byteBuffer = ARBVertexShader.nglGetVertexAttribPointervARB(n, n2, l, l2);
        if (LWJGLUtil.CHECKS && byteBuffer == null) {
            return null;
        }
        return byteBuffer.order(ByteOrder.nativeOrder());
    }

    static native ByteBuffer nglGetVertexAttribPointervARB(int var0, int var1, long var2, long var4);
}
