/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class EXTBlendColor {
    public static final int GL_CONSTANT_COLOR_EXT = 32769;
    public static final int GL_ONE_MINUS_CONSTANT_COLOR_EXT = 32770;
    public static final int GL_CONSTANT_ALPHA_EXT = 32771;
    public static final int GL_ONE_MINUS_CONSTANT_ALPHA_EXT = 32772;
    public static final int GL_BLEND_COLOR_EXT = 32773;

    private EXTBlendColor() {
    }

    public static void glBlendColorEXT(float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendColorEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTBlendColor.nglBlendColorEXT(f, f2, f3, f4, l);
    }

    static native void nglBlendColorEXT(float var0, float var1, float var2, float var3, long var4);
}
