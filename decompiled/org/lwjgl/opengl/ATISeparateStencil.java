/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ATISeparateStencil {
    public static final int GL_STENCIL_BACK_FUNC_ATI = 34816;
    public static final int GL_STENCIL_BACK_FAIL_ATI = 34817;
    public static final int GL_STENCIL_BACK_PASS_DEPTH_FAIL_ATI = 34818;
    public static final int GL_STENCIL_BACK_PASS_DEPTH_PASS_ATI = 34819;

    private ATISeparateStencil() {
    }

    public static void glStencilOpSeparateATI(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glStencilOpSeparateATI;
        BufferChecks.checkFunctionAddress(l);
        ATISeparateStencil.nglStencilOpSeparateATI(n, n2, n3, n4, l);
    }

    static native void nglStencilOpSeparateATI(int var0, int var1, int var2, int var3, long var4);

    public static void glStencilFuncSeparateATI(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glStencilFuncSeparateATI;
        BufferChecks.checkFunctionAddress(l);
        ATISeparateStencil.nglStencilFuncSeparateATI(n, n2, n3, n4, l);
    }

    static native void nglStencilFuncSeparateATI(int var0, int var1, int var2, int var3, long var4);
}
