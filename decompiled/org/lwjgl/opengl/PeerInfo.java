/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import org.lwjgl.LWJGLUtil;

abstract class PeerInfo {
    private final ByteBuffer handle;
    private Thread locking_thread;
    private int lock_count;

    protected PeerInfo(ByteBuffer byteBuffer) {
        this.handle = byteBuffer;
    }

    private void lockAndInitHandle() {
        this.doLockAndInitHandle();
    }

    public final synchronized void unlock() {
        if (this.lock_count <= 0) {
            throw new IllegalStateException("PeerInfo not locked!");
        }
        if (Thread.currentThread() != this.locking_thread) {
            throw new IllegalStateException("PeerInfo already locked by " + this.locking_thread);
        }
        --this.lock_count;
        if (this.lock_count == 0) {
            this.doUnlock();
            this.locking_thread = null;
            this.notify();
        }
    }

    protected abstract void doLockAndInitHandle();

    protected abstract void doUnlock();

    public final synchronized ByteBuffer lockAndGetHandle() {
        Thread thread = Thread.currentThread();
        while (this.locking_thread != null && this.locking_thread != thread) {
            try {
                this.wait();
            }
            catch (InterruptedException interruptedException) {
                LWJGLUtil.log("Interrupted while waiting for PeerInfo lock: " + interruptedException);
            }
        }
        if (this.lock_count == 0) {
            this.locking_thread = thread;
            this.doLockAndInitHandle();
        }
        ++this.lock_count;
        return this.getHandle();
    }

    protected final ByteBuffer getHandle() {
        return this.handle;
    }

    public void destroy() {
    }
}
