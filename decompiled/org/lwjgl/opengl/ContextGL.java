/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.LWJGLException;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.PointerBuffer;
import org.lwjgl.Sys;
import org.lwjgl.opengl.CallbackUtil;
import org.lwjgl.opengl.Context;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.ContextImplementation;
import org.lwjgl.opengl.GL11;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.LinuxContextImplementation;
import org.lwjgl.opengl.MacOSXContextImplementation;
import org.lwjgl.opengl.OpenGLException;
import org.lwjgl.opengl.PeerInfo;
import org.lwjgl.opengl.WindowsContextImplementation;

final class ContextGL
implements Context {
    private static final ContextImplementation implementation;
    private static final ThreadLocal<ContextGL> current_context_local;
    private final ByteBuffer handle;
    private final PeerInfo peer_info;
    private final ContextAttribs contextAttribs;
    private final boolean forwardCompatible;
    private boolean destroyed;
    private boolean destroy_requested;
    private Thread thread;

    private static ContextImplementation createImplementation() {
        switch (LWJGLUtil.getPlatform()) {
            case 1: {
                return new LinuxContextImplementation();
            }
            case 3: {
                return new WindowsContextImplementation();
            }
            case 2: {
                return new MacOSXContextImplementation();
            }
        }
        throw new IllegalStateException("Unsupported platform");
    }

    final PeerInfo getPeerInfo() {
        return this.peer_info;
    }

    final ContextAttribs getContextAttribs() {
        return this.contextAttribs;
    }

    static ContextGL getCurrentContext() {
        return current_context_local.get();
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    ContextGL(PeerInfo peerInfo, ContextAttribs contextAttribs, ContextGL contextGL) {
        ContextGL contextGL2 = contextGL != null ? contextGL : this;
        ContextGL contextGL3 = contextGL2;
        contextGL3 = contextGL2;
        synchronized (contextGL2) {
            if (contextGL != null && contextGL.destroyed) {
                throw new IllegalArgumentException("Shared context is destroyed");
            }
            GLContext.loadOpenGLLibrary();
            try {
                IntBuffer intBuffer;
                this.peer_info = peerInfo;
                this.contextAttribs = contextAttribs;
                if (contextAttribs != null) {
                    intBuffer = contextAttribs.getAttribList();
                    this.forwardCompatible = contextAttribs.isForwardCompatible();
                } else {
                    intBuffer = null;
                    this.forwardCompatible = false;
                }
                this.handle = implementation.create(peerInfo, intBuffer, contextGL != null ? contextGL.handle : null);
            }
            catch (LWJGLException lWJGLException) {
                GLContext.unloadOpenGLLibrary();
                throw lWJGLException;
            }
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public final void releaseCurrent() {
        ContextGL contextGL = ContextGL.getCurrentContext();
        if (contextGL != null) {
            implementation.releaseCurrentContext();
            GLContext.useContext(null);
            current_context_local.set(null);
            ContextGL contextGL2 = contextGL;
            synchronized (contextGL2) {
                contextGL.thread = null;
                contextGL.checkDestroy();
                return;
            }
        }
    }

    public final synchronized void releaseDrawable() {
        if (this.destroyed) {
            throw new IllegalStateException("Context is destroyed");
        }
        implementation.releaseDrawable(this.getHandle());
    }

    public final synchronized void update() {
        if (this.destroyed) {
            throw new IllegalStateException("Context is destroyed");
        }
        implementation.update(this.getHandle());
    }

    public static void swapBuffers() {
        implementation.swapBuffers();
    }

    private boolean canAccess() {
        return this.thread == null || Thread.currentThread() == this.thread;
    }

    private void checkAccess() {
        if (!this.canAccess()) {
            throw new IllegalStateException("From thread " + Thread.currentThread() + ": " + this.thread + " already has the context current");
        }
    }

    public final synchronized void makeCurrent() {
        this.checkAccess();
        if (this.destroyed) {
            throw new IllegalStateException("Context is destroyed");
        }
        this.thread = Thread.currentThread();
        current_context_local.set(this);
        implementation.makeCurrent(this.peer_info, this.handle);
        ContextGL contextGL = this;
        GLContext.useContext(contextGL, contextGL.forwardCompatible);
    }

    final ByteBuffer getHandle() {
        return this.handle;
    }

    public final synchronized boolean isCurrent() {
        if (this.destroyed) {
            throw new IllegalStateException("Context is destroyed");
        }
        return implementation.isCurrent(this.handle);
    }

    private void checkDestroy() {
        if (!this.destroyed && this.destroy_requested) {
            try {
                this.releaseDrawable();
                implementation.destroy(this.peer_info, this.handle);
                CallbackUtil.unregisterCallbacks(this);
                this.destroyed = true;
                this.thread = null;
                GLContext.unloadOpenGLLibrary();
                return;
            }
            catch (LWJGLException lWJGLException) {
                LWJGLUtil.log("Exception occurred while destroying context: " + lWJGLException);
            }
        }
    }

    public static void setSwapInterval(int n) {
        implementation.setSwapInterval(n);
    }

    public final synchronized void forceDestroy() {
        this.checkAccess();
        this.destroy();
    }

    public final synchronized void destroy() {
        if (this.destroyed) {
            return;
        }
        this.destroy_requested = true;
        boolean bl = this.isCurrent();
        int n = 0;
        if (bl) {
            try {
                n = GL11.glGetError();
            }
            catch (Exception exception) {}
            this.releaseCurrent();
        }
        this.checkDestroy();
        if (bl && n != 0) {
            throw new OpenGLException(n);
        }
    }

    /*
     * Enabled force condition propagation
     * Lifted jumps to return sites
     */
    public final synchronized void setCLSharingProperties(PointerBuffer pointerBuffer) {
        Object object = this.peer_info.lockAndGetHandle();
        try {
            switch (LWJGLUtil.getPlatform()) {
                case 3: {
                    WindowsContextImplementation windowsContextImplementation = (WindowsContextImplementation)implementation;
                    pointerBuffer.put(8200L).put(windowsContextImplementation.getHGLRC(this.handle));
                    pointerBuffer.put(8203L).put(windowsContextImplementation.getHDC((ByteBuffer)object));
                    return;
                }
                case 1: {
                    LinuxContextImplementation linuxContextImplementation = (LinuxContextImplementation)implementation;
                    pointerBuffer.put(8200L).put(linuxContextImplementation.getGLXContext(this.handle));
                    pointerBuffer.put(8202L).put(linuxContextImplementation.getDisplay((ByteBuffer)object));
                    return;
                }
                case 2: {
                    if (!LWJGLUtil.isMacOSXEqualsOrBetterThan(10, 6)) throw new UnsupportedOperationException("CL/GL context sharing is not supported on this platform.");
                    object = (MacOSXContextImplementation)implementation;
                    long l = ((MacOSXContextImplementation)object).getCGLShareGroup(this.handle);
                    pointerBuffer.put(0x10000000L).put(l);
                    return;
                }
                default: {
                    throw new UnsupportedOperationException("CL/GL context sharing is not supported on this platform.");
                }
            }
        }
        finally {
            this.peer_info.unlock();
        }
    }

    static {
        current_context_local = new ThreadLocal();
        Sys.initialize();
        implementation = ContextGL.createImplementation();
    }
}
