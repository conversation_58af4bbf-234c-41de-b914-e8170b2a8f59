/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Component;
import java.awt.Cursor;
import java.awt.Dimension;
import java.awt.GraphicsConfiguration;
import java.awt.GraphicsDevice;
import java.awt.IllegalComponentStateException;
import java.awt.MouseInfo;
import java.awt.Point;
import java.awt.PointerInfo;
import java.awt.Robot;
import java.awt.Toolkit;
import java.awt.image.BufferedImage;
import java.nio.IntBuffer;
import java.security.AccessController;
import java.security.PrivilegedActionException;
import java.security.PrivilegedExceptionAction;
import org.lwjgl.LWJGLUtil;

final class AWTUtil {
    AWTUtil() {
    }

    public static boolean hasWheel() {
        return true;
    }

    public static int getButtonCount() {
        return 3;
    }

    public static int getNativeCursorCapabilities() {
        if (LWJGLUtil.getPlatform() != 2 || LWJGLUtil.isMacOSXEqualsOrBetterThan(10, 4)) {
            int n = Toolkit.getDefaultToolkit().getMaximumCursorColors();
            n = n >= Short.MAX_VALUE && AWTUtil.getMaxCursorSize() > 0 ? 1 : 0;
            n = n != 0 ? 3 : 4;
            return n;
        }
        return 0;
    }

    public static Robot createRobot(final Component component) {
        try {
            return AccessController.doPrivileged(new PrivilegedExceptionAction<Robot>(){

                @Override
                public final Robot run() {
                    return new Robot(component.getGraphicsConfiguration().getDevice());
                }
            });
        }
        catch (PrivilegedActionException privilegedActionException) {
            LWJGLUtil.log("Got exception while creating robot: " + privilegedActionException.getCause());
            return null;
        }
    }

    private static int transformY(Component component, int n) {
        return component.getHeight() - 1 - n;
    }

    private static Point getPointerLocation(Component object) {
        try {
            object = ((Component)object).getGraphicsConfiguration();
            if (object != null) {
                PointerInfo pointerInfo = AccessController.doPrivileged(new PrivilegedExceptionAction<PointerInfo>(){

                    @Override
                    public final PointerInfo run() {
                        return MouseInfo.getPointerInfo();
                    }
                });
                GraphicsDevice graphicsDevice = pointerInfo.getDevice();
                if (graphicsDevice == ((GraphicsConfiguration)object).getDevice()) {
                    return pointerInfo.getLocation();
                }
                return null;
            }
        }
        catch (Exception exception) {
            LWJGLUtil.log("Failed to query pointer location: " + exception.getCause());
        }
        return null;
    }

    public static Point getCursorPosition(Component component) {
        try {
            Point point = AWTUtil.getPointerLocation(component);
            if (point != null) {
                Point point2 = component.getLocationOnScreen();
                point.translate(-point2.x, -point2.y);
                Point point3 = point;
                point3.move(point3.x, AWTUtil.transformY(component, point.y));
                return point;
            }
        }
        catch (IllegalComponentStateException illegalComponentStateException) {
            LWJGLUtil.log("Failed to set cursor position: " + illegalComponentStateException);
        }
        catch (NoClassDefFoundError noClassDefFoundError) {
            LWJGLUtil.log("Failed to query cursor position: " + noClassDefFoundError);
        }
        return null;
    }

    public static void setCursorPosition(Component component, Robot robot, int n, int n2) {
        if (robot != null) {
            try {
                Point point = component.getLocationOnScreen();
                n = point.x + n;
                int n3 = point.y + AWTUtil.transformY(component, n2);
                robot.mouseMove(n, n3);
                return;
            }
            catch (IllegalComponentStateException illegalComponentStateException) {
                LWJGLUtil.log("Failed to set cursor position: " + illegalComponentStateException);
            }
        }
    }

    public static int getMinCursorSize() {
        Dimension dimension = Toolkit.getDefaultToolkit().getBestCursorSize(0, 0);
        return Math.max(dimension.width, dimension.height);
    }

    public static int getMaxCursorSize() {
        Dimension dimension = Toolkit.getDefaultToolkit().getBestCursorSize(10000, 10000);
        return Math.min(dimension.width, dimension.height);
    }

    public static Cursor createCursor(int n, int n2, int n3, int n4, int n5, IntBuffer intBuffer, IntBuffer object) {
        BufferedImage bufferedImage = new BufferedImage(n, n2, 2);
        object = new int[intBuffer.remaining()];
        int n6 = intBuffer.position();
        intBuffer.get((int[])object);
        intBuffer.position(n6);
        bufferedImage.setRGB(0, 0, n, n2, (int[])object, 0, n);
        return Toolkit.getDefaultToolkit().createCustomCursor(bufferedImage, new Point(n3, n4), "LWJGL Custom cursor");
    }
}
