/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Canvas;
import java.awt.Graphics;

final class MacOSXGLCanvas
extends Canvas {
    private static final long serialVersionUID = 6916664741667434870L;
    private boolean canvas_painted;
    private boolean dirty;

    MacOSXGLCanvas() {
    }

    public final void update(Graphics graphics) {
        this.paint(graphics);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public final void paint(Graphics object) {
        object = this;
        synchronized (object) {
            this.dirty = true;
            this.canvas_painted = true;
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public final boolean syncCanvasPainted() {
        boolean bl;
        MacOSXGLCanvas macOSXGLCanvas = this;
        synchronized (macOSXGLCanvas) {
            bl = this.canvas_painted;
            this.canvas_painted = false;
        }
        return bl;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public final boolean syncIsDirty() {
        boolean bl;
        MacOSXGLCanvas macOSXGLCanvas = this;
        synchronized (macOSXGLCanvas) {
            bl = this.dirty;
            this.dirty = false;
        }
        return bl;
    }
}
