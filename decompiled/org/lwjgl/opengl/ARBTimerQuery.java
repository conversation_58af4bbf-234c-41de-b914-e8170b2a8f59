/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.LongBuffer;
import org.lwjgl.opengl.GL33;

public final class ARBTimerQuery {
    public static final int GL_TIME_ELAPSED = 35007;
    public static final int GL_TIMESTAMP = 36392;

    private ARBTimerQuery() {
    }

    public static void glQueryCounter(int n, int n2) {
        GL33.glQueryCounter(n, n2);
    }

    public static void glGetQueryObject(int n, int n2, LongBuffer longBuffer) {
        GL33.glGetQueryObject(n, n2, longBuffer);
    }

    public static long glGetQueryObjecti64(int n, int n2) {
        return GL33.glGetQueryObjecti64(n, n2);
    }

    public static void glGetQueryObjectu(int n, int n2, LongBuffer longBuffer) {
        GL33.glGetQueryObjectu(n, n2, longBuffer);
    }

    public static long glGetQueryObjectui64(int n, int n2) {
        return GL33.glGetQueryObjectui64(n, n2);
    }
}
