/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import org.lwjgl.opengl.EXTDrawBuffers2;
import org.lwjgl.opengl.GL41;

public final class ARBViewportArray {
    public static final int GL_MAX_VIEWPORTS = 33371;
    public static final int GL_VIEWPORT_SUBPIXEL_BITS = 33372;
    public static final int GL_VIEWPORT_BOUNDS_RANGE = 33373;
    public static final int GL_LAYER_PROVOKING_VERTEX = 33374;
    public static final int GL_VIEWPORT_INDEX_PROVOKING_VERTEX = 33375;
    public static final int GL_FIRST_VERTEX_CONVENTION = 36429;
    public static final int GL_LAST_VERTEX_CONVENTION = 36430;
    public static final int GL_PROVOKING_VERTEX = 36431;
    public static final int GL_UNDEFINED_VERTEX = 33376;

    private ARBViewportArray() {
    }

    public static void glViewportArray(int n, FloatBuffer floatBuffer) {
        GL41.glViewportArray(n, floatBuffer);
    }

    public static void glViewportIndexedf(int n, float f, float f2, float f3, float f4) {
        GL41.glViewportIndexedf(n, f, f2, f3, f4);
    }

    public static void glViewportIndexed(int n, FloatBuffer floatBuffer) {
        GL41.glViewportIndexed(n, floatBuffer);
    }

    public static void glScissorArray(int n, IntBuffer intBuffer) {
        GL41.glScissorArray(n, intBuffer);
    }

    public static void glScissorIndexed(int n, int n2, int n3, int n4, int n5) {
        GL41.glScissorIndexed(n, n2, n3, n4, n5);
    }

    public static void glScissorIndexed(int n, IntBuffer intBuffer) {
        GL41.glScissorIndexed(n, intBuffer);
    }

    public static void glDepthRangeArray(int n, DoubleBuffer doubleBuffer) {
        GL41.glDepthRangeArray(n, doubleBuffer);
    }

    public static void glDepthRangeIndexed(int n, double d, double d2) {
        GL41.glDepthRangeIndexed(n, d, d2);
    }

    public static void glGetFloat(int n, int n2, FloatBuffer floatBuffer) {
        GL41.glGetFloat(n, n2, floatBuffer);
    }

    public static float glGetFloat(int n, int n2) {
        return GL41.glGetFloat(n, n2);
    }

    public static void glGetDouble(int n, int n2, DoubleBuffer doubleBuffer) {
        GL41.glGetDouble(n, n2, doubleBuffer);
    }

    public static double glGetDouble(int n, int n2) {
        return GL41.glGetDouble(n, n2);
    }

    public static void glGetIntegerIndexedEXT(int n, int n2, IntBuffer intBuffer) {
        EXTDrawBuffers2.glGetIntegerIndexedEXT(n, n2, intBuffer);
    }

    public static int glGetIntegerIndexedEXT(int n, int n2) {
        return EXTDrawBuffers2.glGetIntegerIndexedEXT(n, n2);
    }

    public static void glEnableIndexedEXT(int n, int n2) {
        EXTDrawBuffers2.glEnableIndexedEXT(n, n2);
    }

    public static void glDisableIndexedEXT(int n, int n2) {
        EXTDrawBuffers2.glDisableIndexedEXT(n, n2);
    }

    public static boolean glIsEnabledIndexedEXT(int n, int n2) {
        return EXTDrawBuffers2.glIsEnabledIndexedEXT(n, n2);
    }
}
