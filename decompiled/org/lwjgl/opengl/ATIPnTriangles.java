/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ATIPnTriangles {
    public static final int GL_PN_TRIANGLES_ATI = 34800;
    public static final int GL_MAX_PN_TRIANGLES_TESSELATION_LEVEL_ATI = 34801;
    public static final int GL_PN_TRIANGLES_POINT_MODE_ATI = 34802;
    public static final int GL_PN_TRIANGLES_NORMAL_MODE_ATI = 34803;
    public static final int GL_PN_TRIANGLES_TESSELATION_LEVEL_ATI = 34804;
    public static final int GL_PN_TRIANGLES_POINT_MODE_LINEAR_ATI = 34805;
    public static final int GL_PN_TRIANGLES_POINT_MODE_CUBIC_ATI = 34806;
    public static final int GL_PN_TRIANGLES_NORMAL_MODE_LINEAR_ATI = 34807;
    public static final int GL_PN_TRIANGLES_NORMAL_MODE_QUADRATIC_ATI = 34808;

    private ATIPnTriangles() {
    }

    public static void glPNTrianglesfATI(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPNTrianglesfATI;
        BufferChecks.checkFunctionAddress(l);
        ATIPnTriangles.nglPNTrianglesfATI(n, f, l);
    }

    static native void nglPNTrianglesfATI(int var0, float var1, long var2);

    public static void glPNTrianglesiATI(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPNTrianglesiATI;
        BufferChecks.checkFunctionAddress(l);
        ATIPnTriangles.nglPNTrianglesiATI(n, n2, l);
    }

    static native void nglPNTrianglesiATI(int var0, int var1, long var2);
}
