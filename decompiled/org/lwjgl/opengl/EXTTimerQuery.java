/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.LongBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class EXTTimerQuery {
    public static final int GL_TIME_ELAPSED_EXT = 35007;

    private EXTTimerQuery() {
    }

    public static void glGetQueryObjectEXT(int n, int n2, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetQueryObjecti64vEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 1);
        EXTTimerQuery.nglGetQueryObjecti64vEXT(n, n2, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetQueryObjecti64vEXT(int var0, int var1, long var2, long var4);

    public static long glGetQueryObjectEXT(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetQueryObjecti64vEXT;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferLong((ContextCapabilities)object);
        EXTTimerQuery.nglGetQueryObjecti64vEXT(n, n2, MemoryUtil.getAddress((LongBuffer)object), l);
        return ((LongBuffer)object).get(0);
    }

    public static void glGetQueryObjectuEXT(int n, int n2, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetQueryObjectui64vEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 1);
        EXTTimerQuery.nglGetQueryObjectui64vEXT(n, n2, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetQueryObjectui64vEXT(int var0, int var1, long var2, long var4);

    public static long glGetQueryObjectuEXT(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetQueryObjectui64vEXT;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferLong((ContextCapabilities)object);
        EXTTimerQuery.nglGetQueryObjectui64vEXT(n, n2, MemoryUtil.getAddress((LongBuffer)object), l);
        return ((LongBuffer)object).get(0);
    }
}
