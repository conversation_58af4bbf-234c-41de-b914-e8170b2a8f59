/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.LongBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVVideoCapture {
    public static final int GL_VIDEO_BUFFER_NV = 36896;
    public static final int GL_VIDEO_BUFFER_BINDING_NV = 36897;
    public static final int GL_FIELD_UPPER_NV = 36898;
    public static final int GL_FIELD_LOWER_NV = 36899;
    public static final int GL_NUM_VIDEO_CAPTURE_STREAMS_NV = 36900;
    public static final int GL_NEXT_VIDEO_CAPTURE_BUFFER_STATUS_NV = 36901;
    public static final int GL_LAST_VIDEO_CAPTURE_STATUS_NV = 36903;
    public static final int GL_VIDEO_BUFFER_PITCH_NV = 36904;
    public static final int GL_VIDEO_CAPTURE_FRAME_WIDTH_NV = 36920;
    public static final int GL_VIDEO_CAPTURE_FRAME_HEIGHT_NV = 36921;
    public static final int GL_VIDEO_CAPTURE_FIELD_UPPER_HEIGHT_NV = 36922;
    public static final int GL_VIDEO_CAPTURE_FIELD_LOWER_HEIGHT_NV = 36923;
    public static final int GL_VIDEO_CAPTURE_TO_422_SUPPORTED_NV = 36902;
    public static final int GL_VIDEO_COLOR_CONVERSION_MATRIX_NV = 36905;
    public static final int GL_VIDEO_COLOR_CONVERSION_MAX_NV = 36906;
    public static final int GL_VIDEO_COLOR_CONVERSION_MIN_NV = 36907;
    public static final int GL_VIDEO_COLOR_CONVERSION_OFFSET_NV = 36908;
    public static final int GL_VIDEO_BUFFER_INTERNAL_FORMAT_NV = 36909;
    public static final int GL_VIDEO_CAPTURE_SURFACE_ORIGIN_NV = 36924;
    public static final int GL_PARTIAL_SUCCESS_NV = 36910;
    public static final int GL_SUCCESS_NV = 36911;
    public static final int GL_FAILURE_NV = 36912;
    public static final int GL_YCBYCR8_422_NV = 36913;
    public static final int GL_YCBAYCR8A_4224_NV = 36914;
    public static final int GL_Z6Y10Z6CB10Z6Y10Z6CR10_422_NV = 36915;
    public static final int GL_Z6Y10Z6CB10Z6A10Z6Y10Z6CR10Z6A10_4224_NV = 36916;
    public static final int GL_Z4Y12Z4CB12Z4Y12Z4CR12_422_NV = 36917;
    public static final int GL_Z4Y12Z4CB12Z4A12Z4Y12Z4CR12Z4A12_4224_NV = 36918;
    public static final int GL_Z4Y12Z4CB12Z4CR12_444_NV = 36919;
    public static final int GL_NUM_VIDEO_CAPTURE_SLOTS_NV = 8399;
    public static final int GL_UNIQUE_ID_NV = 8398;

    private NVVideoCapture() {
    }

    public static void glBeginVideoCaptureNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBeginVideoCaptureNV;
        BufferChecks.checkFunctionAddress(l);
        NVVideoCapture.nglBeginVideoCaptureNV(n, l);
    }

    static native void nglBeginVideoCaptureNV(int var0, long var1);

    public static void glBindVideoCaptureStreamBufferNV(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glBindVideoCaptureStreamBufferNV;
        BufferChecks.checkFunctionAddress(l2);
        NVVideoCapture.nglBindVideoCaptureStreamBufferNV(n, n2, n3, l, l2);
    }

    static native void nglBindVideoCaptureStreamBufferNV(int var0, int var1, int var2, long var3, long var5);

    public static void glBindVideoCaptureStreamTextureNV(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindVideoCaptureStreamTextureNV;
        BufferChecks.checkFunctionAddress(l);
        NVVideoCapture.nglBindVideoCaptureStreamTextureNV(n, n2, n3, n4, n5, l);
    }

    static native void nglBindVideoCaptureStreamTextureNV(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glEndVideoCaptureNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEndVideoCaptureNV;
        BufferChecks.checkFunctionAddress(l);
        NVVideoCapture.nglEndVideoCaptureNV(n, l);
    }

    static native void nglEndVideoCaptureNV(int var0, long var1);

    public static void glGetVideoCaptureNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVideoCaptureivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        NVVideoCapture.nglGetVideoCaptureivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetVideoCaptureivNV(int var0, int var1, long var2, long var4);

    public static int glGetVideoCaptureiNV(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetVideoCaptureivNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        NVVideoCapture.nglGetVideoCaptureivNV(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetVideoCaptureStreamNV(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVideoCaptureStreamivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        NVVideoCapture.nglGetVideoCaptureStreamivNV(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetVideoCaptureStreamivNV(int var0, int var1, int var2, long var3, long var5);

    public static int glGetVideoCaptureStreamiNV(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetVideoCaptureStreamivNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        NVVideoCapture.nglGetVideoCaptureStreamivNV(n, n2, n3, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetVideoCaptureStreamNV(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVideoCaptureStreamfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 1);
        NVVideoCapture.nglGetVideoCaptureStreamfvNV(n, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetVideoCaptureStreamfvNV(int var0, int var1, int var2, long var3, long var5);

    public static float glGetVideoCaptureStreamfNV(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetVideoCaptureStreamfvNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        NVVideoCapture.nglGetVideoCaptureStreamfvNV(n, n2, n3, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetVideoCaptureStreamNV(int n, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVideoCaptureStreamdvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 1);
        NVVideoCapture.nglGetVideoCaptureStreamdvNV(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetVideoCaptureStreamdvNV(int var0, int var1, int var2, long var3, long var5);

    public static double glGetVideoCaptureStreamdNV(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetVideoCaptureStreamdvNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferDouble((ContextCapabilities)object);
        NVVideoCapture.nglGetVideoCaptureStreamdvNV(n, n2, n3, MemoryUtil.getAddress((DoubleBuffer)object), l);
        return ((DoubleBuffer)object).get(0);
    }

    public static int glVideoCaptureNV(int n, IntBuffer intBuffer, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVideoCaptureNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        BufferChecks.checkBuffer(longBuffer, 1);
        n = NVVideoCapture.nglVideoCaptureNV(n, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(longBuffer), l);
        return n;
    }

    static native int nglVideoCaptureNV(int var0, long var1, long var3, long var5);

    public static void glVideoCaptureStreamParameterNV(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVideoCaptureStreamParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 16);
        NVVideoCapture.nglVideoCaptureStreamParameterivNV(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglVideoCaptureStreamParameterivNV(int var0, int var1, int var2, long var3, long var5);

    public static void glVideoCaptureStreamParameterNV(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVideoCaptureStreamParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 16);
        NVVideoCapture.nglVideoCaptureStreamParameterfvNV(n, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglVideoCaptureStreamParameterfvNV(int var0, int var1, int var2, long var3, long var5);

    public static void glVideoCaptureStreamParameterNV(int n, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVideoCaptureStreamParameterdvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 16);
        NVVideoCapture.nglVideoCaptureStreamParameterdvNV(n, n2, n3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVideoCaptureStreamParameterdvNV(int var0, int var1, int var2, long var3, long var5);
}
