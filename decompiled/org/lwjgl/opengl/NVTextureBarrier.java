/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVTextureBarrier {
    private NVTextureBarrier() {
    }

    public static void glTextureBarrierNV() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureBarrierNV;
        BufferChecks.checkFunctionAddress(l);
        NVTextureBarrier.nglTextureBarrierNV(l);
    }

    static native void nglTextureBarrierNV(long var0);
}
