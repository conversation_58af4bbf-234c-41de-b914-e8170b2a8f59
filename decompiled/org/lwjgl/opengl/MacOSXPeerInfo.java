/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import org.lwjgl.LWJGLException;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.PeerInfo;
import org.lwjgl.opengl.PixelFormat;

abstract class MacOSXPeerInfo
extends PeerInfo {
    MacOSXPeerInfo(PixelFormat pixelFormat, ContextAttribs contextAttribs, boolean bl, boolean bl2, boolean bl3, boolean bl4) {
        super(MacOSXPeerInfo.createHandle());
        boolean bl5 = contextAttribs != null && (3 < contextAttribs.getMajorVersion() || contextAttribs.getMajorVersion() == 3 && 2 <= contextAttribs.getMinorVersion()) && contextAttribs.isProfileCore();
        if (bl5 && !LWJGLUtil.isMacOSXEqualsOrBetterThan(10, 7)) {
            throw new LWJGLException("OpenGL 3.2+ requested, but it requires MacOS X 10.7 or newer");
        }
        this.choosePixelFormat(pixelFormat, bl5, bl, bl2, bl3, bl4);
    }

    private static native ByteBuffer createHandle();

    private void choosePixelFormat(PixelFormat pixelFormat, boolean bl, boolean bl2, boolean bl3, boolean bl4, boolean bl5) {
        MacOSXPeerInfo.nChoosePixelFormat(this.getHandle(), pixelFormat, bl, bl2, bl3, bl4, bl5);
    }

    private static native void nChoosePixelFormat(ByteBuffer var0, PixelFormat var1, boolean var2, boolean var3, boolean var4, boolean var5, boolean var6);

    public void destroy() {
        MacOSXPeerInfo.nDestroy(this.getHandle());
    }

    private static native void nDestroy(ByteBuffer var0);
}
