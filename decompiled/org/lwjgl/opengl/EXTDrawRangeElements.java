/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;

public final class EXTDrawRangeElements {
    public static final int GL_MAX_ELEMENTS_VERTICES_EXT = 33000;
    public static final int GL_MAX_ELEMENTS_INDICES_EXT = 33001;

    private EXTDrawRangeElements() {
    }

    public static void glDrawRangeElementsEXT(int n, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawRangeElementsEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        EXTDrawRangeElements.nglDrawRangeElementsEXT(n, n2, n3, byteBuffer.remaining(), 5121, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glDrawRangeElementsEXT(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawRangeElementsEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        EXTDrawRangeElements.nglDrawRangeElementsEXT(n, n2, n3, intBuffer.remaining(), 5125, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glDrawRangeElementsEXT(int n, int n2, int n3, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawRangeElementsEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        EXTDrawRangeElements.nglDrawRangeElementsEXT(n, n2, n3, shortBuffer.remaining(), 5123, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglDrawRangeElementsEXT(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glDrawRangeElementsEXT(int n, int n2, int n3, int n4, int n5, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glDrawRangeElementsEXT;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureElementVBOenabled(contextCapabilities);
        EXTDrawRangeElements.nglDrawRangeElementsEXTBO(n, n2, n3, n4, n5, l, l2);
    }

    static native void nglDrawRangeElementsEXTBO(int var0, int var1, int var2, int var3, int var4, long var5, long var7);
}
