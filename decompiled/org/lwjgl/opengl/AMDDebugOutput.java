/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.AMDDebugOutputCallback;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.CallbackUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class AMDDebugOutput {
    public static final int GL_MAX_DEBUG_MESSAGE_LENGTH_AMD = 37187;
    public static final int GL_MAX_DEBUG_LOGGED_MESSAGES_AMD = 37188;
    public static final int GL_DEBUG_LOGGED_MESSAGES_AMD = 37189;
    public static final int GL_DEBUG_SEVERITY_HIGH_AMD = 37190;
    public static final int GL_DEBUG_SEVERITY_MEDIUM_AMD = 37191;
    public static final int GL_DEBUG_SEVERITY_LOW_AMD = 37192;
    public static final int GL_DEBUG_CATEGORY_API_ERROR_AMD = 37193;
    public static final int GL_DEBUG_CATEGORY_WINDOW_SYSTEM_AMD = 37194;
    public static final int GL_DEBUG_CATEGORY_DEPRECATION_AMD = 37195;
    public static final int GL_DEBUG_CATEGORY_UNDEFINED_BEHAVIOR_AMD = 37196;
    public static final int GL_DEBUG_CATEGORY_PERFORMANCE_AMD = 37197;
    public static final int GL_DEBUG_CATEGORY_SHADER_COMPILER_AMD = 37198;
    public static final int GL_DEBUG_CATEGORY_APPLICATION_AMD = 37199;
    public static final int GL_DEBUG_CATEGORY_OTHER_AMD = 37200;

    private AMDDebugOutput() {
    }

    public static void glDebugMessageEnableAMD(int n, int n2, IntBuffer intBuffer, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDebugMessageEnableAMD;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkDirect(intBuffer);
        }
        AMDDebugOutput.nglDebugMessageEnableAMD(n, n2, intBuffer == null ? 0 : intBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), bl, l);
    }

    static native void nglDebugMessageEnableAMD(int var0, int var1, int var2, long var3, boolean var5, long var6);

    public static void glDebugMessageInsertAMD(int n, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDebugMessageInsertAMD;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        AMDDebugOutput.nglDebugMessageInsertAMD(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglDebugMessageInsertAMD(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glDebugMessageInsertAMD(int n, int n2, int n3, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDebugMessageInsertAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDDebugOutput.nglDebugMessageInsertAMD(n, n2, n3, charSequence.length(), APIUtil.getBuffer(contextCapabilities, charSequence), l);
    }

    public static void glDebugMessageCallbackAMD(AMDDebugOutputCallback aMDDebugOutputCallback) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDebugMessageCallbackAMD;
        BufferChecks.checkFunctionAddress(l);
        long l2 = aMDDebugOutputCallback == null ? 0L : CallbackUtil.createGlobalRef(aMDDebugOutputCallback.getHandler());
        CallbackUtil.registerContextCallbackAMD(l2);
        AMDDebugOutput.nglDebugMessageCallbackAMD(aMDDebugOutputCallback == null ? 0L : aMDDebugOutputCallback.getPointer(), l2, l);
    }

    static native void nglDebugMessageCallbackAMD(long var0, long var2, long var4);

    public static int glGetDebugMessageLogAMD(int n, IntBuffer intBuffer, IntBuffer intBuffer2, IntBuffer intBuffer3, IntBuffer intBuffer4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetDebugMessageLogAMD;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, n);
        }
        if (intBuffer2 != null) {
            BufferChecks.checkBuffer(intBuffer2, n);
        }
        if (intBuffer3 != null) {
            BufferChecks.checkBuffer(intBuffer3, n);
        }
        if (intBuffer4 != null) {
            BufferChecks.checkBuffer(intBuffer4, n);
        }
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        n = AMDDebugOutput.nglGetDebugMessageLogAMD(n, byteBuffer == null ? 0 : byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddressSafe(intBuffer2), MemoryUtil.getAddressSafe(intBuffer3), MemoryUtil.getAddressSafe(intBuffer4), MemoryUtil.getAddressSafe(byteBuffer), l);
        return n;
    }

    static native int nglGetDebugMessageLogAMD(int var0, int var1, long var2, long var4, long var6, long var8, long var10, long var12);
}
