/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Canvas;
import java.awt.GraphicsConfiguration;
import java.awt.GraphicsDevice;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.PeerInfo;
import org.lwjgl.opengl.PixelFormat;

interface AWTCanvasImplementation {
    public PeerInfo createPeerInfo(Canvas var1, PixelFormat var2, ContextAttribs var3);

    public GraphicsConfiguration findConfiguration(GraphicsDevice var1, PixelFormat var2);
}
