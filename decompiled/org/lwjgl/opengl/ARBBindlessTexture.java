/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.LongBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ARBBindlessTexture {
    public static final int GL_UNSIGNED_INT64_ARB = 5135;

    private ARBBindlessTexture() {
    }

    public static long glGetTextureHandleARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureHandleARB;
        BufferChecks.checkFunctionAddress(l);
        long l2 = ARBBindlessTexture.nglGetTextureHandleARB(n, l);
        return l2;
    }

    static native long nglGetTextureHandleARB(int var0, long var1);

    public static long glGetTextureSamplerHandleARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureSamplerHandleARB;
        BufferChecks.checkFunctionAddress(l);
        long l2 = ARBBindlessTexture.nglGetTextureSamplerHandleARB(n, n2, l);
        return l2;
    }

    static native long nglGetTextureSamplerHandleARB(int var0, int var1, long var2);

    public static void glMakeTextureHandleResidentARB(long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMakeTextureHandleResidentARB;
        BufferChecks.checkFunctionAddress(l2);
        ARBBindlessTexture.nglMakeTextureHandleResidentARB(l, l2);
    }

    static native void nglMakeTextureHandleResidentARB(long var0, long var2);

    public static void glMakeTextureHandleNonResidentARB(long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMakeTextureHandleNonResidentARB;
        BufferChecks.checkFunctionAddress(l2);
        ARBBindlessTexture.nglMakeTextureHandleNonResidentARB(l, l2);
    }

    static native void nglMakeTextureHandleNonResidentARB(long var0, long var2);

    public static long glGetImageHandleARB(int n, int n2, boolean bl, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetImageHandleARB;
        BufferChecks.checkFunctionAddress(l);
        long l2 = ARBBindlessTexture.nglGetImageHandleARB(n, n2, bl, n3, n4, l);
        return l2;
    }

    static native long nglGetImageHandleARB(int var0, int var1, boolean var2, int var3, int var4, long var5);

    public static void glMakeImageHandleResidentARB(long l, int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMakeImageHandleResidentARB;
        BufferChecks.checkFunctionAddress(l2);
        ARBBindlessTexture.nglMakeImageHandleResidentARB(l, n, l2);
    }

    static native void nglMakeImageHandleResidentARB(long var0, int var2, long var3);

    public static void glMakeImageHandleNonResidentARB(long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMakeImageHandleNonResidentARB;
        BufferChecks.checkFunctionAddress(l2);
        ARBBindlessTexture.nglMakeImageHandleNonResidentARB(l, l2);
    }

    static native void nglMakeImageHandleNonResidentARB(long var0, long var2);

    public static void glUniformHandleui64ARB(int n, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glUniformHandleui64ARB;
        BufferChecks.checkFunctionAddress(l2);
        ARBBindlessTexture.nglUniformHandleui64ARB(n, l, l2);
    }

    static native void nglUniformHandleui64ARB(int var0, long var1, long var3);

    public static void glUniformHandleuARB(int n, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformHandleui64vARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(longBuffer);
        ARBBindlessTexture.nglUniformHandleui64vARB(n, longBuffer.remaining(), MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglUniformHandleui64vARB(int var0, int var1, long var2, long var4);

    public static void glProgramUniformHandleui64ARB(int n, int n2, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glProgramUniformHandleui64ARB;
        BufferChecks.checkFunctionAddress(l2);
        ARBBindlessTexture.nglProgramUniformHandleui64ARB(n, n2, l, l2);
    }

    static native void nglProgramUniformHandleui64ARB(int var0, int var1, long var2, long var4);

    public static void glProgramUniformHandleuARB(int n, int n2, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformHandleui64vARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(longBuffer);
        ARBBindlessTexture.nglProgramUniformHandleui64vARB(n, n2, longBuffer.remaining(), MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglProgramUniformHandleui64vARB(int var0, int var1, int var2, long var3, long var5);

    public static boolean glIsTextureHandleResidentARB(long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glIsTextureHandleResidentARB;
        BufferChecks.checkFunctionAddress(l2);
        boolean bl = ARBBindlessTexture.nglIsTextureHandleResidentARB(l, l2);
        return bl;
    }

    static native boolean nglIsTextureHandleResidentARB(long var0, long var2);

    public static boolean glIsImageHandleResidentARB(long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glIsImageHandleResidentARB;
        BufferChecks.checkFunctionAddress(l2);
        boolean bl = ARBBindlessTexture.nglIsImageHandleResidentARB(l, l2);
        return bl;
    }

    static native boolean nglIsImageHandleResidentARB(long var0, long var2);

    public static void glVertexAttribL1ui64ARB(int n, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glVertexAttribL1ui64ARB;
        BufferChecks.checkFunctionAddress(l2);
        ARBBindlessTexture.nglVertexAttribL1ui64ARB(n, l, l2);
    }

    static native void nglVertexAttribL1ui64ARB(int var0, long var1, long var3);

    public static void glVertexAttribL1uARB(int n, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL1ui64vARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 1);
        ARBBindlessTexture.nglVertexAttribL1ui64vARB(n, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglVertexAttribL1ui64vARB(int var0, long var1, long var3);

    public static void glGetVertexAttribLuARB(int n, int n2, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribLui64vARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 4);
        ARBBindlessTexture.nglGetVertexAttribLui64vARB(n, n2, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetVertexAttribLui64vARB(int var0, int var1, long var2, long var4);
}
