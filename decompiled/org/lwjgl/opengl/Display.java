/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Canvas;
import java.awt.event.ComponentAdapter;
import java.awt.event.ComponentEvent;
import java.awt.event.ComponentListener;
import java.nio.ByteBuffer;
import java.nio.FloatBuffer;
import java.security.AccessController;
import java.security.PrivilegedAction;
import java.util.Arrays;
import java.util.HashSet;
import org.lwjgl.BufferUtils;
import org.lwjgl.LWJGLException;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.Sys;
import org.lwjgl.input.Controllers;
import org.lwjgl.input.Keyboard;
import org.lwjgl.input.Mouse;
import org.lwjgl.opengl.Context;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.ContextGL;
import org.lwjgl.opengl.DisplayImplementation;
import org.lwjgl.opengl.DisplayMode;
import org.lwjgl.opengl.Drawable;
import org.lwjgl.opengl.DrawableGL;
import org.lwjgl.opengl.DrawableGLES;
import org.lwjgl.opengl.DrawableLWJGL;
import org.lwjgl.opengl.GlobalLock;
import org.lwjgl.opengl.LinuxDisplay;
import org.lwjgl.opengl.MacOSXDisplay;
import org.lwjgl.opengl.OpenGLException;
import org.lwjgl.opengl.PixelFormat;
import org.lwjgl.opengl.PixelFormatLWJGL;
import org.lwjgl.opengl.Sync;
import org.lwjgl.opengl.WindowsDisplay;

public final class Display {
    private static final Thread shutdown_hook = new Thread(){

        public final void run() {
            Display.reset();
        }
    };
    private static final DisplayImplementation display_impl;
    private static final DisplayMode initial_mode;
    private static Canvas parent;
    private static DisplayMode current_mode;
    private static int x;
    private static ByteBuffer[] cached_icons;
    private static int y;
    private static int width;
    private static int height;
    private static String title;
    private static boolean fullscreen;
    private static int swap_interval;
    private static DrawableLWJGL drawable;
    private static boolean window_created;
    private static boolean parent_resized;
    private static boolean window_resized;
    private static boolean window_resizable;
    private static float r;
    private static float g;
    private static float b;
    private static final ComponentListener component_listener;

    public static Drawable getDrawable() {
        return drawable;
    }

    private static DisplayImplementation createDisplayImplementation() {
        switch (LWJGLUtil.getPlatform()) {
            case 1: {
                return new LinuxDisplay();
            }
            case 3: {
                return new WindowsDisplay();
            }
            case 2: {
                return new MacOSXDisplay();
            }
        }
        throw new IllegalStateException("Unsupported platform");
    }

    private Display() {
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static DisplayMode[] getAvailableDisplayModes() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            DisplayMode[] displayModeArray = display_impl.getAvailableDisplayModes();
            if (displayModeArray == null) {
                return new DisplayMode[0];
            }
            HashSet<DisplayMode> hashSet = new HashSet<DisplayMode>(displayModeArray.length);
            hashSet.addAll(Arrays.asList(displayModeArray));
            DisplayMode[] displayModeArray2 = new DisplayMode[hashSet.size()];
            hashSet.toArray(displayModeArray2);
            LWJGLUtil.log("Removed " + (displayModeArray.length - displayModeArray2.length) + " duplicate displaymodes");
            return displayModeArray2;
        }
    }

    public static DisplayMode getDesktopDisplayMode() {
        return initial_mode;
    }

    public static DisplayMode getDisplayMode() {
        return current_mode;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void setDisplayMode(DisplayMode displayMode) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (displayMode == null) {
                throw new NullPointerException("mode must be non-null");
            }
            boolean bl = Display.isFullscreen();
            current_mode = displayMode;
            if (Display.isCreated()) {
                Display.destroyWindow();
                try {
                    if (bl && !Display.isFullscreen()) {
                        display_impl.resetDisplayMode();
                    } else if (Display.isFullscreen()) {
                        Display.switchDisplayMode();
                    }
                    Display.createWindow();
                    Display.makeCurrentAndSetSwapInterval();
                }
                catch (LWJGLException lWJGLException) {
                    drawable.destroy();
                    display_impl.resetDisplayMode();
                    throw lWJGLException;
                }
            }
            return;
        }
    }

    private static DisplayMode getEffectiveMode() {
        if (!Display.isFullscreen() && parent != null) {
            return new DisplayMode(parent.getWidth(), parent.getHeight());
        }
        return current_mode;
    }

    private static int getWindowX() {
        if (!Display.isFullscreen() && parent == null) {
            if (x == -1) {
                return Math.max(0, (initial_mode.getWidth() - current_mode.getWidth()) / 2);
            }
            return x;
        }
        return 0;
    }

    private static int getWindowY() {
        if (!Display.isFullscreen() && parent == null) {
            if (y == -1) {
                return Math.max(0, (initial_mode.getHeight() - current_mode.getHeight()) / 2);
            }
            return y;
        }
        return 0;
    }

    private static void createWindow() {
        if (window_created) {
            return;
        }
        Canvas canvas = Display.isFullscreen() ? null : parent;
        if (canvas != null && !canvas.isDisplayable()) {
            throw new LWJGLException("Parent.isDisplayable() must be true");
        }
        if (canvas != null) {
            canvas.addComponentListener(component_listener);
        }
        DisplayMode displayMode = Display.getEffectiveMode();
        display_impl.createWindow(drawable, displayMode, canvas, Display.getWindowX(), Display.getWindowY());
        window_created = true;
        width = Display.getDisplayMode().getWidth();
        height = Display.getDisplayMode().getHeight();
        Display.setTitle(title);
        Display.initControls();
        if (cached_icons != null) {
            Display.setIcon(cached_icons);
            return;
        }
        Display.setIcon(new ByteBuffer[]{LWJGLUtil.LWJGLIcon32x32, LWJGLUtil.LWJGLIcon16x16});
    }

    private static void releaseDrawable() {
        try {
            Context context = drawable.getContext();
            if (context != null && context.isCurrent()) {
                context.releaseCurrent();
                context.releaseDrawable();
            }
            return;
        }
        catch (LWJGLException lWJGLException) {
            LWJGLUtil.log("Exception occurred while trying to release context: " + lWJGLException);
            return;
        }
    }

    private static void destroyWindow() {
        if (!window_created) {
            return;
        }
        if (parent != null) {
            parent.removeComponentListener(component_listener);
        }
        Display.releaseDrawable();
        if (Mouse.isCreated()) {
            Mouse.destroy();
        }
        if (Keyboard.isCreated()) {
            Keyboard.destroy();
        }
        display_impl.destroyWindow();
        window_created = false;
    }

    private static void switchDisplayMode() {
        if (!current_mode.isFullscreenCapable()) {
            throw new IllegalStateException("Only modes acquired from getAvailableDisplayModes() can be used for fullscreen display");
        }
        display_impl.switchDisplayMode(current_mode);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void setDisplayConfiguration(float f, float f2, float f3) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (!Display.isCreated()) {
                throw new LWJGLException("Display not yet created.");
            }
            if (f2 < -1.0f || f2 > 1.0f) {
                throw new IllegalArgumentException("Invalid brightness value");
            }
            if (f3 < 0.0f) {
                throw new IllegalArgumentException("Invalid contrast value");
            }
            int n = display_impl.getGammaRampLength();
            if (n == 0) {
                throw new LWJGLException("Display configuration not supported");
            }
            FloatBuffer floatBuffer = BufferUtils.createFloatBuffer(n);
            for (int i = 0; i < n; ++i) {
                float f4;
                float f5 = (float)i / (float)(n - 1);
                f5 = (float)Math.pow(f5, f);
                f5 += f2;
                f5 = (f5 - 0.5f) * f3 + 0.5f;
                if (f4 > 1.0f) {
                    f5 = 1.0f;
                } else if (f5 < 0.0f) {
                    f5 = 0.0f;
                }
                floatBuffer.put(i, f5);
            }
            display_impl.setGammaRamp(floatBuffer);
            LWJGLUtil.log("Gamma set, gamma = " + f + ", brightness = " + f2 + ", contrast = " + f3);
            return;
        }
    }

    public static void sync(int n) {
        Sync.sync(n);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static String getTitle() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            return title;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static Canvas getParent() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            return parent;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void setParent(Canvas canvas) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (parent != canvas) {
                parent = canvas;
                if (!Display.isCreated()) {
                    return;
                }
                Display.destroyWindow();
                try {
                    if (Display.isFullscreen()) {
                        Display.switchDisplayMode();
                    } else {
                        display_impl.resetDisplayMode();
                    }
                    Display.createWindow();
                    Display.makeCurrentAndSetSwapInterval();
                }
                catch (LWJGLException lWJGLException) {
                    drawable.destroy();
                    display_impl.resetDisplayMode();
                    throw lWJGLException;
                }
            }
            return;
        }
    }

    public static void setFullscreen(boolean bl) {
        Display.setDisplayModeAndFullscreenInternal(bl, current_mode);
    }

    public static void setDisplayModeAndFullscreen(DisplayMode displayMode) {
        Display.setDisplayModeAndFullscreenInternal(displayMode.isFullscreenCapable(), displayMode);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private static void setDisplayModeAndFullscreenInternal(boolean bl, DisplayMode displayMode) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (displayMode == null) {
                throw new NullPointerException("mode must be non-null");
            }
            DisplayMode displayMode2 = current_mode;
            current_mode = displayMode;
            boolean bl2 = Display.isFullscreen();
            fullscreen = bl;
            if (bl2 != Display.isFullscreen() || !displayMode.equals(displayMode2)) {
                if (!Display.isCreated()) {
                    return;
                }
                Display.destroyWindow();
                try {
                    if (Display.isFullscreen()) {
                        Display.switchDisplayMode();
                    } else {
                        display_impl.resetDisplayMode();
                    }
                    Display.createWindow();
                    Display.makeCurrentAndSetSwapInterval();
                }
                catch (LWJGLException lWJGLException) {
                    drawable.destroy();
                    display_impl.resetDisplayMode();
                    throw lWJGLException;
                }
            }
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static boolean isFullscreen() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            return fullscreen && current_mode.isFullscreenCapable();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void setTitle(String string) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (string == null) {
                string = "";
            }
            title = string;
            if (Display.isCreated()) {
                display_impl.setTitle(title);
            }
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static boolean isCloseRequested() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (!Display.isCreated()) {
                throw new IllegalStateException("Cannot determine close requested state of uncreated window");
            }
            return display_impl.isCloseRequested();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static boolean isVisible() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (!Display.isCreated()) {
                throw new IllegalStateException("Cannot determine minimized state of uncreated window");
            }
            return display_impl.isVisible();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static boolean isActive() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (!Display.isCreated()) {
                throw new IllegalStateException("Cannot determine focused state of uncreated window");
            }
            return display_impl.isActive();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static boolean isDirty() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (!Display.isCreated()) {
                throw new IllegalStateException("Cannot determine dirty state of uncreated window");
            }
            return display_impl.isDirty();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void processMessages() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (!Display.isCreated()) {
                throw new IllegalStateException("Display not created");
            }
            display_impl.update();
        }
        Display.pollDevices();
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void swapBuffers() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (!Display.isCreated()) {
                throw new IllegalStateException("Display not created");
            }
            if (LWJGLUtil.DEBUG) {
                drawable.checkGLError();
            }
            drawable.swapBuffers();
            return;
        }
    }

    public static void update() {
        Display.update(true);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void update(boolean bl) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (!Display.isCreated()) {
                throw new IllegalStateException("Display not created");
            }
            if (display_impl.isVisible() || display_impl.isDirty()) {
                try {
                    Display.swapBuffers();
                }
                catch (LWJGLException lWJGLException) {
                    throw new RuntimeException(lWJGLException);
                }
            }
            if (window_resized = !Display.isFullscreen() && parent == null && display_impl.wasResized()) {
                width = display_impl.getWidth();
                height = display_impl.getHeight();
            }
            if (parent_resized) {
                Display.reshape();
                parent_resized = false;
                window_resized = true;
            }
            if (bl) {
                Display.processMessages();
            }
            return;
        }
    }

    static void pollDevices() {
        if (Mouse.isCreated()) {
            Mouse.poll();
            Mouse.updateCursor();
        }
        if (Keyboard.isCreated()) {
            Keyboard.poll();
        }
        if (Controllers.isCreated()) {
            Controllers.poll();
        }
    }

    public static void releaseContext() {
        drawable.releaseContext();
    }

    public static boolean isCurrent() {
        return drawable.isCurrent();
    }

    public static void makeCurrent() {
        drawable.makeCurrent();
    }

    private static void removeShutdownHook() {
        AccessController.doPrivileged(new PrivilegedAction<Object>(){

            @Override
            public final Object run() {
                Runtime.getRuntime().removeShutdownHook(shutdown_hook);
                return null;
            }
        });
    }

    private static void registerShutdownHook() {
        AccessController.doPrivileged(new PrivilegedAction<Object>(){

            @Override
            public final Object run() {
                Runtime.getRuntime().addShutdownHook(shutdown_hook);
                return null;
            }
        });
    }

    public static void create() {
        Display.create(new PixelFormat());
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void create(PixelFormat pixelFormat) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            Display.create(pixelFormat, null, null);
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void create(PixelFormat pixelFormat, Drawable drawable) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            Display.create(pixelFormat, drawable, null);
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void create(PixelFormat pixelFormat, ContextAttribs contextAttribs) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            Display.create(pixelFormat, null, contextAttribs);
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void create(PixelFormat pixelFormat, Drawable drawable, ContextAttribs contextAttribs) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (Display.isCreated()) {
                throw new IllegalStateException("Only one LWJGL context may be instantiated at any one time.");
            }
            if (pixelFormat == null) {
                throw new NullPointerException("pixel_format cannot be null");
            }
            Display.removeShutdownHook();
            Display.registerShutdownHook();
            if (Display.isFullscreen()) {
                Display.switchDisplayMode();
            }
            DrawableGL drawableGL = new DrawableGL(){

                /*
                 * WARNING - Removed try catching itself - possible behaviour change.
                 */
                public final void destroy() {
                    Object object = GlobalLock.lock;
                    synchronized (object) {
                        if (!Display.isCreated()) {
                            return;
                        }
                        Display.releaseDrawable();
                        super.destroy();
                        Display.destroyWindow();
                        x = (y = -1);
                        Display.access$702(null);
                        Display.reset();
                        Display.removeShutdownHook();
                        return;
                    }
                }
            };
            Display.drawable = drawableGL;
            try {
                drawableGL.setPixelFormat(pixelFormat, contextAttribs);
                try {
                    Display.createWindow();
                    try {
                        drawableGL.context = new ContextGL(drawableGL.peer_info, contextAttribs, drawable != null ? ((DrawableGL)drawable).getContext() : null);
                        try {
                            Display.makeCurrentAndSetSwapInterval();
                            Display.initContext();
                        }
                        catch (LWJGLException lWJGLException) {
                            drawableGL.destroy();
                            throw lWJGLException;
                        }
                    }
                    catch (LWJGLException lWJGLException) {
                        Display.destroyWindow();
                        throw lWJGLException;
                    }
                }
                catch (LWJGLException lWJGLException) {
                    drawableGL.destroy();
                    throw lWJGLException;
                }
            }
            catch (LWJGLException lWJGLException) {
                display_impl.resetDisplayMode();
                throw lWJGLException;
            }
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void create(PixelFormatLWJGL pixelFormatLWJGL) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            Display.create(pixelFormatLWJGL, null, null);
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void create(PixelFormatLWJGL pixelFormatLWJGL, Drawable drawable) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            Display.create(pixelFormatLWJGL, drawable, null);
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void create(PixelFormatLWJGL pixelFormatLWJGL, org.lwjgl.opengles.ContextAttribs contextAttribs) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            Display.create(pixelFormatLWJGL, null, contextAttribs);
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void create(PixelFormatLWJGL pixelFormatLWJGL, Drawable drawable, org.lwjgl.opengles.ContextAttribs contextAttribs) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (Display.isCreated()) {
                throw new IllegalStateException("Only one LWJGL context may be instantiated at any one time.");
            }
            if (pixelFormatLWJGL == null) {
                throw new NullPointerException("pixel_format cannot be null");
            }
            Display.removeShutdownHook();
            Display.registerShutdownHook();
            if (Display.isFullscreen()) {
                Display.switchDisplayMode();
            }
            DrawableGLES drawableGLES = new DrawableGLES(){

                public final void setPixelFormat(PixelFormatLWJGL pixelFormatLWJGL, ContextAttribs contextAttribs) {
                    throw new UnsupportedOperationException();
                }

                /*
                 * WARNING - Removed try catching itself - possible behaviour change.
                 */
                public final void destroy() {
                    Object object = GlobalLock.lock;
                    synchronized (object) {
                        if (!Display.isCreated()) {
                            return;
                        }
                        Display.releaseDrawable();
                        super.destroy();
                        Display.destroyWindow();
                        x = (y = -1);
                        Display.access$702(null);
                        Display.reset();
                        Display.removeShutdownHook();
                        return;
                    }
                }
            };
            Display.drawable = drawableGLES;
            try {
                drawableGLES.setPixelFormat(pixelFormatLWJGL);
                try {
                    Display.createWindow();
                    try {
                        drawableGLES.createContext(contextAttribs, drawable);
                        try {
                            Display.makeCurrentAndSetSwapInterval();
                            Display.initContext();
                        }
                        catch (LWJGLException lWJGLException) {
                            drawableGLES.destroy();
                            throw lWJGLException;
                        }
                    }
                    catch (LWJGLException lWJGLException) {
                        Display.destroyWindow();
                        throw lWJGLException;
                    }
                }
                catch (LWJGLException lWJGLException) {
                    drawableGLES.destroy();
                    throw lWJGLException;
                }
            }
            catch (LWJGLException lWJGLException) {
                display_impl.resetDisplayMode();
                throw lWJGLException;
            }
            return;
        }
    }

    public static void setInitialBackground(float f, float f2, float f3) {
        r = f;
        g = f2;
        b = f3;
    }

    private static void makeCurrentAndSetSwapInterval() {
        Display.makeCurrent();
        try {
            drawable.checkGLError();
        }
        catch (OpenGLException openGLException) {
            LWJGLUtil.log("OpenGL error during context creation: " + openGLException.getMessage());
        }
        Display.setSwapInterval(swap_interval);
    }

    private static void initContext() {
        drawable.initContext(r, g, b);
        Display.update();
    }

    static DisplayImplementation getImplementation() {
        return display_impl;
    }

    static boolean getPrivilegedBoolean(final String string) {
        return AccessController.doPrivileged(new PrivilegedAction<Boolean>(){

            @Override
            public final Boolean run() {
                return Boolean.getBoolean(string);
            }
        });
    }

    static String getPrivilegedString(final String string) {
        return AccessController.doPrivileged(new PrivilegedAction<String>(){

            @Override
            public final String run() {
                return System.getProperty(string);
            }
        });
    }

    private static void initControls() {
        if (!Display.getPrivilegedBoolean("org.lwjgl.opengl.Display.noinput")) {
            if (!Mouse.isCreated() && !Display.getPrivilegedBoolean("org.lwjgl.opengl.Display.nomouse")) {
                try {
                    Mouse.create();
                }
                catch (LWJGLException lWJGLException) {
                    if (LWJGLUtil.DEBUG) {
                        lWJGLException.printStackTrace(System.err);
                    }
                    LWJGLUtil.log("Failed to create Mouse: " + lWJGLException);
                }
            }
            if (!Keyboard.isCreated() && !Display.getPrivilegedBoolean("org.lwjgl.opengl.Display.nokeyboard")) {
                try {
                    Keyboard.create();
                    return;
                }
                catch (LWJGLException lWJGLException) {
                    if (LWJGLUtil.DEBUG) {
                        lWJGLException.printStackTrace(System.err);
                        return;
                    }
                    LWJGLUtil.log("Failed to create Keyboard: " + lWJGLException);
                }
            }
        }
    }

    public static void destroy() {
        if (Display.isCreated()) {
            drawable.destroy();
        }
    }

    private static void reset() {
        display_impl.resetDisplayMode();
        current_mode = initial_mode;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static boolean isCreated() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            return window_created;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void setSwapInterval(int n) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            swap_interval = n;
            if (Display.isCreated()) {
                drawable.setSwapInterval(swap_interval);
            }
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void setVSyncEnabled(boolean bl) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            Display.setSwapInterval(bl ? 1 : 0);
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static void setLocation(int n, int n2) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            x = n;
            y = n2;
            if (Display.isCreated() && !Display.isFullscreen()) {
                Display.reshape();
            }
            return;
        }
    }

    private static void reshape() {
        DisplayMode displayMode = Display.getEffectiveMode();
        display_impl.reshape(Display.getWindowX(), Display.getWindowY(), displayMode.getWidth(), displayMode.getHeight());
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static String getAdapter() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            return display_impl.getAdapter();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static String getVersion() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            return display_impl.getVersion();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public static int setIcon(ByteBuffer[] byteBufferArray) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (cached_icons != byteBufferArray) {
                cached_icons = new ByteBuffer[byteBufferArray.length];
                for (int i = 0; i < byteBufferArray.length; ++i) {
                    Display.cached_icons[i] = BufferUtils.createByteBuffer(byteBufferArray[i].capacity());
                    int n = byteBufferArray[i].position();
                    cached_icons[i].put(byteBufferArray[i]);
                    byteBufferArray[i].position(n);
                    cached_icons[i].flip();
                }
            }
            if (Display.isCreated() && parent == null) {
                return display_impl.setIcon(cached_icons);
            }
            return 0;
        }
    }

    public static void setResizable(boolean bl) {
        window_resizable = bl;
        if (Display.isCreated()) {
            display_impl.setResizable(bl);
        }
    }

    public static boolean isResizable() {
        return window_resizable;
    }

    public static boolean wasResized() {
        return window_resized;
    }

    public static int getX() {
        if (Display.isFullscreen()) {
            return 0;
        }
        if (parent != null) {
            return parent.getX();
        }
        return display_impl.getX();
    }

    public static int getY() {
        if (Display.isFullscreen()) {
            return 0;
        }
        if (parent != null) {
            return parent.getY();
        }
        return display_impl.getY();
    }

    public static int getWidth() {
        if (Display.isFullscreen()) {
            return Display.getDisplayMode().getWidth();
        }
        if (parent != null) {
            return parent.getWidth();
        }
        return width;
    }

    public static int getHeight() {
        if (Display.isFullscreen()) {
            return Display.getDisplayMode().getHeight();
        }
        if (parent != null) {
            return parent.getHeight();
        }
        return height;
    }

    public static float getPixelScaleFactor() {
        return display_impl.getPixelScaleFactor();
    }

    static /* synthetic */ ByteBuffer[] access$702(ByteBuffer[] byteBufferArray) {
        cached_icons = byteBufferArray;
        return byteBufferArray;
    }

    static {
        x = -1;
        y = -1;
        width = 0;
        height = 0;
        title = "Game";
        component_listener = new ComponentAdapter(){

            /*
             * WARNING - Removed try catching itself - possible behaviour change.
             */
            public final void componentResized(ComponentEvent object) {
                object = GlobalLock.lock;
                synchronized (object) {
                    parent_resized = true;
                    return;
                }
            }
        };
        Sys.initialize();
        display_impl = Display.createDisplayImplementation();
        try {
            current_mode = initial_mode = display_impl.init();
            LWJGLUtil.log("Initial mode: " + initial_mode);
            return;
        }
        catch (LWJGLException lWJGLException) {
            throw new RuntimeException(lWJGLException);
        }
    }
}
