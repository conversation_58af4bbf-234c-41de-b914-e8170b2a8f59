/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVPointSprite {
    public static final int GL_POINT_SPRITE_NV = 34913;
    public static final int GL_COORD_REPLACE_NV = 34914;
    public static final int GL_POINT_SPRITE_R_MODE_NV = 34915;

    private NVPointSprite() {
    }

    public static void glPointParameteriNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPointParameteriNV;
        BufferChecks.checkFunctionAddress(l);
        NVPointSprite.nglPointParameteriNV(n, n2, l);
    }

    static native void nglPointParameteriNV(int var0, int var1, long var2);

    public static void glPointParameterNV(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPointParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVPointSprite.nglPointParameterivNV(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglPointParameterivNV(int var0, long var1, long var3);
}
