/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVGpuProgram4 {
    public static final int GL_PROGRAM_ATTRIB_COMPONENTS_NV = 35078;
    public static final int GL_PROGRAM_RESULT_COMPONENTS_NV = 35079;
    public static final int GL_MAX_PROGRAM_ATTRIB_COMPONENTS_NV = 35080;
    public static final int GL_MAX_PROGRAM_RESULT_COMPONENTS_NV = 35081;
    public static final int GL_MAX_PROGRAM_GENERIC_ATTRIBS_NV = 36261;
    public static final int GL_MAX_PROGRAM_GENERIC_RESULTS_NV = 36262;

    private NVGpuProgram4() {
    }

    public static void glProgramLocalParameterI4iNV(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramLocalParameterI4iNV;
        BufferChecks.checkFunctionAddress(l);
        NVGpuProgram4.nglProgramLocalParameterI4iNV(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglProgramLocalParameterI4iNV(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glProgramLocalParameterI4NV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramLocalParameterI4ivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVGpuProgram4.nglProgramLocalParameterI4ivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramLocalParameterI4ivNV(int var0, int var1, long var2, long var4);

    public static void glProgramLocalParametersI4NV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramLocalParametersI4ivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        NVGpuProgram4.nglProgramLocalParametersI4ivNV(n, n2, intBuffer.remaining() >> 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramLocalParametersI4ivNV(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramLocalParameterI4uiNV(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramLocalParameterI4uiNV;
        BufferChecks.checkFunctionAddress(l);
        NVGpuProgram4.nglProgramLocalParameterI4uiNV(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglProgramLocalParameterI4uiNV(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glProgramLocalParameterI4uNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramLocalParameterI4uivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVGpuProgram4.nglProgramLocalParameterI4uivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramLocalParameterI4uivNV(int var0, int var1, long var2, long var4);

    public static void glProgramLocalParametersI4uNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramLocalParametersI4uivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        NVGpuProgram4.nglProgramLocalParametersI4uivNV(n, n2, intBuffer.remaining() >> 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramLocalParametersI4uivNV(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramEnvParameterI4iNV(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramEnvParameterI4iNV;
        BufferChecks.checkFunctionAddress(l);
        NVGpuProgram4.nglProgramEnvParameterI4iNV(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglProgramEnvParameterI4iNV(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glProgramEnvParameterI4NV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramEnvParameterI4ivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVGpuProgram4.nglProgramEnvParameterI4ivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramEnvParameterI4ivNV(int var0, int var1, long var2, long var4);

    public static void glProgramEnvParametersI4NV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramEnvParametersI4ivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        NVGpuProgram4.nglProgramEnvParametersI4ivNV(n, n2, intBuffer.remaining() >> 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramEnvParametersI4ivNV(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramEnvParameterI4uiNV(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramEnvParameterI4uiNV;
        BufferChecks.checkFunctionAddress(l);
        NVGpuProgram4.nglProgramEnvParameterI4uiNV(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglProgramEnvParameterI4uiNV(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glProgramEnvParameterI4uNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramEnvParameterI4uivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVGpuProgram4.nglProgramEnvParameterI4uivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramEnvParameterI4uivNV(int var0, int var1, long var2, long var4);

    public static void glProgramEnvParametersI4uNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramEnvParametersI4uivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        NVGpuProgram4.nglProgramEnvParametersI4uivNV(n, n2, intBuffer.remaining() >> 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramEnvParametersI4uivNV(int var0, int var1, int var2, long var3, long var5);

    public static void glGetProgramLocalParameterINV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramLocalParameterIivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVGpuProgram4.nglGetProgramLocalParameterIivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetProgramLocalParameterIivNV(int var0, int var1, long var2, long var4);

    public static void glGetProgramLocalParameterIuNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramLocalParameterIuivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVGpuProgram4.nglGetProgramLocalParameterIuivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetProgramLocalParameterIuivNV(int var0, int var1, long var2, long var4);

    public static void glGetProgramEnvParameterINV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramEnvParameterIivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVGpuProgram4.nglGetProgramEnvParameterIivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetProgramEnvParameterIivNV(int var0, int var1, long var2, long var4);

    public static void glGetProgramEnvParameterIuNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramEnvParameterIuivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVGpuProgram4.nglGetProgramEnvParameterIuivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetProgramEnvParameterIuivNV(int var0, int var1, long var2, long var4);
}
