/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GL43;
import org.lwjgl.opengl.GLContext;

public final class ARBClearBufferObject {
    private ARBClearBufferObject() {
    }

    public static void glClearBufferData(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        GL43.glClearBufferData(n, n2, n3, n4, byteBuffer);
    }

    public static void glClearBufferSubData(int n, int n2, long l, long l2, int n3, int n4, ByteBuffer byteBuffer) {
        GL43.glClearBufferSubData(n, n2, l, l2, n3, n4, byteBuffer);
    }

    public static void glClearNamedBufferDataEXT(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearNamedBufferDataEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 1);
        ARBClearBufferObject.nglClearNamedBufferDataEXT(n, n2, n3, n4, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglClearNamedBufferDataEXT(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glClearNamedBufferSubDataEXT(int n, int n2, long l, long l2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glClearNamedBufferSubDataEXT;
        BufferChecks.checkFunctionAddress(l3);
        BufferChecks.checkBuffer(byteBuffer, 1);
        ARBClearBufferObject.nglClearNamedBufferSubDataEXT(n, n2, l, l2, n3, n4, MemoryUtil.getAddress(byteBuffer), l3);
    }

    static native void nglClearNamedBufferSubDataEXT(int var0, int var1, long var2, long var4, int var6, int var7, long var8, long var10);
}
