/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.opengl.PixelFormat;
import org.lwjgl.opengl.WindowsPeerInfo;

final class WindowsPbufferPeerInfo
extends WindowsPeerInfo {
    WindowsPbufferPeerInfo(int n, int n2, PixelFormat pixelFormat, IntBuffer intBuffer, IntBuffer intBuffer2) {
        WindowsPbufferPeerInfo.nCreate(this.getHandle(), n, n2, pixelFormat, intBuffer, intBuffer2);
    }

    private static native void nCreate(ByteBuffer var0, int var1, int var2, PixelFormat var3, IntBuffer var4, IntBuffer var5);

    public final boolean isBufferLost() {
        return WindowsPbufferPeerInfo.nIsBufferLost(this.getHandle());
    }

    private static native boolean nIsBufferLost(ByteBuffer var0);

    public final void setPbufferAttrib(int n, int n2) {
        WindowsPbufferPeerInfo.nSetPbufferAttrib(this.getHandle(), n, n2);
    }

    private static native void nSetPbufferAttrib(ByteBuffer var0, int var1, int var2);

    public final void bindTexImageToPbuffer(int n) {
        WindowsPbufferPeerInfo.nBindTexImageToPbuffer(this.getHandle(), n);
    }

    private static native void nBindTexImageToPbuffer(ByteBuffer var0, int var1);

    public final void releaseTexImageFromPbuffer(int n) {
        WindowsPbufferPeerInfo.nReleaseTexImageFromPbuffer(this.getHandle(), n);
    }

    private static native void nReleaseTexImageFromPbuffer(ByteBuffer var0, int var1);

    public final void destroy() {
        WindowsPbufferPeerInfo.nDestroy(this.getHandle());
    }

    private static native void nDestroy(ByteBuffer var0);

    protected final void doLockAndInitHandle() {
    }

    protected final void doUnlock() {
    }
}
