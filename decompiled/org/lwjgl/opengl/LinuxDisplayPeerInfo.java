/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.lwjgl.opengles.GLContext
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import org.lwjgl.LWJGLException;
import org.lwjgl.opengl.LinuxDisplay;
import org.lwjgl.opengl.LinuxPeerInfo;
import org.lwjgl.opengl.PixelFormat;
import org.lwjgl.opengles.GLContext;

final class LinuxDisplayPeerInfo
extends LinuxPeerInfo {
    final boolean egl;

    LinuxDisplayPeerInfo() {
        this.egl = true;
        GLContext.loadOpenGLLibrary();
    }

    LinuxDisplayPeerInfo(PixelFormat pixelFormat) {
        this.egl = false;
        LinuxDisplay.lockAWT();
        try {
            org.lwjgl.opengl.GLContext.loadOpenGLLibrary();
            try {
                LinuxDisplay.incDisplay();
                try {
                    LinuxDisplayPeerInfo.initDefaultPeerInfo(LinuxDisplay.getDisplay(), LinuxDisplay.getDefaultScreen(), this.getHandle(), pixelFormat);
                }
                catch (LWJGLException lWJGLException) {
                    LinuxDisplay.decDisplay();
                    throw lWJGLException;
                }
            }
            catch (LWJGLException lWJGLException) {
                org.lwjgl.opengl.GLContext.unloadOpenGLLibrary();
                throw lWJGLException;
            }
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native void initDefaultPeerInfo(long var0, int var2, ByteBuffer var3, PixelFormat var4);

    protected final void doLockAndInitHandle() {
        LinuxDisplay.lockAWT();
        try {
            LinuxDisplayPeerInfo.initDrawable(LinuxDisplay.getWindow(), this.getHandle());
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native void initDrawable(long var0, ByteBuffer var2);

    protected final void doUnlock() {
    }

    public final void destroy() {
        super.destroy();
        if (this.egl) {
            GLContext.unloadOpenGLLibrary();
            return;
        }
        LinuxDisplay.lockAWT();
        LinuxDisplay.decDisplay();
        org.lwjgl.opengl.GLContext.unloadOpenGLLibrary();
        LinuxDisplay.unlockAWT();
    }
}
