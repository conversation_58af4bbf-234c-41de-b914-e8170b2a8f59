/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.LongBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.opengl.GL44;

public final class ARBClearTexture {
    public static final int GL_CLEAR_TEXTURE = 37733;

    private ARBClearTexture() {
    }

    public static void glClearTexImage(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        GL44.glClearTexImage(n, n2, n3, n4, byteBuffer);
    }

    public static void glClearTexImage(int n, int n2, int n3, int n4, DoubleBuffer doubleBuffer) {
        GL44.glClearTexImage(n, n2, n3, n4, doubleBuffer);
    }

    public static void glClearTexImage(int n, int n2, int n3, int n4, FloatBuffer floatBuffer) {
        GL44.glClearTexImage(n, n2, n3, n4, floatBuffer);
    }

    public static void glClearTexImage(int n, int n2, int n3, int n4, IntBuffer intBuffer) {
        GL44.glClearTexImage(n, n2, n3, n4, intBuffer);
    }

    public static void glClearTexImage(int n, int n2, int n3, int n4, ShortBuffer shortBuffer) {
        GL44.glClearTexImage(n, n2, n3, n4, shortBuffer);
    }

    public static void glClearTexImage(int n, int n2, int n3, int n4, LongBuffer longBuffer) {
        GL44.glClearTexImage(n, n2, n3, n4, longBuffer);
    }

    public static void glClearTexSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, ByteBuffer byteBuffer) {
        GL44.glClearTexSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, byteBuffer);
    }

    public static void glClearTexSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, DoubleBuffer doubleBuffer) {
        GL44.glClearTexSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, doubleBuffer);
    }

    public static void glClearTexSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, FloatBuffer floatBuffer) {
        GL44.glClearTexSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, floatBuffer);
    }

    public static void glClearTexSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, IntBuffer intBuffer) {
        GL44.glClearTexSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, intBuffer);
    }

    public static void glClearTexSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, ShortBuffer shortBuffer) {
        GL44.glClearTexSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, shortBuffer);
    }

    public static void glClearTexSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, LongBuffer longBuffer) {
        GL44.glClearTexSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, longBuffer);
    }
}
