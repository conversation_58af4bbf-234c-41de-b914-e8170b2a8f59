/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class EXTDrawBuffers2 {
    private EXTDrawBuffers2() {
    }

    public static void glColorMaskIndexedEXT(int n, boolean bl, boolean bl2, boolean bl3, boolean bl4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorMaskIndexedEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTDrawBuffers2.nglColorMaskIndexedEXT(n, bl, bl2, bl3, bl4, l);
    }

    static native void nglColorMaskIndexedEXT(int var0, boolean var1, boolean var2, boolean var3, boolean var4, long var5);

    public static void glGetBooleanIndexedEXT(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetBooleanIndexedvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 4);
        EXTDrawBuffers2.nglGetBooleanIndexedvEXT(n, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetBooleanIndexedvEXT(int var0, int var1, long var2, long var4);

    public static boolean glGetBooleanIndexedEXT(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetBooleanIndexedvEXT;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferByte((ContextCapabilities)object, 1);
        EXTDrawBuffers2.nglGetBooleanIndexedvEXT(n, n2, MemoryUtil.getAddress((ByteBuffer)object), l);
        return ((ByteBuffer)object).get(0) == 1;
    }

    public static void glGetIntegerIndexedEXT(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetIntegerIndexedvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        EXTDrawBuffers2.nglGetIntegerIndexedvEXT(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetIntegerIndexedvEXT(int var0, int var1, long var2, long var4);

    public static int glGetIntegerIndexedEXT(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetIntegerIndexedvEXT;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        EXTDrawBuffers2.nglGetIntegerIndexedvEXT(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glEnableIndexedEXT(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEnableIndexedEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTDrawBuffers2.nglEnableIndexedEXT(n, n2, l);
    }

    static native void nglEnableIndexedEXT(int var0, int var1, long var2);

    public static void glDisableIndexedEXT(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDisableIndexedEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTDrawBuffers2.nglDisableIndexedEXT(n, n2, l);
    }

    static native void nglDisableIndexedEXT(int var0, int var1, long var2);

    public static boolean glIsEnabledIndexedEXT(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsEnabledIndexedEXT;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = EXTDrawBuffers2.nglIsEnabledIndexedEXT(n, n2, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsEnabledIndexedEXT(int var0, int var1, long var2);
}
