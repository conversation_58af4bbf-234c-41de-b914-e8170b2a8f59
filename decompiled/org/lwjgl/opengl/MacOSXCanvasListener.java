/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Canvas;
import java.awt.EventQueue;
import java.awt.event.ComponentEvent;
import java.awt.event.ComponentListener;
import java.awt.event.HierarchyEvent;
import java.awt.event.HierarchyListener;

final class MacOSXCanvasListener
implements ComponentListener,
HierarchyListener {
    private final Canvas canvas;
    private int width;
    private int height;
    private boolean context_update;
    private boolean resized;

    MacOSXCanvasListener(Canvas canvas) {
        this.canvas = canvas;
        canvas.addComponentListener(this);
        canvas.addHierarchyListener(this);
        this.setUpdate();
    }

    public final void disableListeners() {
        EventQueue.invokeLater(new Runnable(){

            public void run() {
                MacOSXCanvasListener.this.canvas.removeComponentListener(MacOSXCanvasListener.this);
                MacOSXCanvasListener.this.canvas.removeHierarchyListener(MacOSXCanvasListener.this);
            }
        });
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public final boolean syncShouldUpdateContext() {
        boolean bl;
        MacOSXCanvasListener macOSXCanvasListener = this;
        synchronized (macOSXCanvasListener) {
            bl = this.context_update;
            this.context_update = false;
        }
        return bl;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private synchronized void setUpdate() {
        MacOSXCanvasListener macOSXCanvasListener = this;
        synchronized (macOSXCanvasListener) {
            this.width = this.canvas.getWidth();
            this.height = this.canvas.getHeight();
            this.context_update = true;
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public final int syncGetWidth() {
        MacOSXCanvasListener macOSXCanvasListener = this;
        synchronized (macOSXCanvasListener) {
            return this.width;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public final int syncGetHeight() {
        MacOSXCanvasListener macOSXCanvasListener = this;
        synchronized (macOSXCanvasListener) {
            return this.height;
        }
    }

    public final void componentShown(ComponentEvent componentEvent) {
    }

    public final void componentHidden(ComponentEvent componentEvent) {
    }

    public final void componentResized(ComponentEvent componentEvent) {
        this.setUpdate();
        this.resized = true;
    }

    public final void componentMoved(ComponentEvent componentEvent) {
        this.setUpdate();
    }

    public final void hierarchyChanged(HierarchyEvent hierarchyEvent) {
        this.setUpdate();
    }

    public final boolean wasResized() {
        if (this.resized) {
            this.resized = false;
            return true;
        }
        return false;
    }
}
