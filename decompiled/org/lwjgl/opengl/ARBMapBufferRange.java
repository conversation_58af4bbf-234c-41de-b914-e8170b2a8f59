/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import org.lwjgl.opengl.GL30;

public final class ARBMapBufferRange {
    public static final int GL_MAP_READ_BIT = 1;
    public static final int GL_MAP_WRITE_BIT = 2;
    public static final int GL_MAP_INVALIDATE_RANGE_BIT = 4;
    public static final int GL_MAP_INVALIDATE_BUFFER_BIT = 8;
    public static final int GL_MAP_FLUSH_EXPLICIT_BIT = 16;
    public static final int GL_MAP_UNSYNCHRONIZED_BIT = 32;

    private ARBMapBufferRange() {
    }

    public static ByteBuffer glMapBufferRange(int n, long l, long l2, int n2, ByteBuffer byteBuffer) {
        return GL30.glMapBufferRange(n, l, l2, n2, byteBuffer);
    }

    public static void glFlushMappedBufferRange(int n, long l, long l2) {
        GL30.glFlushMappedBufferRange(n, l, l2);
    }
}
