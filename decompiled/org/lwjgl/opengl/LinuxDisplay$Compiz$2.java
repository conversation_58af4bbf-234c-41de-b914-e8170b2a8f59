/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.security.PrivilegedAction;
import org.lwjgl.LWJGLException;
import org.lwjgl.LWJGLUtil;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
static final class LinuxDisplay.Compiz.2
implements PrivilegedAction<Object> {
    final /* synthetic */ boolean val$enabled;

    LinuxDisplay.Compiz.2(boolean bl) {
        this.val$enabled = bl;
    }

    @Override
    public final Object run() {
        try {
            provider.setLegacyFullscreenSupport(this.val$enabled);
        }
        catch (LWJGLException lWJGLException) {
            LWJGLUtil.log("Failed to change Compiz Legacy Fullscreen Support. Reason: " + lWJGLException.getMessage());
        }
        return null;
    }
}
