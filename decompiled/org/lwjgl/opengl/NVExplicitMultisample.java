/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.EXTDrawBuffers2;
import org.lwjgl.opengl.GLContext;

public final class NVExplicitMultisample {
    public static final int GL_SAMPLE_POSITION_NV = 36432;
    public static final int GL_SAMPLE_MASK_NV = 36433;
    public static final int GL_SAMPLE_MASK_VALUE_NV = 36434;
    public static final int GL_TEXTURE_BINDING_RENDERBUFFER_NV = 36435;
    public static final int GL_TEXTURE_RENDERBUFFER_DATA_STORE_BINDING_NV = 36436;
    public static final int GL_MAX_SAMPLE_MASK_WORDS_NV = 36441;
    public static final int GL_TEXTURE_RENDERBUFFER_NV = 36437;
    public static final int GL_SAMPLER_RENDERBUFFER_NV = 36438;
    public static final int GL_INT_SAMPLER_RENDERBUFFER_NV = 36439;
    public static final int GL_UNSIGNED_INT_SAMPLER_RENDERBUFFER_NV = 36440;

    private NVExplicitMultisample() {
    }

    public static void glGetBooleanIndexedEXT(int n, int n2, ByteBuffer byteBuffer) {
        EXTDrawBuffers2.glGetBooleanIndexedEXT(n, n2, byteBuffer);
    }

    public static boolean glGetBooleanIndexedEXT(int n, int n2) {
        return EXTDrawBuffers2.glGetBooleanIndexedEXT(n, n2);
    }

    public static void glGetIntegerIndexedEXT(int n, int n2, IntBuffer intBuffer) {
        EXTDrawBuffers2.glGetIntegerIndexedEXT(n, n2, intBuffer);
    }

    public static int glGetIntegerIndexedEXT(int n, int n2) {
        return EXTDrawBuffers2.glGetIntegerIndexedEXT(n, n2);
    }

    public static void glGetMultisampleNV(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMultisamplefvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 2);
        NVExplicitMultisample.nglGetMultisamplefvNV(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetMultisamplefvNV(int var0, int var1, long var2, long var4);

    public static void glSampleMaskIndexedNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSampleMaskIndexedNV;
        BufferChecks.checkFunctionAddress(l);
        NVExplicitMultisample.nglSampleMaskIndexedNV(n, n2, l);
    }

    static native void nglSampleMaskIndexedNV(int var0, int var1, long var2);

    public static void glTexRenderbufferNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexRenderbufferNV;
        BufferChecks.checkFunctionAddress(l);
        NVExplicitMultisample.nglTexRenderbufferNV(n, n2, l);
    }

    static native void nglTexRenderbufferNV(int var0, int var1, long var2);
}
