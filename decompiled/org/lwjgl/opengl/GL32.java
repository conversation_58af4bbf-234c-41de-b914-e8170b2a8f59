/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.LongBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.GLSync;

public final class GL32 {
    public static final int GL_CONTEXT_PROFILE_MASK = 37158;
    public static final int GL_CONTEXT_CORE_PROFILE_BIT = 1;
    public static final int GL_CONTEXT_COMPATIBILITY_PROFILE_BIT = 2;
    public static final int GL_MAX_VERTEX_OUTPUT_COMPONENTS = 37154;
    public static final int GL_MAX_GEOMETRY_INPUT_COMPONENTS = 37155;
    public static final int GL_MAX_GEOMETRY_OUTPUT_COMPONENTS = 37156;
    public static final int GL_MAX_FRAGMENT_INPUT_COMPONENTS = 37157;
    public static final int GL_FIRST_VERTEX_CONVENTION = 36429;
    public static final int GL_LAST_VERTEX_CONVENTION = 36430;
    public static final int GL_PROVOKING_VERTEX = 36431;
    public static final int GL_QUADS_FOLLOW_PROVOKING_VERTEX_CONVENTION = 36428;
    public static final int GL_TEXTURE_CUBE_MAP_SEAMLESS = 34895;
    public static final int GL_SAMPLE_POSITION = 36432;
    public static final int GL_SAMPLE_MASK = 36433;
    public static final int GL_SAMPLE_MASK_VALUE = 36434;
    public static final int GL_TEXTURE_2D_MULTISAMPLE = 37120;
    public static final int GL_PROXY_TEXTURE_2D_MULTISAMPLE = 37121;
    public static final int GL_TEXTURE_2D_MULTISAMPLE_ARRAY = 37122;
    public static final int GL_PROXY_TEXTURE_2D_MULTISAMPLE_ARRAY = 37123;
    public static final int GL_MAX_SAMPLE_MASK_WORDS = 36441;
    public static final int GL_MAX_COLOR_TEXTURE_SAMPLES = 37134;
    public static final int GL_MAX_DEPTH_TEXTURE_SAMPLES = 37135;
    public static final int GL_MAX_INTEGER_SAMPLES = 37136;
    public static final int GL_TEXTURE_BINDING_2D_MULTISAMPLE = 37124;
    public static final int GL_TEXTURE_BINDING_2D_MULTISAMPLE_ARRAY = 37125;
    public static final int GL_TEXTURE_SAMPLES = 37126;
    public static final int GL_TEXTURE_FIXED_SAMPLE_LOCATIONS = 37127;
    public static final int GL_SAMPLER_2D_MULTISAMPLE = 37128;
    public static final int GL_INT_SAMPLER_2D_MULTISAMPLE = 37129;
    public static final int GL_UNSIGNED_INT_SAMPLER_2D_MULTISAMPLE = 37130;
    public static final int GL_SAMPLER_2D_MULTISAMPLE_ARRAY = 37131;
    public static final int GL_INT_SAMPLER_2D_MULTISAMPLE_ARRAY = 37132;
    public static final int GL_UNSIGNED_INT_SAMPLER_2D_MULTISAMPLE_ARRAY = 37133;
    public static final int GL_DEPTH_CLAMP = 34383;
    public static final int GL_GEOMETRY_SHADER = 36313;
    public static final int GL_GEOMETRY_VERTICES_OUT = 36314;
    public static final int GL_GEOMETRY_INPUT_TYPE = 36315;
    public static final int GL_GEOMETRY_OUTPUT_TYPE = 36316;
    public static final int GL_MAX_GEOMETRY_TEXTURE_IMAGE_UNITS = 35881;
    public static final int GL_MAX_GEOMETRY_UNIFORM_COMPONENTS = 36319;
    public static final int GL_MAX_GEOMETRY_OUTPUT_VERTICES = 36320;
    public static final int GL_MAX_GEOMETRY_TOTAL_OUTPUT_COMPONENTS = 36321;
    public static final int GL_LINES_ADJACENCY = 10;
    public static final int GL_LINE_STRIP_ADJACENCY = 11;
    public static final int GL_TRIANGLES_ADJACENCY = 12;
    public static final int GL_TRIANGLE_STRIP_ADJACENCY = 13;
    public static final int GL_FRAMEBUFFER_INCOMPLETE_LAYER_TARGETS = 36264;
    public static final int GL_FRAMEBUFFER_ATTACHMENT_LAYERED = 36263;
    public static final int GL_PROGRAM_POINT_SIZE = 34370;
    public static final int GL_MAX_SERVER_WAIT_TIMEOUT = 37137;
    public static final int GL_OBJECT_TYPE = 37138;
    public static final int GL_SYNC_CONDITION = 37139;
    public static final int GL_SYNC_STATUS = 37140;
    public static final int GL_SYNC_FLAGS = 37141;
    public static final int GL_SYNC_FENCE = 37142;
    public static final int GL_SYNC_GPU_COMMANDS_COMPLETE = 37143;
    public static final int GL_UNSIGNALED = 37144;
    public static final int GL_SIGNALED = 37145;
    public static final int GL_SYNC_FLUSH_COMMANDS_BIT = 1;
    public static final long GL_TIMEOUT_IGNORED = -1L;
    public static final int GL_ALREADY_SIGNALED = 37146;
    public static final int GL_TIMEOUT_EXPIRED = 37147;
    public static final int GL_CONDITION_SATISFIED = 37148;
    public static final int GL_WAIT_FAILED = 37149;

    private GL32() {
    }

    public static void glGetBufferParameter(int n, int n2, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetBufferParameteri64v;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 4);
        GL32.nglGetBufferParameteri64v(n, n2, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetBufferParameteri64v(int var0, int var1, long var2, long var4);

    public static long glGetBufferParameter(int n, int n2) {
        return GL32.glGetBufferParameteri64(n, n2);
    }

    public static long glGetBufferParameteri64(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetBufferParameteri64v;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferLong((ContextCapabilities)object);
        GL32.nglGetBufferParameteri64v(n, n2, MemoryUtil.getAddress((LongBuffer)object), l);
        return ((LongBuffer)object).get(0);
    }

    public static void glDrawElementsBaseVertex(int n, ByteBuffer byteBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsBaseVertex;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL32.nglDrawElementsBaseVertex(n, byteBuffer.remaining(), 5121, MemoryUtil.getAddress(byteBuffer), n2, l);
    }

    public static void glDrawElementsBaseVertex(int n, IntBuffer intBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsBaseVertex;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        GL32.nglDrawElementsBaseVertex(n, intBuffer.remaining(), 5125, MemoryUtil.getAddress(intBuffer), n2, l);
    }

    public static void glDrawElementsBaseVertex(int n, ShortBuffer shortBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsBaseVertex;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        GL32.nglDrawElementsBaseVertex(n, shortBuffer.remaining(), 5123, MemoryUtil.getAddress(shortBuffer), n2, l);
    }

    static native void nglDrawElementsBaseVertex(int var0, int var1, int var2, long var3, int var5, long var6);

    public static void glDrawElementsBaseVertex(int n, int n2, int n3, long l, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glDrawElementsBaseVertex;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureElementVBOenabled(contextCapabilities);
        GL32.nglDrawElementsBaseVertexBO(n, n2, n3, l, n4, l2);
    }

    static native void nglDrawElementsBaseVertexBO(int var0, int var1, int var2, long var3, int var5, long var6);

    public static void glDrawRangeElementsBaseVertex(int n, int n2, int n3, ByteBuffer byteBuffer, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawRangeElementsBaseVertex;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL32.nglDrawRangeElementsBaseVertex(n, n2, n3, byteBuffer.remaining(), 5121, MemoryUtil.getAddress(byteBuffer), n4, l);
    }

    public static void glDrawRangeElementsBaseVertex(int n, int n2, int n3, IntBuffer intBuffer, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawRangeElementsBaseVertex;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        GL32.nglDrawRangeElementsBaseVertex(n, n2, n3, intBuffer.remaining(), 5125, MemoryUtil.getAddress(intBuffer), n4, l);
    }

    public static void glDrawRangeElementsBaseVertex(int n, int n2, int n3, ShortBuffer shortBuffer, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawRangeElementsBaseVertex;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        GL32.nglDrawRangeElementsBaseVertex(n, n2, n3, shortBuffer.remaining(), 5123, MemoryUtil.getAddress(shortBuffer), n4, l);
    }

    static native void nglDrawRangeElementsBaseVertex(int var0, int var1, int var2, int var3, int var4, long var5, int var7, long var8);

    public static void glDrawRangeElementsBaseVertex(int n, int n2, int n3, int n4, int n5, long l, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glDrawRangeElementsBaseVertex;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureElementVBOenabled(contextCapabilities);
        GL32.nglDrawRangeElementsBaseVertexBO(n, n2, n3, n4, n5, l, n6, l2);
    }

    static native void nglDrawRangeElementsBaseVertexBO(int var0, int var1, int var2, int var3, int var4, long var5, int var7, long var8);

    public static void glDrawElementsInstancedBaseVertex(int n, ByteBuffer byteBuffer, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsInstancedBaseVertex;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL32.nglDrawElementsInstancedBaseVertex(n, byteBuffer.remaining(), 5121, MemoryUtil.getAddress(byteBuffer), n2, n3, l);
    }

    public static void glDrawElementsInstancedBaseVertex(int n, IntBuffer intBuffer, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsInstancedBaseVertex;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        GL32.nglDrawElementsInstancedBaseVertex(n, intBuffer.remaining(), 5125, MemoryUtil.getAddress(intBuffer), n2, n3, l);
    }

    public static void glDrawElementsInstancedBaseVertex(int n, ShortBuffer shortBuffer, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsInstancedBaseVertex;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        GL32.nglDrawElementsInstancedBaseVertex(n, shortBuffer.remaining(), 5123, MemoryUtil.getAddress(shortBuffer), n2, n3, l);
    }

    static native void nglDrawElementsInstancedBaseVertex(int var0, int var1, int var2, long var3, int var5, int var6, long var7);

    public static void glDrawElementsInstancedBaseVertex(int n, int n2, int n3, long l, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glDrawElementsInstancedBaseVertex;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureElementVBOenabled(contextCapabilities);
        GL32.nglDrawElementsInstancedBaseVertexBO(n, n2, n3, l, n4, n5, l2);
    }

    static native void nglDrawElementsInstancedBaseVertexBO(int var0, int var1, int var2, long var3, int var5, int var6, long var7);

    public static void glProvokingVertex(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProvokingVertex;
        BufferChecks.checkFunctionAddress(l);
        GL32.nglProvokingVertex(n, l);
    }

    static native void nglProvokingVertex(int var0, long var1);

    public static void glTexImage2DMultisample(int n, int n2, int n3, int n4, int n5, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexImage2DMultisample;
        BufferChecks.checkFunctionAddress(l);
        GL32.nglTexImage2DMultisample(n, n2, n3, n4, n5, bl, l);
    }

    static native void nglTexImage2DMultisample(int var0, int var1, int var2, int var3, int var4, boolean var5, long var6);

    public static void glTexImage3DMultisample(int n, int n2, int n3, int n4, int n5, int n6, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexImage3DMultisample;
        BufferChecks.checkFunctionAddress(l);
        GL32.nglTexImage3DMultisample(n, n2, n3, n4, n5, n6, bl, l);
    }

    static native void nglTexImage3DMultisample(int var0, int var1, int var2, int var3, int var4, int var5, boolean var6, long var7);

    public static void glGetMultisample(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMultisamplefv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 2);
        GL32.nglGetMultisamplefv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetMultisamplefv(int var0, int var1, long var2, long var4);

    public static void glSampleMaski(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSampleMaski;
        BufferChecks.checkFunctionAddress(l);
        GL32.nglSampleMaski(n, n2, l);
    }

    static native void nglSampleMaski(int var0, int var1, long var2);

    public static void glFramebufferTexture(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFramebufferTexture;
        BufferChecks.checkFunctionAddress(l);
        GL32.nglFramebufferTexture(n, n2, n3, n4, l);
    }

    static native void nglFramebufferTexture(int var0, int var1, int var2, int var3, long var4);

    public static GLSync glFenceSync(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFenceSync;
        BufferChecks.checkFunctionAddress(l);
        GLSync gLSync = new GLSync(GL32.nglFenceSync(n, n2, l));
        return gLSync;
    }

    static native long nglFenceSync(int var0, int var1, long var2);

    public static boolean glIsSync(GLSync gLSync) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsSync;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = GL32.nglIsSync(gLSync.getPointer(), l);
        return bl;
    }

    static native boolean nglIsSync(long var0, long var2);

    public static void glDeleteSync(GLSync gLSync) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteSync;
        BufferChecks.checkFunctionAddress(l);
        GL32.nglDeleteSync(gLSync.getPointer(), l);
    }

    static native void nglDeleteSync(long var0, long var2);

    public static int glClientWaitSync(GLSync gLSync, int n, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glClientWaitSync;
        BufferChecks.checkFunctionAddress(l2);
        int n2 = GL32.nglClientWaitSync(gLSync.getPointer(), n, l, l2);
        return n2;
    }

    static native int nglClientWaitSync(long var0, int var2, long var3, long var5);

    public static void glWaitSync(GLSync gLSync, int n, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glWaitSync;
        BufferChecks.checkFunctionAddress(l2);
        GL32.nglWaitSync(gLSync.getPointer(), n, l, l2);
    }

    static native void nglWaitSync(long var0, int var2, long var3, long var5);

    public static void glGetInteger64(int n, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetInteger64v;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 1);
        GL32.nglGetInteger64v(n, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetInteger64v(int var0, long var1, long var3);

    public static long glGetInteger64(int n) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetInteger64v;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferLong((ContextCapabilities)object);
        GL32.nglGetInteger64v(n, MemoryUtil.getAddress((LongBuffer)object), l);
        return ((LongBuffer)object).get(0);
    }

    public static void glGetInteger64(int n, int n2, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetInteger64i_v;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 4);
        GL32.nglGetInteger64i_v(n, n2, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetInteger64i_v(int var0, int var1, long var2, long var4);

    public static long glGetInteger64(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetInteger64i_v;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferLong((ContextCapabilities)object);
        GL32.nglGetInteger64i_v(n, n2, MemoryUtil.getAddress((LongBuffer)object), l);
        return ((LongBuffer)object).get(0);
    }

    public static void glGetSync(GLSync gLSync, int n, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetSynciv;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(intBuffer2);
        GL32.nglGetSynciv(gLSync.getPointer(), n, intBuffer2.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    static native void nglGetSynciv(long var0, int var2, int var3, long var4, long var6, long var8);

    public static int glGetSync(GLSync gLSync, int n) {
        return GL32.glGetSynci(gLSync, n);
    }

    public static int glGetSynci(GLSync gLSync, int n) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetSynciv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL32.nglGetSynciv(gLSync.getPointer(), n, 1, 0L, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }
}
