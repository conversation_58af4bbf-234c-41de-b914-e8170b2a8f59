/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.StateTracker;

public final class GL11 {
    public static final int GL_ACCUM = 256;
    public static final int GL_LOAD = 257;
    public static final int GL_RETURN = 258;
    public static final int GL_MULT = 259;
    public static final int GL_ADD = 260;
    public static final int GL_NEVER = 512;
    public static final int GL_LESS = 513;
    public static final int GL_EQUAL = 514;
    public static final int GL_LEQUAL = 515;
    public static final int GL_GREATER = 516;
    public static final int GL_NOTEQUAL = 517;
    public static final int GL_GEQUAL = 518;
    public static final int GL_ALWAYS = 519;
    public static final int GL_CURRENT_BIT = 1;
    public static final int GL_POINT_BIT = 2;
    public static final int GL_LINE_BIT = 4;
    public static final int GL_POLYGON_BIT = 8;
    public static final int GL_POLYGON_STIPPLE_BIT = 16;
    public static final int GL_PIXEL_MODE_BIT = 32;
    public static final int GL_LIGHTING_BIT = 64;
    public static final int GL_FOG_BIT = 128;
    public static final int GL_DEPTH_BUFFER_BIT = 256;
    public static final int GL_ACCUM_BUFFER_BIT = 512;
    public static final int GL_STENCIL_BUFFER_BIT = 1024;
    public static final int GL_VIEWPORT_BIT = 2048;
    public static final int GL_TRANSFORM_BIT = 4096;
    public static final int GL_ENABLE_BIT = 8192;
    public static final int GL_COLOR_BUFFER_BIT = 16384;
    public static final int GL_HINT_BIT = 32768;
    public static final int GL_EVAL_BIT = 65536;
    public static final int GL_LIST_BIT = 131072;
    public static final int GL_TEXTURE_BIT = 262144;
    public static final int GL_SCISSOR_BIT = 524288;
    public static final int GL_ALL_ATTRIB_BITS = 1048575;
    public static final int GL_POINTS = 0;
    public static final int GL_LINES = 1;
    public static final int GL_LINE_LOOP = 2;
    public static final int GL_LINE_STRIP = 3;
    public static final int GL_TRIANGLES = 4;
    public static final int GL_TRIANGLE_STRIP = 5;
    public static final int GL_TRIANGLE_FAN = 6;
    public static final int GL_QUADS = 7;
    public static final int GL_QUAD_STRIP = 8;
    public static final int GL_POLYGON = 9;
    public static final int GL_ZERO = 0;
    public static final int GL_ONE = 1;
    public static final int GL_SRC_COLOR = 768;
    public static final int GL_ONE_MINUS_SRC_COLOR = 769;
    public static final int GL_SRC_ALPHA = 770;
    public static final int GL_ONE_MINUS_SRC_ALPHA = 771;
    public static final int GL_DST_ALPHA = 772;
    public static final int GL_ONE_MINUS_DST_ALPHA = 773;
    public static final int GL_DST_COLOR = 774;
    public static final int GL_ONE_MINUS_DST_COLOR = 775;
    public static final int GL_SRC_ALPHA_SATURATE = 776;
    public static final int GL_CONSTANT_COLOR = 32769;
    public static final int GL_ONE_MINUS_CONSTANT_COLOR = 32770;
    public static final int GL_CONSTANT_ALPHA = 32771;
    public static final int GL_ONE_MINUS_CONSTANT_ALPHA = 32772;
    public static final int GL_TRUE = 1;
    public static final int GL_FALSE = 0;
    public static final int GL_CLIP_PLANE0 = 12288;
    public static final int GL_CLIP_PLANE1 = 12289;
    public static final int GL_CLIP_PLANE2 = 12290;
    public static final int GL_CLIP_PLANE3 = 12291;
    public static final int GL_CLIP_PLANE4 = 12292;
    public static final int GL_CLIP_PLANE5 = 12293;
    public static final int GL_BYTE = 5120;
    public static final int GL_UNSIGNED_BYTE = 5121;
    public static final int GL_SHORT = 5122;
    public static final int GL_UNSIGNED_SHORT = 5123;
    public static final int GL_INT = 5124;
    public static final int GL_UNSIGNED_INT = 5125;
    public static final int GL_FLOAT = 5126;
    public static final int GL_2_BYTES = 5127;
    public static final int GL_3_BYTES = 5128;
    public static final int GL_4_BYTES = 5129;
    public static final int GL_DOUBLE = 5130;
    public static final int GL_NONE = 0;
    public static final int GL_FRONT_LEFT = 1024;
    public static final int GL_FRONT_RIGHT = 1025;
    public static final int GL_BACK_LEFT = 1026;
    public static final int GL_BACK_RIGHT = 1027;
    public static final int GL_FRONT = 1028;
    public static final int GL_BACK = 1029;
    public static final int GL_LEFT = 1030;
    public static final int GL_RIGHT = 1031;
    public static final int GL_FRONT_AND_BACK = 1032;
    public static final int GL_AUX0 = 1033;
    public static final int GL_AUX1 = 1034;
    public static final int GL_AUX2 = 1035;
    public static final int GL_AUX3 = 1036;
    public static final int GL_NO_ERROR = 0;
    public static final int GL_INVALID_ENUM = 1280;
    public static final int GL_INVALID_VALUE = 1281;
    public static final int GL_INVALID_OPERATION = 1282;
    public static final int GL_STACK_OVERFLOW = 1283;
    public static final int GL_STACK_UNDERFLOW = 1284;
    public static final int GL_OUT_OF_MEMORY = 1285;
    public static final int GL_2D = 1536;
    public static final int GL_3D = 1537;
    public static final int GL_3D_COLOR = 1538;
    public static final int GL_3D_COLOR_TEXTURE = 1539;
    public static final int GL_4D_COLOR_TEXTURE = 1540;
    public static final int GL_PASS_THROUGH_TOKEN = 1792;
    public static final int GL_POINT_TOKEN = 1793;
    public static final int GL_LINE_TOKEN = 1794;
    public static final int GL_POLYGON_TOKEN = 1795;
    public static final int GL_BITMAP_TOKEN = 1796;
    public static final int GL_DRAW_PIXEL_TOKEN = 1797;
    public static final int GL_COPY_PIXEL_TOKEN = 1798;
    public static final int GL_LINE_RESET_TOKEN = 1799;
    public static final int GL_EXP = 2048;
    public static final int GL_EXP2 = 2049;
    public static final int GL_CW = 2304;
    public static final int GL_CCW = 2305;
    public static final int GL_COEFF = 2560;
    public static final int GL_ORDER = 2561;
    public static final int GL_DOMAIN = 2562;
    public static final int GL_CURRENT_COLOR = 2816;
    public static final int GL_CURRENT_INDEX = 2817;
    public static final int GL_CURRENT_NORMAL = 2818;
    public static final int GL_CURRENT_TEXTURE_COORDS = 2819;
    public static final int GL_CURRENT_RASTER_COLOR = 2820;
    public static final int GL_CURRENT_RASTER_INDEX = 2821;
    public static final int GL_CURRENT_RASTER_TEXTURE_COORDS = 2822;
    public static final int GL_CURRENT_RASTER_POSITION = 2823;
    public static final int GL_CURRENT_RASTER_POSITION_VALID = 2824;
    public static final int GL_CURRENT_RASTER_DISTANCE = 2825;
    public static final int GL_POINT_SMOOTH = 2832;
    public static final int GL_POINT_SIZE = 2833;
    public static final int GL_POINT_SIZE_RANGE = 2834;
    public static final int GL_POINT_SIZE_GRANULARITY = 2835;
    public static final int GL_LINE_SMOOTH = 2848;
    public static final int GL_LINE_WIDTH = 2849;
    public static final int GL_LINE_WIDTH_RANGE = 2850;
    public static final int GL_LINE_WIDTH_GRANULARITY = 2851;
    public static final int GL_LINE_STIPPLE = 2852;
    public static final int GL_LINE_STIPPLE_PATTERN = 2853;
    public static final int GL_LINE_STIPPLE_REPEAT = 2854;
    public static final int GL_LIST_MODE = 2864;
    public static final int GL_MAX_LIST_NESTING = 2865;
    public static final int GL_LIST_BASE = 2866;
    public static final int GL_LIST_INDEX = 2867;
    public static final int GL_POLYGON_MODE = 2880;
    public static final int GL_POLYGON_SMOOTH = 2881;
    public static final int GL_POLYGON_STIPPLE = 2882;
    public static final int GL_EDGE_FLAG = 2883;
    public static final int GL_CULL_FACE = 2884;
    public static final int GL_CULL_FACE_MODE = 2885;
    public static final int GL_FRONT_FACE = 2886;
    public static final int GL_LIGHTING = 2896;
    public static final int GL_LIGHT_MODEL_LOCAL_VIEWER = 2897;
    public static final int GL_LIGHT_MODEL_TWO_SIDE = 2898;
    public static final int GL_LIGHT_MODEL_AMBIENT = 2899;
    public static final int GL_SHADE_MODEL = 2900;
    public static final int GL_COLOR_MATERIAL_FACE = 2901;
    public static final int GL_COLOR_MATERIAL_PARAMETER = 2902;
    public static final int GL_COLOR_MATERIAL = 2903;
    public static final int GL_FOG = 2912;
    public static final int GL_FOG_INDEX = 2913;
    public static final int GL_FOG_DENSITY = 2914;
    public static final int GL_FOG_START = 2915;
    public static final int GL_FOG_END = 2916;
    public static final int GL_FOG_MODE = 2917;
    public static final int GL_FOG_COLOR = 2918;
    public static final int GL_DEPTH_RANGE = 2928;
    public static final int GL_DEPTH_TEST = 2929;
    public static final int GL_DEPTH_WRITEMASK = 2930;
    public static final int GL_DEPTH_CLEAR_VALUE = 2931;
    public static final int GL_DEPTH_FUNC = 2932;
    public static final int GL_ACCUM_CLEAR_VALUE = 2944;
    public static final int GL_STENCIL_TEST = 2960;
    public static final int GL_STENCIL_CLEAR_VALUE = 2961;
    public static final int GL_STENCIL_FUNC = 2962;
    public static final int GL_STENCIL_VALUE_MASK = 2963;
    public static final int GL_STENCIL_FAIL = 2964;
    public static final int GL_STENCIL_PASS_DEPTH_FAIL = 2965;
    public static final int GL_STENCIL_PASS_DEPTH_PASS = 2966;
    public static final int GL_STENCIL_REF = 2967;
    public static final int GL_STENCIL_WRITEMASK = 2968;
    public static final int GL_MATRIX_MODE = 2976;
    public static final int GL_NORMALIZE = 2977;
    public static final int GL_VIEWPORT = 2978;
    public static final int GL_MODELVIEW_STACK_DEPTH = 2979;
    public static final int GL_PROJECTION_STACK_DEPTH = 2980;
    public static final int GL_TEXTURE_STACK_DEPTH = 2981;
    public static final int GL_MODELVIEW_MATRIX = 2982;
    public static final int GL_PROJECTION_MATRIX = 2983;
    public static final int GL_TEXTURE_MATRIX = 2984;
    public static final int GL_ATTRIB_STACK_DEPTH = 2992;
    public static final int GL_CLIENT_ATTRIB_STACK_DEPTH = 2993;
    public static final int GL_ALPHA_TEST = 3008;
    public static final int GL_ALPHA_TEST_FUNC = 3009;
    public static final int GL_ALPHA_TEST_REF = 3010;
    public static final int GL_DITHER = 3024;
    public static final int GL_BLEND_DST = 3040;
    public static final int GL_BLEND_SRC = 3041;
    public static final int GL_BLEND = 3042;
    public static final int GL_LOGIC_OP_MODE = 3056;
    public static final int GL_INDEX_LOGIC_OP = 3057;
    public static final int GL_COLOR_LOGIC_OP = 3058;
    public static final int GL_AUX_BUFFERS = 3072;
    public static final int GL_DRAW_BUFFER = 3073;
    public static final int GL_READ_BUFFER = 3074;
    public static final int GL_SCISSOR_BOX = 3088;
    public static final int GL_SCISSOR_TEST = 3089;
    public static final int GL_INDEX_CLEAR_VALUE = 3104;
    public static final int GL_INDEX_WRITEMASK = 3105;
    public static final int GL_COLOR_CLEAR_VALUE = 3106;
    public static final int GL_COLOR_WRITEMASK = 3107;
    public static final int GL_INDEX_MODE = 3120;
    public static final int GL_RGBA_MODE = 3121;
    public static final int GL_DOUBLEBUFFER = 3122;
    public static final int GL_STEREO = 3123;
    public static final int GL_RENDER_MODE = 3136;
    public static final int GL_PERSPECTIVE_CORRECTION_HINT = 3152;
    public static final int GL_POINT_SMOOTH_HINT = 3153;
    public static final int GL_LINE_SMOOTH_HINT = 3154;
    public static final int GL_POLYGON_SMOOTH_HINT = 3155;
    public static final int GL_FOG_HINT = 3156;
    public static final int GL_TEXTURE_GEN_S = 3168;
    public static final int GL_TEXTURE_GEN_T = 3169;
    public static final int GL_TEXTURE_GEN_R = 3170;
    public static final int GL_TEXTURE_GEN_Q = 3171;
    public static final int GL_PIXEL_MAP_I_TO_I = 3184;
    public static final int GL_PIXEL_MAP_S_TO_S = 3185;
    public static final int GL_PIXEL_MAP_I_TO_R = 3186;
    public static final int GL_PIXEL_MAP_I_TO_G = 3187;
    public static final int GL_PIXEL_MAP_I_TO_B = 3188;
    public static final int GL_PIXEL_MAP_I_TO_A = 3189;
    public static final int GL_PIXEL_MAP_R_TO_R = 3190;
    public static final int GL_PIXEL_MAP_G_TO_G = 3191;
    public static final int GL_PIXEL_MAP_B_TO_B = 3192;
    public static final int GL_PIXEL_MAP_A_TO_A = 3193;
    public static final int GL_PIXEL_MAP_I_TO_I_SIZE = 3248;
    public static final int GL_PIXEL_MAP_S_TO_S_SIZE = 3249;
    public static final int GL_PIXEL_MAP_I_TO_R_SIZE = 3250;
    public static final int GL_PIXEL_MAP_I_TO_G_SIZE = 3251;
    public static final int GL_PIXEL_MAP_I_TO_B_SIZE = 3252;
    public static final int GL_PIXEL_MAP_I_TO_A_SIZE = 3253;
    public static final int GL_PIXEL_MAP_R_TO_R_SIZE = 3254;
    public static final int GL_PIXEL_MAP_G_TO_G_SIZE = 3255;
    public static final int GL_PIXEL_MAP_B_TO_B_SIZE = 3256;
    public static final int GL_PIXEL_MAP_A_TO_A_SIZE = 3257;
    public static final int GL_UNPACK_SWAP_BYTES = 3312;
    public static final int GL_UNPACK_LSB_FIRST = 3313;
    public static final int GL_UNPACK_ROW_LENGTH = 3314;
    public static final int GL_UNPACK_SKIP_ROWS = 3315;
    public static final int GL_UNPACK_SKIP_PIXELS = 3316;
    public static final int GL_UNPACK_ALIGNMENT = 3317;
    public static final int GL_PACK_SWAP_BYTES = 3328;
    public static final int GL_PACK_LSB_FIRST = 3329;
    public static final int GL_PACK_ROW_LENGTH = 3330;
    public static final int GL_PACK_SKIP_ROWS = 3331;
    public static final int GL_PACK_SKIP_PIXELS = 3332;
    public static final int GL_PACK_ALIGNMENT = 3333;
    public static final int GL_MAP_COLOR = 3344;
    public static final int GL_MAP_STENCIL = 3345;
    public static final int GL_INDEX_SHIFT = 3346;
    public static final int GL_INDEX_OFFSET = 3347;
    public static final int GL_RED_SCALE = 3348;
    public static final int GL_RED_BIAS = 3349;
    public static final int GL_ZOOM_X = 3350;
    public static final int GL_ZOOM_Y = 3351;
    public static final int GL_GREEN_SCALE = 3352;
    public static final int GL_GREEN_BIAS = 3353;
    public static final int GL_BLUE_SCALE = 3354;
    public static final int GL_BLUE_BIAS = 3355;
    public static final int GL_ALPHA_SCALE = 3356;
    public static final int GL_ALPHA_BIAS = 3357;
    public static final int GL_DEPTH_SCALE = 3358;
    public static final int GL_DEPTH_BIAS = 3359;
    public static final int GL_MAX_EVAL_ORDER = 3376;
    public static final int GL_MAX_LIGHTS = 3377;
    public static final int GL_MAX_CLIP_PLANES = 3378;
    public static final int GL_MAX_TEXTURE_SIZE = 3379;
    public static final int GL_MAX_PIXEL_MAP_TABLE = 3380;
    public static final int GL_MAX_ATTRIB_STACK_DEPTH = 3381;
    public static final int GL_MAX_MODELVIEW_STACK_DEPTH = 3382;
    public static final int GL_MAX_NAME_STACK_DEPTH = 3383;
    public static final int GL_MAX_PROJECTION_STACK_DEPTH = 3384;
    public static final int GL_MAX_TEXTURE_STACK_DEPTH = 3385;
    public static final int GL_MAX_VIEWPORT_DIMS = 3386;
    public static final int GL_MAX_CLIENT_ATTRIB_STACK_DEPTH = 3387;
    public static final int GL_SUBPIXEL_BITS = 3408;
    public static final int GL_INDEX_BITS = 3409;
    public static final int GL_RED_BITS = 3410;
    public static final int GL_GREEN_BITS = 3411;
    public static final int GL_BLUE_BITS = 3412;
    public static final int GL_ALPHA_BITS = 3413;
    public static final int GL_DEPTH_BITS = 3414;
    public static final int GL_STENCIL_BITS = 3415;
    public static final int GL_ACCUM_RED_BITS = 3416;
    public static final int GL_ACCUM_GREEN_BITS = 3417;
    public static final int GL_ACCUM_BLUE_BITS = 3418;
    public static final int GL_ACCUM_ALPHA_BITS = 3419;
    public static final int GL_NAME_STACK_DEPTH = 3440;
    public static final int GL_AUTO_NORMAL = 3456;
    public static final int GL_MAP1_COLOR_4 = 3472;
    public static final int GL_MAP1_INDEX = 3473;
    public static final int GL_MAP1_NORMAL = 3474;
    public static final int GL_MAP1_TEXTURE_COORD_1 = 3475;
    public static final int GL_MAP1_TEXTURE_COORD_2 = 3476;
    public static final int GL_MAP1_TEXTURE_COORD_3 = 3477;
    public static final int GL_MAP1_TEXTURE_COORD_4 = 3478;
    public static final int GL_MAP1_VERTEX_3 = 3479;
    public static final int GL_MAP1_VERTEX_4 = 3480;
    public static final int GL_MAP2_COLOR_4 = 3504;
    public static final int GL_MAP2_INDEX = 3505;
    public static final int GL_MAP2_NORMAL = 3506;
    public static final int GL_MAP2_TEXTURE_COORD_1 = 3507;
    public static final int GL_MAP2_TEXTURE_COORD_2 = 3508;
    public static final int GL_MAP2_TEXTURE_COORD_3 = 3509;
    public static final int GL_MAP2_TEXTURE_COORD_4 = 3510;
    public static final int GL_MAP2_VERTEX_3 = 3511;
    public static final int GL_MAP2_VERTEX_4 = 3512;
    public static final int GL_MAP1_GRID_DOMAIN = 3536;
    public static final int GL_MAP1_GRID_SEGMENTS = 3537;
    public static final int GL_MAP2_GRID_DOMAIN = 3538;
    public static final int GL_MAP2_GRID_SEGMENTS = 3539;
    public static final int GL_TEXTURE_1D = 3552;
    public static final int GL_TEXTURE_2D = 3553;
    public static final int GL_FEEDBACK_BUFFER_POINTER = 3568;
    public static final int GL_FEEDBACK_BUFFER_SIZE = 3569;
    public static final int GL_FEEDBACK_BUFFER_TYPE = 3570;
    public static final int GL_SELECTION_BUFFER_POINTER = 3571;
    public static final int GL_SELECTION_BUFFER_SIZE = 3572;
    public static final int GL_TEXTURE_WIDTH = 4096;
    public static final int GL_TEXTURE_HEIGHT = 4097;
    public static final int GL_TEXTURE_INTERNAL_FORMAT = 4099;
    public static final int GL_TEXTURE_BORDER_COLOR = 4100;
    public static final int GL_TEXTURE_BORDER = 4101;
    public static final int GL_DONT_CARE = 4352;
    public static final int GL_FASTEST = 4353;
    public static final int GL_NICEST = 4354;
    public static final int GL_LIGHT0 = 16384;
    public static final int GL_LIGHT1 = 16385;
    public static final int GL_LIGHT2 = 16386;
    public static final int GL_LIGHT3 = 16387;
    public static final int GL_LIGHT4 = 16388;
    public static final int GL_LIGHT5 = 16389;
    public static final int GL_LIGHT6 = 16390;
    public static final int GL_LIGHT7 = 16391;
    public static final int GL_AMBIENT = 4608;
    public static final int GL_DIFFUSE = 4609;
    public static final int GL_SPECULAR = 4610;
    public static final int GL_POSITION = 4611;
    public static final int GL_SPOT_DIRECTION = 4612;
    public static final int GL_SPOT_EXPONENT = 4613;
    public static final int GL_SPOT_CUTOFF = 4614;
    public static final int GL_CONSTANT_ATTENUATION = 4615;
    public static final int GL_LINEAR_ATTENUATION = 4616;
    public static final int GL_QUADRATIC_ATTENUATION = 4617;
    public static final int GL_COMPILE = 4864;
    public static final int GL_COMPILE_AND_EXECUTE = 4865;
    public static final int GL_CLEAR = 5376;
    public static final int GL_AND = 5377;
    public static final int GL_AND_REVERSE = 5378;
    public static final int GL_COPY = 5379;
    public static final int GL_AND_INVERTED = 5380;
    public static final int GL_NOOP = 5381;
    public static final int GL_XOR = 5382;
    public static final int GL_OR = 5383;
    public static final int GL_NOR = 5384;
    public static final int GL_EQUIV = 5385;
    public static final int GL_INVERT = 5386;
    public static final int GL_OR_REVERSE = 5387;
    public static final int GL_COPY_INVERTED = 5388;
    public static final int GL_OR_INVERTED = 5389;
    public static final int GL_NAND = 5390;
    public static final int GL_SET = 5391;
    public static final int GL_EMISSION = 5632;
    public static final int GL_SHININESS = 5633;
    public static final int GL_AMBIENT_AND_DIFFUSE = 5634;
    public static final int GL_COLOR_INDEXES = 5635;
    public static final int GL_MODELVIEW = 5888;
    public static final int GL_PROJECTION = 5889;
    public static final int GL_TEXTURE = 5890;
    public static final int GL_COLOR = 6144;
    public static final int GL_DEPTH = 6145;
    public static final int GL_STENCIL = 6146;
    public static final int GL_COLOR_INDEX = 6400;
    public static final int GL_STENCIL_INDEX = 6401;
    public static final int GL_DEPTH_COMPONENT = 6402;
    public static final int GL_RED = 6403;
    public static final int GL_GREEN = 6404;
    public static final int GL_BLUE = 6405;
    public static final int GL_ALPHA = 6406;
    public static final int GL_RGB = 6407;
    public static final int GL_RGBA = 6408;
    public static final int GL_LUMINANCE = 6409;
    public static final int GL_LUMINANCE_ALPHA = 6410;
    public static final int GL_BITMAP = 6656;
    public static final int GL_POINT = 6912;
    public static final int GL_LINE = 6913;
    public static final int GL_FILL = 6914;
    public static final int GL_RENDER = 7168;
    public static final int GL_FEEDBACK = 7169;
    public static final int GL_SELECT = 7170;
    public static final int GL_FLAT = 7424;
    public static final int GL_SMOOTH = 7425;
    public static final int GL_KEEP = 7680;
    public static final int GL_REPLACE = 7681;
    public static final int GL_INCR = 7682;
    public static final int GL_DECR = 7683;
    public static final int GL_VENDOR = 7936;
    public static final int GL_RENDERER = 7937;
    public static final int GL_VERSION = 7938;
    public static final int GL_EXTENSIONS = 7939;
    public static final int GL_S = 8192;
    public static final int GL_T = 8193;
    public static final int GL_R = 8194;
    public static final int GL_Q = 8195;
    public static final int GL_MODULATE = 8448;
    public static final int GL_DECAL = 8449;
    public static final int GL_TEXTURE_ENV_MODE = 8704;
    public static final int GL_TEXTURE_ENV_COLOR = 8705;
    public static final int GL_TEXTURE_ENV = 8960;
    public static final int GL_EYE_LINEAR = 9216;
    public static final int GL_OBJECT_LINEAR = 9217;
    public static final int GL_SPHERE_MAP = 9218;
    public static final int GL_TEXTURE_GEN_MODE = 9472;
    public static final int GL_OBJECT_PLANE = 9473;
    public static final int GL_EYE_PLANE = 9474;
    public static final int GL_NEAREST = 9728;
    public static final int GL_LINEAR = 9729;
    public static final int GL_NEAREST_MIPMAP_NEAREST = 9984;
    public static final int GL_LINEAR_MIPMAP_NEAREST = 9985;
    public static final int GL_NEAREST_MIPMAP_LINEAR = 9986;
    public static final int GL_LINEAR_MIPMAP_LINEAR = 9987;
    public static final int GL_TEXTURE_MAG_FILTER = 10240;
    public static final int GL_TEXTURE_MIN_FILTER = 10241;
    public static final int GL_TEXTURE_WRAP_S = 10242;
    public static final int GL_TEXTURE_WRAP_T = 10243;
    public static final int GL_CLAMP = 10496;
    public static final int GL_REPEAT = 10497;
    public static final int GL_CLIENT_PIXEL_STORE_BIT = 1;
    public static final int GL_CLIENT_VERTEX_ARRAY_BIT = 2;
    public static final int GL_ALL_CLIENT_ATTRIB_BITS = -1;
    public static final int GL_POLYGON_OFFSET_FACTOR = 32824;
    public static final int GL_POLYGON_OFFSET_UNITS = 10752;
    public static final int GL_POLYGON_OFFSET_POINT = 10753;
    public static final int GL_POLYGON_OFFSET_LINE = 10754;
    public static final int GL_POLYGON_OFFSET_FILL = 32823;
    public static final int GL_ALPHA4 = 32827;
    public static final int GL_ALPHA8 = 32828;
    public static final int GL_ALPHA12 = 32829;
    public static final int GL_ALPHA16 = 32830;
    public static final int GL_LUMINANCE4 = 32831;
    public static final int GL_LUMINANCE8 = 32832;
    public static final int GL_LUMINANCE12 = 32833;
    public static final int GL_LUMINANCE16 = 32834;
    public static final int GL_LUMINANCE4_ALPHA4 = 32835;
    public static final int GL_LUMINANCE6_ALPHA2 = 32836;
    public static final int GL_LUMINANCE8_ALPHA8 = 32837;
    public static final int GL_LUMINANCE12_ALPHA4 = 32838;
    public static final int GL_LUMINANCE12_ALPHA12 = 32839;
    public static final int GL_LUMINANCE16_ALPHA16 = 32840;
    public static final int GL_INTENSITY = 32841;
    public static final int GL_INTENSITY4 = 32842;
    public static final int GL_INTENSITY8 = 32843;
    public static final int GL_INTENSITY12 = 32844;
    public static final int GL_INTENSITY16 = 32845;
    public static final int GL_R3_G3_B2 = 10768;
    public static final int GL_RGB4 = 32847;
    public static final int GL_RGB5 = 32848;
    public static final int GL_RGB8 = 32849;
    public static final int GL_RGB10 = 32850;
    public static final int GL_RGB12 = 32851;
    public static final int GL_RGB16 = 32852;
    public static final int GL_RGBA2 = 32853;
    public static final int GL_RGBA4 = 32854;
    public static final int GL_RGB5_A1 = 32855;
    public static final int GL_RGBA8 = 32856;
    public static final int GL_RGB10_A2 = 32857;
    public static final int GL_RGBA12 = 32858;
    public static final int GL_RGBA16 = 32859;
    public static final int GL_TEXTURE_RED_SIZE = 32860;
    public static final int GL_TEXTURE_GREEN_SIZE = 32861;
    public static final int GL_TEXTURE_BLUE_SIZE = 32862;
    public static final int GL_TEXTURE_ALPHA_SIZE = 32863;
    public static final int GL_TEXTURE_LUMINANCE_SIZE = 32864;
    public static final int GL_TEXTURE_INTENSITY_SIZE = 32865;
    public static final int GL_PROXY_TEXTURE_1D = 32867;
    public static final int GL_PROXY_TEXTURE_2D = 32868;
    public static final int GL_TEXTURE_PRIORITY = 32870;
    public static final int GL_TEXTURE_RESIDENT = 32871;
    public static final int GL_TEXTURE_BINDING_1D = 32872;
    public static final int GL_TEXTURE_BINDING_2D = 32873;
    public static final int GL_VERTEX_ARRAY = 32884;
    public static final int GL_NORMAL_ARRAY = 32885;
    public static final int GL_COLOR_ARRAY = 32886;
    public static final int GL_INDEX_ARRAY = 32887;
    public static final int GL_TEXTURE_COORD_ARRAY = 32888;
    public static final int GL_EDGE_FLAG_ARRAY = 32889;
    public static final int GL_VERTEX_ARRAY_SIZE = 32890;
    public static final int GL_VERTEX_ARRAY_TYPE = 32891;
    public static final int GL_VERTEX_ARRAY_STRIDE = 32892;
    public static final int GL_NORMAL_ARRAY_TYPE = 32894;
    public static final int GL_NORMAL_ARRAY_STRIDE = 32895;
    public static final int GL_COLOR_ARRAY_SIZE = 32897;
    public static final int GL_COLOR_ARRAY_TYPE = 32898;
    public static final int GL_COLOR_ARRAY_STRIDE = 32899;
    public static final int GL_INDEX_ARRAY_TYPE = 32901;
    public static final int GL_INDEX_ARRAY_STRIDE = 32902;
    public static final int GL_TEXTURE_COORD_ARRAY_SIZE = 32904;
    public static final int GL_TEXTURE_COORD_ARRAY_TYPE = 32905;
    public static final int GL_TEXTURE_COORD_ARRAY_STRIDE = 32906;
    public static final int GL_EDGE_FLAG_ARRAY_STRIDE = 32908;
    public static final int GL_VERTEX_ARRAY_POINTER = 32910;
    public static final int GL_NORMAL_ARRAY_POINTER = 32911;
    public static final int GL_COLOR_ARRAY_POINTER = 32912;
    public static final int GL_INDEX_ARRAY_POINTER = 32913;
    public static final int GL_TEXTURE_COORD_ARRAY_POINTER = 32914;
    public static final int GL_EDGE_FLAG_ARRAY_POINTER = 32915;
    public static final int GL_V2F = 10784;
    public static final int GL_V3F = 10785;
    public static final int GL_C4UB_V2F = 10786;
    public static final int GL_C4UB_V3F = 10787;
    public static final int GL_C3F_V3F = 10788;
    public static final int GL_N3F_V3F = 10789;
    public static final int GL_C4F_N3F_V3F = 10790;
    public static final int GL_T2F_V3F = 10791;
    public static final int GL_T4F_V4F = 10792;
    public static final int GL_T2F_C4UB_V3F = 10793;
    public static final int GL_T2F_C3F_V3F = 10794;
    public static final int GL_T2F_N3F_V3F = 10795;
    public static final int GL_T2F_C4F_N3F_V3F = 10796;
    public static final int GL_T4F_C4F_N3F_V4F = 10797;
    public static final int GL_LOGIC_OP = 3057;
    public static final int GL_TEXTURE_COMPONENTS = 4099;

    private GL11() {
    }

    public static void glAccum(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glAccum;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglAccum(n, f, l);
    }

    static native void nglAccum(int var0, float var1, long var2);

    public static void glAlphaFunc(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glAlphaFunc;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglAlphaFunc(n, f, l);
    }

    static native void nglAlphaFunc(int var0, float var1, long var2);

    public static void glClearColor(float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearColor;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglClearColor(f, f2, f3, f4, l);
    }

    static native void nglClearColor(float var0, float var1, float var2, float var3, long var4);

    public static void glClearAccum(float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearAccum;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglClearAccum(f, f2, f3, f4, l);
    }

    static native void nglClearAccum(float var0, float var1, float var2, float var3, long var4);

    public static void glClear(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClear;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglClear(n, l);
    }

    static native void nglClear(int var0, long var1);

    public static void glCallLists(ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCallLists;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        GL11.nglCallLists(byteBuffer.remaining(), 5121, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glCallLists(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCallLists;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL11.nglCallLists(intBuffer.remaining(), 5125, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glCallLists(ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCallLists;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        GL11.nglCallLists(shortBuffer.remaining(), 5123, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglCallLists(int var0, int var1, long var2, long var4);

    public static void glCallList(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCallList;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglCallList(n, l);
    }

    static native void nglCallList(int var0, long var1);

    public static void glBlendFunc(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendFunc;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglBlendFunc(n, n2, l);
    }

    static native void nglBlendFunc(int var0, int var1, long var2);

    public static void glBitmap(int n, int n2, float f, float f2, float f3, float f4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBitmap;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        if (byteBuffer != null) {
            BufferChecks.checkBuffer(byteBuffer, (n + 7) / 8 * n2);
        }
        GL11.nglBitmap(n, n2, f, f2, f3, f4, MemoryUtil.getAddressSafe(byteBuffer), l);
    }

    static native void nglBitmap(int var0, int var1, float var2, float var3, float var4, float var5, long var6, long var8);

    public static void glBitmap(int n, int n2, float f, float f2, float f3, float f4, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glBitmap;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL11.nglBitmapBO(n, n2, f, f2, f3, f4, l, l2);
    }

    static native void nglBitmapBO(int var0, int var1, float var2, float var3, float var4, float var5, long var6, long var8);

    public static void glBindTexture(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindTexture;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglBindTexture(n, n2, l);
    }

    static native void nglBindTexture(int var0, int var1, long var2);

    public static void glPrioritizeTextures(IntBuffer intBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPrioritizeTextures;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkBuffer(floatBuffer, intBuffer.remaining());
        GL11.nglPrioritizeTextures(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglPrioritizeTextures(int var0, long var1, long var3, long var5);

    public static boolean glAreTexturesResident(IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glAreTexturesResident;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkBuffer(byteBuffer, intBuffer.remaining());
        boolean bl = GL11.nglAreTexturesResident(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        return bl;
    }

    static native boolean nglAreTexturesResident(int var0, long var1, long var3, long var5);

    public static void glBegin(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBegin;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglBegin(n, l);
    }

    static native void nglBegin(int var0, long var1);

    public static void glEnd() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEnd;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglEnd(l);
    }

    static native void nglEnd(long var0);

    public static void glArrayElement(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glArrayElement;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglArrayElement(n, l);
    }

    static native void nglArrayElement(int var0, long var1);

    public static void glClearDepth(double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearDepth;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglClearDepth(d, l);
    }

    static native void nglClearDepth(double var0, long var2);

    public static void glDeleteLists(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteLists;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglDeleteLists(n, n2, l);
    }

    static native void nglDeleteLists(int var0, int var1, long var2);

    public static void glDeleteTextures(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteTextures;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL11.nglDeleteTextures(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeleteTextures(int var0, long var1, long var3);

    public static void glDeleteTextures(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteTextures;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglDeleteTextures(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static void glCullFace(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCullFace;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglCullFace(n, l);
    }

    static native void nglCullFace(int var0, long var1);

    public static void glCopyTexSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCopyTexSubImage2D;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglCopyTexSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, l);
    }

    static native void nglCopyTexSubImage2D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8);

    public static void glCopyTexSubImage1D(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCopyTexSubImage1D;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglCopyTexSubImage1D(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglCopyTexSubImage1D(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glCopyTexImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCopyTexImage2D;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglCopyTexImage2D(n, n2, n3, n4, n5, n6, n7, n8, l);
    }

    static native void nglCopyTexImage2D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8);

    public static void glCopyTexImage1D(int n, int n2, int n3, int n4, int n5, int n6, int n7) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCopyTexImage1D;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglCopyTexImage1D(n, n2, n3, n4, n5, n6, n7, l);
    }

    static native void nglCopyTexImage1D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, long var7);

    public static void glCopyPixels(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCopyPixels;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglCopyPixels(n, n2, n3, n4, n5, l);
    }

    static native void nglCopyPixels(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glColorPointer(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glColorPointer_pointer = doubleBuffer;
        }
        GL11.nglColorPointer(n, 5130, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glColorPointer(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glColorPointer_pointer = floatBuffer;
        }
        GL11.nglColorPointer(n, 5126, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glColorPointer(int n, boolean bl, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glColorPointer_pointer = byteBuffer;
        }
        GL11.nglColorPointer(n, bl ? 5121 : 5120, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglColorPointer(int var0, int var1, int var2, long var3, long var5);

    public static void glColorPointer(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glColorPointer;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        GL11.nglColorPointerBO(n, n2, n3, l, l2);
    }

    static native void nglColorPointerBO(int var0, int var1, int var2, long var3, long var5);

    public static void glColorPointer(int n, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glColorPointer_pointer = byteBuffer;
        }
        GL11.nglColorPointer(n, n2, n3, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glColorMaterial(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorMaterial;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglColorMaterial(n, n2, l);
    }

    static native void nglColorMaterial(int var0, int var1, long var2);

    public static void glColorMask(boolean bl, boolean bl2, boolean bl3, boolean bl4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColorMask;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglColorMask(bl, bl2, bl3, bl4, l);
    }

    static native void nglColorMask(boolean var0, boolean var1, boolean var2, boolean var3, long var4);

    public static void glColor3b(byte by, byte by2, byte by3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColor3b;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglColor3b(by, by2, by3, l);
    }

    static native void nglColor3b(byte var0, byte var1, byte var2, long var3);

    public static void glColor3f(float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColor3f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglColor3f(f, f2, f3, l);
    }

    static native void nglColor3f(float var0, float var1, float var2, long var3);

    public static void glColor3d(double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColor3d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglColor3d(d, d2, d3, l);
    }

    static native void nglColor3d(double var0, double var2, double var4, long var6);

    public static void glColor3ub(byte by, byte by2, byte by3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColor3ub;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglColor3ub(by, by2, by3, l);
    }

    static native void nglColor3ub(byte var0, byte var1, byte var2, long var3);

    public static void glColor4b(byte by, byte by2, byte by3, byte by4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColor4b;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglColor4b(by, by2, by3, by4, l);
    }

    static native void nglColor4b(byte var0, byte var1, byte var2, byte var3, long var4);

    public static void glColor4f(float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColor4f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglColor4f(f, f2, f3, f4, l);
    }

    static native void nglColor4f(float var0, float var1, float var2, float var3, long var4);

    public static void glColor4d(double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColor4d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglColor4d(d, d2, d3, d4, l);
    }

    static native void nglColor4d(double var0, double var2, double var4, double var6, long var8);

    public static void glColor4ub(byte by, byte by2, byte by3, byte by4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glColor4ub;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglColor4ub(by, by2, by3, by4, l);
    }

    static native void nglColor4ub(byte var0, byte var1, byte var2, byte var3, long var4);

    public static void glClipPlane(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClipPlane;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        GL11.nglClipPlane(n, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglClipPlane(int var0, long var1, long var3);

    public static void glClearStencil(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearStencil;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglClearStencil(n, l);
    }

    static native void nglClearStencil(int var0, long var1);

    public static void glEvalPoint1(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEvalPoint1;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglEvalPoint1(n, l);
    }

    static native void nglEvalPoint1(int var0, long var1);

    public static void glEvalPoint2(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEvalPoint2;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglEvalPoint2(n, n2, l);
    }

    static native void nglEvalPoint2(int var0, int var1, long var2);

    public static void glEvalMesh1(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEvalMesh1;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglEvalMesh1(n, n2, n3, l);
    }

    static native void nglEvalMesh1(int var0, int var1, int var2, long var3);

    public static void glEvalMesh2(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEvalMesh2;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglEvalMesh2(n, n2, n3, n4, n5, l);
    }

    static native void nglEvalMesh2(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glEvalCoord1f(float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEvalCoord1f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglEvalCoord1f(f, l);
    }

    static native void nglEvalCoord1f(float var0, long var1);

    public static void glEvalCoord1d(double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEvalCoord1d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglEvalCoord1d(d, l);
    }

    static native void nglEvalCoord1d(double var0, long var2);

    public static void glEvalCoord2f(float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEvalCoord2f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglEvalCoord2f(f, f2, l);
    }

    static native void nglEvalCoord2f(float var0, float var1, long var2);

    public static void glEvalCoord2d(double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEvalCoord2d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglEvalCoord2d(d, d2, l);
    }

    static native void nglEvalCoord2d(double var0, double var2, long var4);

    public static void glEnableClientState(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEnableClientState;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglEnableClientState(n, l);
    }

    static native void nglEnableClientState(int var0, long var1);

    public static void glDisableClientState(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDisableClientState;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglDisableClientState(n, l);
    }

    static native void nglDisableClientState(int var0, long var1);

    public static void glEnable(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEnable;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglEnable(n, l);
    }

    static native void nglEnable(int var0, long var1);

    public static void glDisable(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDisable;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglDisable(n, l);
    }

    static native void nglDisable(int var0, long var1);

    public static void glEdgeFlagPointer(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEdgeFlagPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glEdgeFlagPointer_pointer = byteBuffer;
        }
        GL11.nglEdgeFlagPointer(n, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglEdgeFlagPointer(int var0, long var1, long var3);

    public static void glEdgeFlagPointer(int n, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glEdgeFlagPointer;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        GL11.nglEdgeFlagPointerBO(n, l, l2);
    }

    static native void nglEdgeFlagPointerBO(int var0, long var1, long var3);

    public static void glEdgeFlag(boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEdgeFlag;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglEdgeFlag(bl, l);
    }

    static native void nglEdgeFlag(boolean var0, long var1);

    public static void glDrawPixels(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawPixels;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ByteBuffer byteBuffer2 = byteBuffer;
        BufferChecks.checkBuffer(byteBuffer2, GLChecks.calculateImageStorage(byteBuffer2, n3, n4, n, n2, 1));
        GL11.nglDrawPixels(n, n2, n3, n4, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glDrawPixels(int n, int n2, int n3, int n4, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawPixels;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        IntBuffer intBuffer2 = intBuffer;
        BufferChecks.checkBuffer(intBuffer2, GLChecks.calculateImageStorage(intBuffer2, n3, n4, n, n2, 1));
        GL11.nglDrawPixels(n, n2, n3, n4, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glDrawPixels(int n, int n2, int n3, int n4, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawPixels;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ShortBuffer shortBuffer2 = shortBuffer;
        BufferChecks.checkBuffer(shortBuffer2, GLChecks.calculateImageStorage(shortBuffer2, n3, n4, n, n2, 1));
        GL11.nglDrawPixels(n, n2, n3, n4, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglDrawPixels(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glDrawPixels(int n, int n2, int n3, int n4, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glDrawPixels;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL11.nglDrawPixelsBO(n, n2, n3, n4, l, l2);
    }

    static native void nglDrawPixelsBO(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glDrawElements(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElements;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL11.nglDrawElements(n, byteBuffer.remaining(), 5121, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glDrawElements(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElements;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        GL11.nglDrawElements(n, intBuffer.remaining(), 5125, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glDrawElements(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElements;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        GL11.nglDrawElements(n, shortBuffer.remaining(), 5123, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglDrawElements(int var0, int var1, int var2, long var3, long var5);

    public static void glDrawElements(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glDrawElements;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureElementVBOenabled(contextCapabilities);
        GL11.nglDrawElementsBO(n, n2, n3, l, l2);
    }

    static native void nglDrawElementsBO(int var0, int var1, int var2, long var3, long var5);

    public static void glDrawElements(int n, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElements;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, n2);
        GL11.nglDrawElements(n, n2, n3, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glDrawBuffer(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawBuffer;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglDrawBuffer(n, l);
    }

    static native void nglDrawBuffer(int var0, long var1);

    public static void glDrawArrays(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawArrays;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglDrawArrays(n, n2, n3, l);
    }

    static native void nglDrawArrays(int var0, int var1, int var2, long var3);

    public static void glDepthRange(double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDepthRange;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglDepthRange(d, d2, l);
    }

    static native void nglDepthRange(double var0, double var2, long var4);

    public static void glDepthMask(boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDepthMask;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglDepthMask(bl, l);
    }

    static native void nglDepthMask(boolean var0, long var1);

    public static void glDepthFunc(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDepthFunc;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglDepthFunc(n, l);
    }

    static native void nglDepthFunc(int var0, long var1);

    public static void glFeedbackBuffer(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFeedbackBuffer;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL11.nglFeedbackBuffer(floatBuffer.remaining(), n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglFeedbackBuffer(int var0, int var1, long var2, long var4);

    public static void glGetPixelMap(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPixelMapfv;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(floatBuffer, 256);
        GL11.nglGetPixelMapfv(n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetPixelMapfv(int var0, long var1, long var3);

    public static void glGetPixelMapfv(int n, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetPixelMapfv;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        GL11.nglGetPixelMapfvBO(n, l, l2);
    }

    static native void nglGetPixelMapfvBO(int var0, long var1, long var3);

    public static void glGetPixelMapu(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPixelMapuiv;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(intBuffer, 256);
        GL11.nglGetPixelMapuiv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetPixelMapuiv(int var0, long var1, long var3);

    public static void glGetPixelMapuiv(int n, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetPixelMapuiv;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        GL11.nglGetPixelMapuivBO(n, l, l2);
    }

    static native void nglGetPixelMapuivBO(int var0, long var1, long var3);

    public static void glGetPixelMapu(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPixelMapusv;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(shortBuffer, 256);
        GL11.nglGetPixelMapusv(n, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetPixelMapusv(int var0, long var1, long var3);

    public static void glGetPixelMapusv(int n, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetPixelMapusv;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        GL11.nglGetPixelMapusvBO(n, l, l2);
    }

    static native void nglGetPixelMapusvBO(int var0, long var1, long var3);

    public static void glGetMaterial(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMaterialfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL11.nglGetMaterialfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetMaterialfv(int var0, int var1, long var2, long var4);

    public static void glGetMaterial(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMaterialiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL11.nglGetMaterialiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetMaterialiv(int var0, int var1, long var2, long var4);

    public static void glGetMap(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMapfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 256);
        GL11.nglGetMapfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetMapfv(int var0, int var1, long var2, long var4);

    public static void glGetMap(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMapdv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 256);
        GL11.nglGetMapdv(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetMapdv(int var0, int var1, long var2, long var4);

    public static void glGetMap(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMapiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 256);
        GL11.nglGetMapiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetMapiv(int var0, int var1, long var2, long var4);

    public static void glGetLight(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetLightfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL11.nglGetLightfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetLightfv(int var0, int var1, long var2, long var4);

    public static void glGetLight(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetLightiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL11.nglGetLightiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetLightiv(int var0, int var1, long var2, long var4);

    public static int glGetError() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetError;
        BufferChecks.checkFunctionAddress(l);
        int n = GL11.nglGetError(l);
        return n;
    }

    static native int nglGetError(long var0);

    public static void glGetClipPlane(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetClipPlane;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        GL11.nglGetClipPlane(n, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetClipPlane(int var0, long var1, long var3);

    public static void glGetBoolean(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetBooleanv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 16);
        GL11.nglGetBooleanv(n, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetBooleanv(int var0, long var1, long var3);

    public static boolean glGetBoolean(int n) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetBooleanv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferByte((ContextCapabilities)object, 1);
        GL11.nglGetBooleanv(n, MemoryUtil.getAddress((ByteBuffer)object), l);
        return ((ByteBuffer)object).get(0) == 1;
    }

    public static void glGetDouble(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetDoublev;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 16);
        GL11.nglGetDoublev(n, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetDoublev(int var0, long var1, long var3);

    public static double glGetDouble(int n) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetDoublev;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferDouble((ContextCapabilities)object);
        GL11.nglGetDoublev(n, MemoryUtil.getAddress((DoubleBuffer)object), l);
        return ((DoubleBuffer)object).get(0);
    }

    public static void glGetFloat(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetFloatv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 16);
        GL11.nglGetFloatv(n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetFloatv(int var0, long var1, long var3);

    public static float glGetFloat(int n) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetFloatv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        GL11.nglGetFloatv(n, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetInteger(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetIntegerv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 16);
        GL11.nglGetIntegerv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetIntegerv(int var0, long var1, long var3);

    public static int glGetInteger(int n) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetIntegerv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL11.nglGetIntegerv(n, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGenTextures(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenTextures;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL11.nglGenTextures(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenTextures(int var0, long var1, long var3);

    public static int glGenTextures() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenTextures;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL11.nglGenTextures(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static int glGenLists(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenLists;
        BufferChecks.checkFunctionAddress(l);
        n = GL11.nglGenLists(n, l);
        return n;
    }

    static native int nglGenLists(int var0, long var1);

    public static void glFrustum(double d, double d2, double d3, double d4, double d5, double d6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFrustum;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglFrustum(d, d2, d3, d4, d5, d6, l);
    }

    static native void nglFrustum(double var0, double var2, double var4, double var6, double var8, double var10, long var12);

    public static void glFrontFace(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFrontFace;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglFrontFace(n, l);
    }

    static native void nglFrontFace(int var0, long var1);

    public static void glFogf(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFogf;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglFogf(n, f, l);
    }

    static native void nglFogf(int var0, float var1, long var2);

    public static void glFogi(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFogi;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglFogi(n, n2, l);
    }

    static native void nglFogi(int var0, int var1, long var2);

    public static void glFog(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFogfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL11.nglFogfv(n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglFogfv(int var0, long var1, long var3);

    public static void glFog(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFogiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL11.nglFogiv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglFogiv(int var0, long var1, long var3);

    public static void glFlush() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFlush;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglFlush(l);
    }

    static native void nglFlush(long var0);

    public static void glFinish() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFinish;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglFinish(l);
    }

    static native void nglFinish(long var0);

    public static ByteBuffer glGetPointer(int n, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetPointerv;
        BufferChecks.checkFunctionAddress(l2);
        ByteBuffer byteBuffer = GL11.nglGetPointerv(n, l, l2);
        if (LWJGLUtil.CHECKS && byteBuffer == null) {
            return null;
        }
        return byteBuffer.order(ByteOrder.nativeOrder());
    }

    static native ByteBuffer nglGetPointerv(int var0, long var1, long var3);

    public static boolean glIsEnabled(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsEnabled;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = GL11.nglIsEnabled(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsEnabled(int var0, long var1);

    public static void glInterleavedArrays(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInterleavedArrays;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL11.nglInterleavedArrays(n, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glInterleavedArrays(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInterleavedArrays;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        GL11.nglInterleavedArrays(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glInterleavedArrays(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInterleavedArrays;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        GL11.nglInterleavedArrays(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glInterleavedArrays(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInterleavedArrays;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        GL11.nglInterleavedArrays(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glInterleavedArrays(int n, int n2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInterleavedArrays;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        GL11.nglInterleavedArrays(n, n2, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglInterleavedArrays(int var0, int var1, long var2, long var4);

    public static void glInterleavedArrays(int n, int n2, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glInterleavedArrays;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        GL11.nglInterleavedArraysBO(n, n2, l, l2);
    }

    static native void nglInterleavedArraysBO(int var0, int var1, long var2, long var4);

    public static void glInitNames() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInitNames;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglInitNames(l);
    }

    static native void nglInitNames(long var0);

    public static void glHint(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glHint;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglHint(n, n2, l);
    }

    static native void nglHint(int var0, int var1, long var2);

    public static void glGetTexParameter(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL11.nglGetTexParameterfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetTexParameterfv(int var0, int var1, long var2, long var4);

    public static float glGetTexParameterf(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTexParameterfv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        GL11.nglGetTexParameterfv(n, n2, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetTexParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL11.nglGetTexParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetTexParameteriv(int var0, int var1, long var2, long var4);

    public static int glGetTexParameteri(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTexParameteriv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL11.nglGetTexParameteriv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetTexLevelParameter(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexLevelParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL11.nglGetTexLevelParameterfv(n, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetTexLevelParameterfv(int var0, int var1, int var2, long var3, long var5);

    public static float glGetTexLevelParameterf(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTexLevelParameterfv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        GL11.nglGetTexLevelParameterfv(n, n2, n3, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetTexLevelParameter(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexLevelParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL11.nglGetTexLevelParameteriv(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetTexLevelParameteriv(int var0, int var1, int var2, long var3, long var5);

    public static int glGetTexLevelParameteri(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTexLevelParameteriv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL11.nglGetTexLevelParameteriv(n, n2, n3, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetTexImage(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        ByteBuffer byteBuffer2 = byteBuffer;
        BufferChecks.checkBuffer(byteBuffer2, GLChecks.calculateImageStorage(byteBuffer2, n3, n4, 1, 1, 1));
        GL11.nglGetTexImage(n, n2, n3, n4, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetTexImage(int n, int n2, int n3, int n4, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        DoubleBuffer doubleBuffer2 = doubleBuffer;
        BufferChecks.checkBuffer(doubleBuffer2, GLChecks.calculateImageStorage(doubleBuffer2, n3, n4, 1, 1, 1));
        GL11.nglGetTexImage(n, n2, n3, n4, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetTexImage(int n, int n2, int n3, int n4, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        FloatBuffer floatBuffer2 = floatBuffer;
        BufferChecks.checkBuffer(floatBuffer2, GLChecks.calculateImageStorage(floatBuffer2, n3, n4, 1, 1, 1));
        GL11.nglGetTexImage(n, n2, n3, n4, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetTexImage(int n, int n2, int n3, int n4, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        IntBuffer intBuffer2 = intBuffer;
        BufferChecks.checkBuffer(intBuffer2, GLChecks.calculateImageStorage(intBuffer2, n3, n4, 1, 1, 1));
        GL11.nglGetTexImage(n, n2, n3, n4, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetTexImage(int n, int n2, int n3, int n4, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        ShortBuffer shortBuffer2 = shortBuffer;
        BufferChecks.checkBuffer(shortBuffer2, GLChecks.calculateImageStorage(shortBuffer2, n3, n4, 1, 1, 1));
        GL11.nglGetTexImage(n, n2, n3, n4, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetTexImage(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glGetTexImage(int n, int n2, int n3, int n4, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetTexImage;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        GL11.nglGetTexImageBO(n, n2, n3, n4, l, l2);
    }

    static native void nglGetTexImageBO(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glGetTexGen(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexGeniv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL11.nglGetTexGeniv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetTexGeniv(int var0, int var1, long var2, long var4);

    public static int glGetTexGeni(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTexGeniv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL11.nglGetTexGeniv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetTexGen(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexGenfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL11.nglGetTexGenfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetTexGenfv(int var0, int var1, long var2, long var4);

    public static float glGetTexGenf(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTexGenfv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        GL11.nglGetTexGenfv(n, n2, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetTexGen(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexGendv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        GL11.nglGetTexGendv(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetTexGendv(int var0, int var1, long var2, long var4);

    public static double glGetTexGend(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTexGendv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferDouble((ContextCapabilities)object);
        GL11.nglGetTexGendv(n, n2, MemoryUtil.getAddress((DoubleBuffer)object), l);
        return ((DoubleBuffer)object).get(0);
    }

    public static void glGetTexEnv(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexEnviv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL11.nglGetTexEnviv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetTexEnviv(int var0, int var1, long var2, long var4);

    public static int glGetTexEnvi(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTexEnviv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL11.nglGetTexEnviv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetTexEnv(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTexEnvfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL11.nglGetTexEnvfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetTexEnvfv(int var0, int var1, long var2, long var4);

    public static float glGetTexEnvf(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTexEnvfv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        GL11.nglGetTexEnvfv(n, n2, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static String glGetString(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetString;
        BufferChecks.checkFunctionAddress(l);
        String string = GL11.nglGetString(n, l);
        return string;
    }

    static native String nglGetString(int var0, long var1);

    public static void glGetPolygonStipple(ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPolygonStipple;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, 128);
        GL11.nglGetPolygonStipple(MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetPolygonStipple(long var0, long var2);

    public static void glGetPolygonStipple(long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetPolygonStipple;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        GL11.nglGetPolygonStippleBO(l, l2);
    }

    static native void nglGetPolygonStippleBO(long var0, long var2);

    public static boolean glIsList(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsList;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = GL11.nglIsList(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsList(int var0, long var1);

    public static void glMaterialf(int n, int n2, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMaterialf;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglMaterialf(n, n2, f, l);
    }

    static native void nglMaterialf(int var0, int var1, float var2, long var3);

    public static void glMateriali(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMateriali;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglMateriali(n, n2, n3, l);
    }

    static native void nglMateriali(int var0, int var1, int var2, long var3);

    public static void glMaterial(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMaterialfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL11.nglMaterialfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglMaterialfv(int var0, int var1, long var2, long var4);

    public static void glMaterial(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMaterialiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL11.nglMaterialiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglMaterialiv(int var0, int var1, long var2, long var4);

    public static void glMapGrid1f(int n, float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMapGrid1f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglMapGrid1f(n, f, f2, l);
    }

    static native void nglMapGrid1f(int var0, float var1, float var2, long var3);

    public static void glMapGrid1d(int n, double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMapGrid1d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglMapGrid1d(n, d, d2, l);
    }

    static native void nglMapGrid1d(int var0, double var1, double var3, long var5);

    public static void glMapGrid2f(int n, float f, float f2, int n2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMapGrid2f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglMapGrid2f(n, f, f2, n2, f3, f4, l);
    }

    static native void nglMapGrid2f(int var0, float var1, float var2, int var3, float var4, float var5, long var6);

    public static void glMapGrid2d(int n, double d, double d2, int n2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMapGrid2d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglMapGrid2d(n, d, d2, n2, d3, d4, l);
    }

    static native void nglMapGrid2d(int var0, double var1, double var3, int var5, double var6, double var8, long var10);

    public static void glMap2f(int n, float f, float f2, int n2, int n3, float f3, float f4, int n4, int n5, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMap2f;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL11.nglMap2f(n, f, f2, n2, n3, f3, f4, n4, n5, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglMap2f(int var0, float var1, float var2, int var3, int var4, float var5, float var6, int var7, int var8, long var9, long var11);

    public static void glMap2d(int n, double d, double d2, int n2, int n3, double d3, double d4, int n4, int n5, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMap2d;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL11.nglMap2d(n, d, d2, n2, n3, d3, d4, n4, n5, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglMap2d(int var0, double var1, double var3, int var5, int var6, double var7, double var9, int var11, int var12, long var13, long var15);

    public static void glMap1f(int n, float f, float f2, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMap1f;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL11.nglMap1f(n, f, f2, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglMap1f(int var0, float var1, float var2, int var3, int var4, long var5, long var7);

    public static void glMap1d(int n, double d, double d2, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMap1d;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL11.nglMap1d(n, d, d2, n2, n3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglMap1d(int var0, double var1, double var3, int var5, int var6, long var7, long var9);

    public static void glLogicOp(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLogicOp;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglLogicOp(n, l);
    }

    static native void nglLogicOp(int var0, long var1);

    public static void glLoadName(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLoadName;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglLoadName(n, l);
    }

    static native void nglLoadName(int var0, long var1);

    public static void glLoadMatrix(FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLoadMatrixf;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 16);
        GL11.nglLoadMatrixf(MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglLoadMatrixf(long var0, long var2);

    public static void glLoadMatrix(DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLoadMatrixd;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 16);
        GL11.nglLoadMatrixd(MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglLoadMatrixd(long var0, long var2);

    public static void glLoadIdentity() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLoadIdentity;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglLoadIdentity(l);
    }

    static native void nglLoadIdentity(long var0);

    public static void glListBase(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glListBase;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglListBase(n, l);
    }

    static native void nglListBase(int var0, long var1);

    public static void glLineWidth(float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLineWidth;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglLineWidth(f, l);
    }

    static native void nglLineWidth(float var0, long var1);

    public static void glLineStipple(int n, short s) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLineStipple;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglLineStipple(n, s, l);
    }

    static native void nglLineStipple(int var0, short var1, long var2);

    public static void glLightModelf(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLightModelf;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglLightModelf(n, f, l);
    }

    static native void nglLightModelf(int var0, float var1, long var2);

    public static void glLightModeli(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLightModeli;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglLightModeli(n, n2, l);
    }

    static native void nglLightModeli(int var0, int var1, long var2);

    public static void glLightModel(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLightModelfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL11.nglLightModelfv(n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglLightModelfv(int var0, long var1, long var3);

    public static void glLightModel(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLightModeliv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL11.nglLightModeliv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglLightModeliv(int var0, long var1, long var3);

    public static void glLightf(int n, int n2, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLightf;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglLightf(n, n2, f, l);
    }

    static native void nglLightf(int var0, int var1, float var2, long var3);

    public static void glLighti(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLighti;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglLighti(n, n2, n3, l);
    }

    static native void nglLighti(int var0, int var1, int var2, long var3);

    public static void glLight(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLightfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL11.nglLightfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglLightfv(int var0, int var1, long var2, long var4);

    public static void glLight(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLightiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL11.nglLightiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglLightiv(int var0, int var1, long var2, long var4);

    public static boolean glIsTexture(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsTexture;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = GL11.nglIsTexture(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsTexture(int var0, long var1);

    public static void glMatrixMode(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMatrixMode;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglMatrixMode(n, l);
    }

    static native void nglMatrixMode(int var0, long var1);

    public static void glPolygonStipple(ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPolygonStipple;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, 128);
        GL11.nglPolygonStipple(MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglPolygonStipple(long var0, long var2);

    public static void glPolygonStipple(long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glPolygonStipple;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL11.nglPolygonStippleBO(l, l2);
    }

    static native void nglPolygonStippleBO(long var0, long var2);

    public static void glPolygonOffset(float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPolygonOffset;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPolygonOffset(f, f2, l);
    }

    static native void nglPolygonOffset(float var0, float var1, long var2);

    public static void glPolygonMode(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPolygonMode;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPolygonMode(n, n2, l);
    }

    static native void nglPolygonMode(int var0, int var1, long var2);

    public static void glPointSize(float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPointSize;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPointSize(f, l);
    }

    static native void nglPointSize(float var0, long var1);

    public static void glPixelZoom(float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPixelZoom;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPixelZoom(f, f2, l);
    }

    static native void nglPixelZoom(float var0, float var1, long var2);

    public static void glPixelTransferf(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPixelTransferf;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPixelTransferf(n, f, l);
    }

    static native void nglPixelTransferf(int var0, float var1, long var2);

    public static void glPixelTransferi(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPixelTransferi;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPixelTransferi(n, n2, l);
    }

    static native void nglPixelTransferi(int var0, int var1, long var2);

    public static void glPixelStoref(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPixelStoref;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPixelStoref(n, f, l);
    }

    static native void nglPixelStoref(int var0, float var1, long var2);

    public static void glPixelStorei(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPixelStorei;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPixelStorei(n, n2, l);
    }

    static native void nglPixelStorei(int var0, int var1, long var2);

    public static void glPixelMap(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPixelMapfv;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        GL11.nglPixelMapfv(n, floatBuffer.remaining(), MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglPixelMapfv(int var0, int var1, long var2, long var4);

    public static void glPixelMapfv(int n, int n2, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glPixelMapfv;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL11.nglPixelMapfvBO(n, n2, l, l2);
    }

    static native void nglPixelMapfvBO(int var0, int var1, long var2, long var4);

    public static void glPixelMapu(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPixelMapuiv;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        GL11.nglPixelMapuiv(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglPixelMapuiv(int var0, int var1, long var2, long var4);

    public static void glPixelMapuiv(int n, int n2, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glPixelMapuiv;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL11.nglPixelMapuivBO(n, n2, l, l2);
    }

    static native void nglPixelMapuivBO(int var0, int var1, long var2, long var4);

    public static void glPixelMapu(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPixelMapusv;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        GL11.nglPixelMapusv(n, shortBuffer.remaining(), MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglPixelMapusv(int var0, int var1, long var2, long var4);

    public static void glPixelMapusv(int n, int n2, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glPixelMapusv;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL11.nglPixelMapusvBO(n, n2, l, l2);
    }

    static native void nglPixelMapusvBO(int var0, int var1, long var2, long var4);

    public static void glPassThrough(float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPassThrough;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPassThrough(f, l);
    }

    static native void nglPassThrough(float var0, long var1);

    public static void glOrtho(double d, double d2, double d3, double d4, double d5, double d6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glOrtho;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglOrtho(d, d2, d3, d4, d5, d6, l);
    }

    static native void nglOrtho(double var0, double var2, double var4, double var6, double var8, double var10, long var12);

    public static void glNormalPointer(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormalPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glNormalPointer_pointer = byteBuffer;
        }
        GL11.nglNormalPointer(5120, n, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glNormalPointer(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormalPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glNormalPointer_pointer = doubleBuffer;
        }
        GL11.nglNormalPointer(5130, n, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glNormalPointer(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormalPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glNormalPointer_pointer = floatBuffer;
        }
        GL11.nglNormalPointer(5126, n, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glNormalPointer(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormalPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glNormalPointer_pointer = intBuffer;
        }
        GL11.nglNormalPointer(5124, n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglNormalPointer(int var0, int var1, long var2, long var4);

    public static void glNormalPointer(int n, int n2, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glNormalPointer;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        GL11.nglNormalPointerBO(n, n2, l, l2);
    }

    static native void nglNormalPointerBO(int var0, int var1, long var2, long var4);

    public static void glNormalPointer(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormalPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glNormalPointer_pointer = byteBuffer;
        }
        GL11.nglNormalPointer(n, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glNormal3b(byte by, byte by2, byte by3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormal3b;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglNormal3b(by, by2, by3, l);
    }

    static native void nglNormal3b(byte var0, byte var1, byte var2, long var3);

    public static void glNormal3f(float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormal3f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglNormal3f(f, f2, f3, l);
    }

    static native void nglNormal3f(float var0, float var1, float var2, long var3);

    public static void glNormal3d(double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormal3d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglNormal3d(d, d2, d3, l);
    }

    static native void nglNormal3d(double var0, double var2, double var4, long var6);

    public static void glNormal3i(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNormal3i;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglNormal3i(n, n2, n3, l);
    }

    static native void nglNormal3i(int var0, int var1, int var2, long var3);

    public static void glNewList(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNewList;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglNewList(n, n2, l);
    }

    static native void nglNewList(int var0, int var1, long var2);

    public static void glEndList() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEndList;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglEndList(l);
    }

    static native void nglEndList(long var0);

    public static void glMultMatrix(FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultMatrixf;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 16);
        GL11.nglMultMatrixf(MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglMultMatrixf(long var0, long var2);

    public static void glMultMatrix(DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultMatrixd;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 16);
        GL11.nglMultMatrixd(MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglMultMatrixd(long var0, long var2);

    public static void glShadeModel(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glShadeModel;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglShadeModel(n, l);
    }

    static native void nglShadeModel(int var0, long var1);

    public static void glSelectBuffer(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSelectBuffer;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL11.nglSelectBuffer(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglSelectBuffer(int var0, long var1, long var3);

    public static void glScissor(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glScissor;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglScissor(n, n2, n3, n4, l);
    }

    static native void nglScissor(int var0, int var1, int var2, int var3, long var4);

    public static void glScalef(float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glScalef;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglScalef(f, f2, f3, l);
    }

    static native void nglScalef(float var0, float var1, float var2, long var3);

    public static void glScaled(double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glScaled;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglScaled(d, d2, d3, l);
    }

    static native void nglScaled(double var0, double var2, double var4, long var6);

    public static void glRotatef(float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRotatef;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglRotatef(f, f2, f3, f4, l);
    }

    static native void nglRotatef(float var0, float var1, float var2, float var3, long var4);

    public static void glRotated(double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRotated;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglRotated(d, d2, d3, d4, l);
    }

    static native void nglRotated(double var0, double var2, double var4, double var6, long var8);

    public static int glRenderMode(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRenderMode;
        BufferChecks.checkFunctionAddress(l);
        n = GL11.nglRenderMode(n, l);
        return n;
    }

    static native int nglRenderMode(int var0, long var1);

    public static void glRectf(float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRectf;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglRectf(f, f2, f3, f4, l);
    }

    static native void nglRectf(float var0, float var1, float var2, float var3, long var4);

    public static void glRectd(double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRectd;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglRectd(d, d2, d3, d4, l);
    }

    static native void nglRectd(double var0, double var2, double var4, double var6, long var8);

    public static void glRecti(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRecti;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglRecti(n, n2, n3, n4, l);
    }

    static native void nglRecti(int var0, int var1, int var2, int var3, long var4);

    public static void glReadPixels(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadPixels;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        ByteBuffer byteBuffer2 = byteBuffer;
        BufferChecks.checkBuffer(byteBuffer2, GLChecks.calculateImageStorage(byteBuffer2, n5, n6, n3, n4, 1));
        GL11.nglReadPixels(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glReadPixels(int n, int n2, int n3, int n4, int n5, int n6, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadPixels;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        DoubleBuffer doubleBuffer2 = doubleBuffer;
        BufferChecks.checkBuffer(doubleBuffer2, GLChecks.calculateImageStorage(doubleBuffer2, n5, n6, n3, n4, 1));
        GL11.nglReadPixels(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glReadPixels(int n, int n2, int n3, int n4, int n5, int n6, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadPixels;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        FloatBuffer floatBuffer2 = floatBuffer;
        BufferChecks.checkBuffer(floatBuffer2, GLChecks.calculateImageStorage(floatBuffer2, n5, n6, n3, n4, 1));
        GL11.nglReadPixels(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glReadPixels(int n, int n2, int n3, int n4, int n5, int n6, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadPixels;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        IntBuffer intBuffer2 = intBuffer;
        BufferChecks.checkBuffer(intBuffer2, GLChecks.calculateImageStorage(intBuffer2, n5, n6, n3, n4, 1));
        GL11.nglReadPixels(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glReadPixels(int n, int n2, int n3, int n4, int n5, int n6, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadPixels;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        ShortBuffer shortBuffer2 = shortBuffer;
        BufferChecks.checkBuffer(shortBuffer2, GLChecks.calculateImageStorage(shortBuffer2, n5, n6, n3, n4, 1));
        GL11.nglReadPixels(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglReadPixels(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glReadPixels(int n, int n2, int n3, int n4, int n5, int n6, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glReadPixels;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        GL11.nglReadPixelsBO(n, n2, n3, n4, n5, n6, l, l2);
    }

    static native void nglReadPixelsBO(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glReadBuffer(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadBuffer;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglReadBuffer(n, l);
    }

    static native void nglReadBuffer(int var0, long var1);

    public static void glRasterPos2f(float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRasterPos2f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglRasterPos2f(f, f2, l);
    }

    static native void nglRasterPos2f(float var0, float var1, long var2);

    public static void glRasterPos2d(double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRasterPos2d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglRasterPos2d(d, d2, l);
    }

    static native void nglRasterPos2d(double var0, double var2, long var4);

    public static void glRasterPos2i(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRasterPos2i;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglRasterPos2i(n, n2, l);
    }

    static native void nglRasterPos2i(int var0, int var1, long var2);

    public static void glRasterPos3f(float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRasterPos3f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglRasterPos3f(f, f2, f3, l);
    }

    static native void nglRasterPos3f(float var0, float var1, float var2, long var3);

    public static void glRasterPos3d(double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRasterPos3d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglRasterPos3d(d, d2, d3, l);
    }

    static native void nglRasterPos3d(double var0, double var2, double var4, long var6);

    public static void glRasterPos3i(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRasterPos3i;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglRasterPos3i(n, n2, n3, l);
    }

    static native void nglRasterPos3i(int var0, int var1, int var2, long var3);

    public static void glRasterPos4f(float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRasterPos4f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglRasterPos4f(f, f2, f3, f4, l);
    }

    static native void nglRasterPos4f(float var0, float var1, float var2, float var3, long var4);

    public static void glRasterPos4d(double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRasterPos4d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglRasterPos4d(d, d2, d3, d4, l);
    }

    static native void nglRasterPos4d(double var0, double var2, double var4, double var6, long var8);

    public static void glRasterPos4i(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRasterPos4i;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglRasterPos4i(n, n2, n3, n4, l);
    }

    static native void nglRasterPos4i(int var0, int var1, int var2, int var3, long var4);

    public static void glPushName(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPushName;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPushName(n, l);
    }

    static native void nglPushName(int var0, long var1);

    public static void glPopName() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPopName;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPopName(l);
    }

    static native void nglPopName(long var0);

    public static void glPushMatrix() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPushMatrix;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPushMatrix(l);
    }

    static native void nglPushMatrix(long var0);

    public static void glPopMatrix() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPopMatrix;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPopMatrix(l);
    }

    static native void nglPopMatrix(long var0);

    public static void glPushClientAttrib(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPushClientAttrib;
        BufferChecks.checkFunctionAddress(l);
        StateTracker.pushAttrib(contextCapabilities, n);
        GL11.nglPushClientAttrib(n, l);
    }

    static native void nglPushClientAttrib(int var0, long var1);

    public static void glPopClientAttrib() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPopClientAttrib;
        BufferChecks.checkFunctionAddress(l);
        StateTracker.popAttrib(contextCapabilities);
        GL11.nglPopClientAttrib(l);
    }

    static native void nglPopClientAttrib(long var0);

    public static void glPushAttrib(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPushAttrib;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPushAttrib(n, l);
    }

    static native void nglPushAttrib(int var0, long var1);

    public static void glPopAttrib() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPopAttrib;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglPopAttrib(l);
    }

    static native void nglPopAttrib(long var0);

    public static void glStencilFunc(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glStencilFunc;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglStencilFunc(n, n2, n3, l);
    }

    static native void nglStencilFunc(int var0, int var1, int var2, long var3);

    public static void glVertexPointer(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glVertexPointer_pointer = doubleBuffer;
        }
        GL11.nglVertexPointer(n, 5130, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glVertexPointer(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glVertexPointer_pointer = floatBuffer;
        }
        GL11.nglVertexPointer(n, 5126, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glVertexPointer(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glVertexPointer_pointer = intBuffer;
        }
        GL11.nglVertexPointer(n, 5124, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glVertexPointer(int n, int n2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glVertexPointer_pointer = shortBuffer;
        }
        GL11.nglVertexPointer(n, 5122, n2, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexPointer(int var0, int var1, int var2, long var3, long var5);

    public static void glVertexPointer(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glVertexPointer;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        GL11.nglVertexPointerBO(n, n2, n3, l, l2);
    }

    static native void nglVertexPointerBO(int var0, int var1, int var2, long var3, long var5);

    public static void glVertexPointer(int n, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).GL11_glVertexPointer_pointer = byteBuffer;
        }
        GL11.nglVertexPointer(n, n2, n3, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glVertex2f(float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertex2f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglVertex2f(f, f2, l);
    }

    static native void nglVertex2f(float var0, float var1, long var2);

    public static void glVertex2d(double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertex2d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglVertex2d(d, d2, l);
    }

    static native void nglVertex2d(double var0, double var2, long var4);

    public static void glVertex2i(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertex2i;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglVertex2i(n, n2, l);
    }

    static native void nglVertex2i(int var0, int var1, long var2);

    public static void glVertex3f(float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertex3f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglVertex3f(f, f2, f3, l);
    }

    static native void nglVertex3f(float var0, float var1, float var2, long var3);

    public static void glVertex3d(double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertex3d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglVertex3d(d, d2, d3, l);
    }

    static native void nglVertex3d(double var0, double var2, double var4, long var6);

    public static void glVertex3i(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertex3i;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglVertex3i(n, n2, n3, l);
    }

    static native void nglVertex3i(int var0, int var1, int var2, long var3);

    public static void glVertex4f(float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertex4f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglVertex4f(f, f2, f3, f4, l);
    }

    static native void nglVertex4f(float var0, float var1, float var2, float var3, long var4);

    public static void glVertex4d(double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertex4d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglVertex4d(d, d2, d3, d4, l);
    }

    static native void nglVertex4d(double var0, double var2, double var4, double var6, long var8);

    public static void glVertex4i(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertex4i;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglVertex4i(n, n2, n3, n4, l);
    }

    static native void nglVertex4i(int var0, int var1, int var2, int var3, long var4);

    public static void glTranslatef(float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTranslatef;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTranslatef(f, f2, f3, l);
    }

    static native void nglTranslatef(float var0, float var1, float var2, long var3);

    public static void glTranslated(double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTranslated;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTranslated(d, d2, d3, l);
    }

    static native void nglTranslated(double var0, double var2, double var4, long var6);

    public static void glTexImage1D(int n, int n2, int n3, int n4, int n5, int n6, int n7, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        if (byteBuffer != null) {
            ByteBuffer byteBuffer2 = byteBuffer;
            BufferChecks.checkBuffer(byteBuffer2, GLChecks.calculateTexImage1DStorage(byteBuffer2, n6, n7, n4));
        }
        GL11.nglTexImage1D(n, n2, n3, n4, n5, n6, n7, MemoryUtil.getAddressSafe(byteBuffer), l);
    }

    public static void glTexImage1D(int n, int n2, int n3, int n4, int n5, int n6, int n7, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        if (doubleBuffer != null) {
            DoubleBuffer doubleBuffer2 = doubleBuffer;
            BufferChecks.checkBuffer(doubleBuffer2, GLChecks.calculateTexImage1DStorage(doubleBuffer2, n6, n7, n4));
        }
        GL11.nglTexImage1D(n, n2, n3, n4, n5, n6, n7, MemoryUtil.getAddressSafe(doubleBuffer), l);
    }

    public static void glTexImage1D(int n, int n2, int n3, int n4, int n5, int n6, int n7, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        if (floatBuffer != null) {
            FloatBuffer floatBuffer2 = floatBuffer;
            BufferChecks.checkBuffer(floatBuffer2, GLChecks.calculateTexImage1DStorage(floatBuffer2, n6, n7, n4));
        }
        GL11.nglTexImage1D(n, n2, n3, n4, n5, n6, n7, MemoryUtil.getAddressSafe(floatBuffer), l);
    }

    public static void glTexImage1D(int n, int n2, int n3, int n4, int n5, int n6, int n7, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        if (intBuffer != null) {
            IntBuffer intBuffer2 = intBuffer;
            BufferChecks.checkBuffer(intBuffer2, GLChecks.calculateTexImage1DStorage(intBuffer2, n6, n7, n4));
        }
        GL11.nglTexImage1D(n, n2, n3, n4, n5, n6, n7, MemoryUtil.getAddressSafe(intBuffer), l);
    }

    public static void glTexImage1D(int n, int n2, int n3, int n4, int n5, int n6, int n7, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        if (shortBuffer != null) {
            ShortBuffer shortBuffer2 = shortBuffer;
            BufferChecks.checkBuffer(shortBuffer2, GLChecks.calculateTexImage1DStorage(shortBuffer2, n6, n7, n4));
        }
        GL11.nglTexImage1D(n, n2, n3, n4, n5, n6, n7, MemoryUtil.getAddressSafe(shortBuffer), l);
    }

    static native void nglTexImage1D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, long var7, long var9);

    public static void glTexImage1D(int n, int n2, int n3, int n4, int n5, int n6, int n7, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glTexImage1D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL11.nglTexImage1DBO(n, n2, n3, n4, n5, n6, n7, l, l2);
    }

    static native void nglTexImage1DBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, long var7, long var9);

    public static void glTexImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        if (byteBuffer != null) {
            ByteBuffer byteBuffer2 = byteBuffer;
            BufferChecks.checkBuffer(byteBuffer2, GLChecks.calculateTexImage2DStorage(byteBuffer2, n7, n8, n4, n5));
        }
        GL11.nglTexImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddressSafe(byteBuffer), l);
    }

    public static void glTexImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        if (doubleBuffer != null) {
            DoubleBuffer doubleBuffer2 = doubleBuffer;
            BufferChecks.checkBuffer(doubleBuffer2, GLChecks.calculateTexImage2DStorage(doubleBuffer2, n7, n8, n4, n5));
        }
        GL11.nglTexImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddressSafe(doubleBuffer), l);
    }

    public static void glTexImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        if (floatBuffer != null) {
            FloatBuffer floatBuffer2 = floatBuffer;
            BufferChecks.checkBuffer(floatBuffer2, GLChecks.calculateTexImage2DStorage(floatBuffer2, n7, n8, n4, n5));
        }
        GL11.nglTexImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddressSafe(floatBuffer), l);
    }

    public static void glTexImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        if (intBuffer != null) {
            IntBuffer intBuffer2 = intBuffer;
            BufferChecks.checkBuffer(intBuffer2, GLChecks.calculateTexImage2DStorage(intBuffer2, n7, n8, n4, n5));
        }
        GL11.nglTexImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddressSafe(intBuffer), l);
    }

    public static void glTexImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        if (shortBuffer != null) {
            ShortBuffer shortBuffer2 = shortBuffer;
            BufferChecks.checkBuffer(shortBuffer2, GLChecks.calculateTexImage2DStorage(shortBuffer2, n7, n8, n4, n5));
        }
        GL11.nglTexImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddressSafe(shortBuffer), l);
    }

    static native void nglTexImage2D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glTexImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glTexImage2D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL11.nglTexImage2DBO(n, n2, n3, n4, n5, n6, n7, n8, l, l2);
    }

    static native void nglTexImage2DBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glTexSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexSubImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ByteBuffer byteBuffer2 = byteBuffer;
        BufferChecks.checkBuffer(byteBuffer2, GLChecks.calculateImageStorage(byteBuffer2, n5, n6, n4, 1, 1));
        GL11.nglTexSubImage1D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glTexSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexSubImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        DoubleBuffer doubleBuffer2 = doubleBuffer;
        BufferChecks.checkBuffer(doubleBuffer2, GLChecks.calculateImageStorage(doubleBuffer2, n5, n6, n4, 1, 1));
        GL11.nglTexSubImage1D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glTexSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexSubImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        FloatBuffer floatBuffer2 = floatBuffer;
        BufferChecks.checkBuffer(floatBuffer2, GLChecks.calculateImageStorage(floatBuffer2, n5, n6, n4, 1, 1));
        GL11.nglTexSubImage1D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glTexSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexSubImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        IntBuffer intBuffer2 = intBuffer;
        BufferChecks.checkBuffer(intBuffer2, GLChecks.calculateImageStorage(intBuffer2, n5, n6, n4, 1, 1));
        GL11.nglTexSubImage1D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glTexSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexSubImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ShortBuffer shortBuffer2 = shortBuffer;
        BufferChecks.checkBuffer(shortBuffer2, GLChecks.calculateImageStorage(shortBuffer2, n5, n6, n4, 1, 1));
        GL11.nglTexSubImage1D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglTexSubImage1D(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glTexSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glTexSubImage1D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL11.nglTexSubImage1DBO(n, n2, n3, n4, n5, n6, l, l2);
    }

    static native void nglTexSubImage1DBO(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glTexSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexSubImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ByteBuffer byteBuffer2 = byteBuffer;
        BufferChecks.checkBuffer(byteBuffer2, GLChecks.calculateImageStorage(byteBuffer2, n7, n8, n5, n6, 1));
        GL11.nglTexSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glTexSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexSubImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        DoubleBuffer doubleBuffer2 = doubleBuffer;
        BufferChecks.checkBuffer(doubleBuffer2, GLChecks.calculateImageStorage(doubleBuffer2, n7, n8, n5, n6, 1));
        GL11.nglTexSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glTexSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexSubImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        FloatBuffer floatBuffer2 = floatBuffer;
        BufferChecks.checkBuffer(floatBuffer2, GLChecks.calculateImageStorage(floatBuffer2, n7, n8, n5, n6, 1));
        GL11.nglTexSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glTexSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexSubImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        IntBuffer intBuffer2 = intBuffer;
        BufferChecks.checkBuffer(intBuffer2, GLChecks.calculateImageStorage(intBuffer2, n7, n8, n5, n6, 1));
        GL11.nglTexSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glTexSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexSubImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ShortBuffer shortBuffer2 = shortBuffer;
        BufferChecks.checkBuffer(shortBuffer2, GLChecks.calculateImageStorage(shortBuffer2, n7, n8, n5, n6, 1));
        GL11.nglTexSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglTexSubImage2D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glTexSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glTexSubImage2D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL11.nglTexSubImage2DBO(n, n2, n3, n4, n5, n6, n7, n8, l, l2);
    }

    static native void nglTexSubImage2DBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glTexParameterf(int n, int n2, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexParameterf;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexParameterf(n, n2, f, l);
    }

    static native void nglTexParameterf(int var0, int var1, float var2, long var3);

    public static void glTexParameteri(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexParameteri;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexParameteri(n, n2, n3, l);
    }

    static native void nglTexParameteri(int var0, int var1, int var2, long var3);

    public static void glTexParameter(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL11.nglTexParameterfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglTexParameterfv(int var0, int var1, long var2, long var4);

    public static void glTexParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL11.nglTexParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglTexParameteriv(int var0, int var1, long var2, long var4);

    public static void glTexGenf(int n, int n2, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexGenf;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexGenf(n, n2, f, l);
    }

    static native void nglTexGenf(int var0, int var1, float var2, long var3);

    public static void glTexGend(int n, int n2, double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexGend;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexGend(n, n2, d, l);
    }

    static native void nglTexGend(int var0, int var1, double var2, long var4);

    public static void glTexGen(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexGenfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL11.nglTexGenfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglTexGenfv(int var0, int var1, long var2, long var4);

    public static void glTexGen(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexGendv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        GL11.nglTexGendv(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglTexGendv(int var0, int var1, long var2, long var4);

    public static void glTexGeni(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexGeni;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexGeni(n, n2, n3, l);
    }

    static native void nglTexGeni(int var0, int var1, int var2, long var3);

    public static void glTexGen(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexGeniv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL11.nglTexGeniv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglTexGeniv(int var0, int var1, long var2, long var4);

    public static void glTexEnvf(int n, int n2, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexEnvf;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexEnvf(n, n2, f, l);
    }

    static native void nglTexEnvf(int var0, int var1, float var2, long var3);

    public static void glTexEnvi(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexEnvi;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexEnvi(n, n2, n3, l);
    }

    static native void nglTexEnvi(int var0, int var1, int var2, long var3);

    public static void glTexEnv(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexEnvfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL11.nglTexEnvfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglTexEnvfv(int var0, int var1, long var2, long var4);

    public static void glTexEnv(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexEnviv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL11.nglTexEnviv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglTexEnviv(int var0, int var1, long var2, long var4);

    public static void glTexCoordPointer(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoordPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glTexCoordPointer_buffer[StateTracker.getReferences((ContextCapabilities)contextCapabilities).glClientActiveTexture] = doubleBuffer;
        }
        GL11.nglTexCoordPointer(n, 5130, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glTexCoordPointer(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoordPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glTexCoordPointer_buffer[StateTracker.getReferences((ContextCapabilities)contextCapabilities).glClientActiveTexture] = floatBuffer;
        }
        GL11.nglTexCoordPointer(n, 5126, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glTexCoordPointer(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoordPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glTexCoordPointer_buffer[StateTracker.getReferences((ContextCapabilities)contextCapabilities).glClientActiveTexture] = intBuffer;
        }
        GL11.nglTexCoordPointer(n, 5124, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glTexCoordPointer(int n, int n2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoordPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glTexCoordPointer_buffer[StateTracker.getReferences((ContextCapabilities)contextCapabilities).glClientActiveTexture] = shortBuffer;
        }
        GL11.nglTexCoordPointer(n, 5122, n2, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglTexCoordPointer(int var0, int var1, int var2, long var3, long var5);

    public static void glTexCoordPointer(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glTexCoordPointer;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        GL11.nglTexCoordPointerBO(n, n2, n3, l, l2);
    }

    static native void nglTexCoordPointerBO(int var0, int var1, int var2, long var3, long var5);

    public static void glTexCoordPointer(int n, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoordPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glTexCoordPointer_buffer[StateTracker.getReferences((ContextCapabilities)contextCapabilities).glClientActiveTexture] = byteBuffer;
        }
        GL11.nglTexCoordPointer(n, n2, n3, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glTexCoord1f(float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoord1f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexCoord1f(f, l);
    }

    static native void nglTexCoord1f(float var0, long var1);

    public static void glTexCoord1d(double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoord1d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexCoord1d(d, l);
    }

    static native void nglTexCoord1d(double var0, long var2);

    public static void glTexCoord2f(float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoord2f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexCoord2f(f, f2, l);
    }

    static native void nglTexCoord2f(float var0, float var1, long var2);

    public static void glTexCoord2d(double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoord2d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexCoord2d(d, d2, l);
    }

    static native void nglTexCoord2d(double var0, double var2, long var4);

    public static void glTexCoord3f(float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoord3f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexCoord3f(f, f2, f3, l);
    }

    static native void nglTexCoord3f(float var0, float var1, float var2, long var3);

    public static void glTexCoord3d(double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoord3d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexCoord3d(d, d2, d3, l);
    }

    static native void nglTexCoord3d(double var0, double var2, double var4, long var6);

    public static void glTexCoord4f(float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoord4f;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexCoord4f(f, f2, f3, f4, l);
    }

    static native void nglTexCoord4f(float var0, float var1, float var2, float var3, long var4);

    public static void glTexCoord4d(double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexCoord4d;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglTexCoord4d(d, d2, d3, d4, l);
    }

    static native void nglTexCoord4d(double var0, double var2, double var4, double var6, long var8);

    public static void glStencilOp(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glStencilOp;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglStencilOp(n, n2, n3, l);
    }

    static native void nglStencilOp(int var0, int var1, int var2, long var3);

    public static void glStencilMask(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glStencilMask;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglStencilMask(n, l);
    }

    static native void nglStencilMask(int var0, long var1);

    public static void glViewport(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glViewport;
        BufferChecks.checkFunctionAddress(l);
        GL11.nglViewport(n, n2, n3, n4, l);
    }

    static native void nglViewport(int var0, int var1, int var2, int var3, long var4);
}
