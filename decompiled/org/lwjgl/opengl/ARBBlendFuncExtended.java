/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import org.lwjgl.opengl.GL33;

public final class ARBBlendFuncExtended {
    public static final int GL_SRC1_COLOR = 35065;
    public static final int GL_SRC1_ALPHA = 34185;
    public static final int GL_ONE_MINUS_SRC1_COLOR = 35066;
    public static final int GL_ONE_MINUS_SRC1_ALPHA = 35067;
    public static final int GL_MAX_DUAL_SOURCE_DRAW_BUFFERS = 35068;

    private ARBBlendFuncExtended() {
    }

    public static void glBindFragDataLocationIndexed(int n, int n2, int n3, ByteBuffer byteBuffer) {
        GL33.glBindFragDataLocationIndexed(n, n2, n3, byteBuffer);
    }

    public static void glBindFragDataLocationIndexed(int n, int n2, int n3, CharSequence charSequence) {
        GL33.glBindFragDataLocationIndexed(n, n2, n3, charSequence);
    }

    public static int glGetFragDataIndex(int n, ByteBuffer byteBuffer) {
        return GL33.glGetFragDataIndex(n, byteBuffer);
    }

    public static int glGetFragDataIndex(int n, CharSequence charSequence) {
        return GL33.glGetFragDataIndex(n, charSequence);
    }
}
