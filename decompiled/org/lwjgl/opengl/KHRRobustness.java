/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.opengl.GL45;

public final class KHRRobustness {
    public static final int GL_GUILTY_CONTEXT_RESET = 33363;
    public static final int GL_INNOCENT_CONTEXT_RESET = 33364;
    public static final int GL_UNKNOWN_CONTEXT_RESET = 33365;
    public static final int GL_CONTEXT_ROBUST_ACCESS = 37107;
    public static final int GL_RESET_NOTIFICATION_STRATEGY = 33366;
    public static final int GL_LOSE_CONTEXT_ON_RESET = 33362;
    public static final int GL_NO_RESET_NOTIFICATION = 33377;
    public static final int GL_CONTEXT_LOST = 1287;

    private KHRRobustness() {
    }

    public static int glGetGraphicsResetStatus() {
        return GL45.glGetGraphicsResetStatus();
    }

    public static void glReadnPixels(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer) {
        GL45.glReadnPixels(n, n2, n3, n4, n5, n6, byteBuffer);
    }

    public static void glReadnPixels(int n, int n2, int n3, int n4, int n5, int n6, DoubleBuffer doubleBuffer) {
        GL45.glReadnPixels(n, n2, n3, n4, n5, n6, doubleBuffer);
    }

    public static void glReadnPixels(int n, int n2, int n3, int n4, int n5, int n6, FloatBuffer floatBuffer) {
        GL45.glReadnPixels(n, n2, n3, n4, n5, n6, floatBuffer);
    }

    public static void glReadnPixels(int n, int n2, int n3, int n4, int n5, int n6, IntBuffer intBuffer) {
        GL45.glReadnPixels(n, n2, n3, n4, n5, n6, intBuffer);
    }

    public static void glReadnPixels(int n, int n2, int n3, int n4, int n5, int n6, ShortBuffer shortBuffer) {
        GL45.glReadnPixels(n, n2, n3, n4, n5, n6, shortBuffer);
    }

    public static void glReadnPixels(int n, int n2, int n3, int n4, int n5, int n6, int n7, long l) {
        GL45.glReadnPixels(n, n2, n3, n4, n5, n6, n7, l);
    }

    public static void glGetnUniform(int n, int n2, FloatBuffer floatBuffer) {
        GL45.glGetnUniform(n, n2, floatBuffer);
    }

    public static void glGetnUniform(int n, int n2, IntBuffer intBuffer) {
        GL45.glGetnUniform(n, n2, intBuffer);
    }

    public static void glGetnUniformu(int n, int n2, IntBuffer intBuffer) {
        GL45.glGetnUniformu(n, n2, intBuffer);
    }
}
