/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.LongBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.PointerBuffer;
import org.lwjgl.opengl.GL45;

public final class ARBDirectStateAccess {
    public static final int GL_TEXTURE_TARGET = 4102;
    public static final int GL_QUERY_TARGET = 33514;
    public static final int GL_TEXTURE_BINDING = 33515;

    private ARBDirectStateAccess() {
    }

    public static void glCreateTransformFeedbacks(IntBuffer intBuffer) {
        GL45.glCreateTransformFeedbacks(intBuffer);
    }

    public static int glCreateTransformFeedbacks() {
        return GL45.glCreateTransformFeedbacks();
    }

    public static void glTransformFeedbackBufferBase(int n, int n2, int n3) {
        GL45.glTransformFeedbackBufferBase(n, n2, n3);
    }

    public static void glTransformFeedbackBufferRange(int n, int n2, int n3, long l, long l2) {
        GL45.glTransformFeedbackBufferRange(n, n2, n3, l, l2);
    }

    public static void glGetTransformFeedback(int n, int n2, IntBuffer intBuffer) {
        GL45.glGetTransformFeedback(n, n2, intBuffer);
    }

    public static int glGetTransformFeedbacki(int n, int n2) {
        return GL45.glGetTransformFeedbacki(n, n2);
    }

    public static void glGetTransformFeedback(int n, int n2, int n3, IntBuffer intBuffer) {
        GL45.glGetTransformFeedback(n, n2, n3, intBuffer);
    }

    public static int glGetTransformFeedbacki(int n, int n2, int n3) {
        return GL45.glGetTransformFeedbacki(n, n2, n3);
    }

    public static void glGetTransformFeedback(int n, int n2, int n3, LongBuffer longBuffer) {
        GL45.glGetTransformFeedback(n, n2, n3, longBuffer);
    }

    public static long glGetTransformFeedbacki64(int n, int n2, int n3) {
        return GL45.glGetTransformFeedbacki64(n, n2, n3);
    }

    public static void glCreateBuffers(IntBuffer intBuffer) {
        GL45.glCreateBuffers(intBuffer);
    }

    public static int glCreateBuffers() {
        return GL45.glCreateBuffers();
    }

    public static void glNamedBufferStorage(int n, ByteBuffer byteBuffer, int n2) {
        GL45.glNamedBufferStorage(n, byteBuffer, n2);
    }

    public static void glNamedBufferStorage(int n, DoubleBuffer doubleBuffer, int n2) {
        GL45.glNamedBufferStorage(n, doubleBuffer, n2);
    }

    public static void glNamedBufferStorage(int n, FloatBuffer floatBuffer, int n2) {
        GL45.glNamedBufferStorage(n, floatBuffer, n2);
    }

    public static void glNamedBufferStorage(int n, IntBuffer intBuffer, int n2) {
        GL45.glNamedBufferStorage(n, intBuffer, n2);
    }

    public static void glNamedBufferStorage(int n, ShortBuffer shortBuffer, int n2) {
        GL45.glNamedBufferStorage(n, shortBuffer, n2);
    }

    public static void glNamedBufferStorage(int n, LongBuffer longBuffer, int n2) {
        GL45.glNamedBufferStorage(n, longBuffer, n2);
    }

    public static void glNamedBufferStorage(int n, long l, int n2) {
        GL45.glNamedBufferStorage(n, l, n2);
    }

    public static void glNamedBufferData(int n, long l, int n2) {
        GL45.glNamedBufferData(n, l, n2);
    }

    public static void glNamedBufferData(int n, ByteBuffer byteBuffer, int n2) {
        GL45.glNamedBufferData(n, byteBuffer, n2);
    }

    public static void glNamedBufferData(int n, DoubleBuffer doubleBuffer, int n2) {
        GL45.glNamedBufferData(n, doubleBuffer, n2);
    }

    public static void glNamedBufferData(int n, FloatBuffer floatBuffer, int n2) {
        GL45.glNamedBufferData(n, floatBuffer, n2);
    }

    public static void glNamedBufferData(int n, IntBuffer intBuffer, int n2) {
        GL45.glNamedBufferData(n, intBuffer, n2);
    }

    public static void glNamedBufferData(int n, ShortBuffer shortBuffer, int n2) {
        GL45.glNamedBufferData(n, shortBuffer, n2);
    }

    public static void glNamedBufferSubData(int n, long l, ByteBuffer byteBuffer) {
        GL45.glNamedBufferSubData(n, l, byteBuffer);
    }

    public static void glNamedBufferSubData(int n, long l, DoubleBuffer doubleBuffer) {
        GL45.glNamedBufferSubData(n, l, doubleBuffer);
    }

    public static void glNamedBufferSubData(int n, long l, FloatBuffer floatBuffer) {
        GL45.glNamedBufferSubData(n, l, floatBuffer);
    }

    public static void glNamedBufferSubData(int n, long l, IntBuffer intBuffer) {
        GL45.glNamedBufferSubData(n, l, intBuffer);
    }

    public static void glNamedBufferSubData(int n, long l, ShortBuffer shortBuffer) {
        GL45.glNamedBufferSubData(n, l, shortBuffer);
    }

    public static void glCopyNamedBufferSubData(int n, int n2, long l, long l2, long l3) {
        GL45.glCopyNamedBufferSubData(n, n2, l, l2, l3);
    }

    public static void glClearNamedBufferData(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        GL45.glClearNamedBufferData(n, n2, n3, n4, byteBuffer);
    }

    public static void glClearNamedBufferSubData(int n, int n2, long l, long l2, int n3, int n4, ByteBuffer byteBuffer) {
        GL45.glClearNamedBufferSubData(n, n2, l, l2, n3, n4, byteBuffer);
    }

    public static ByteBuffer glMapNamedBuffer(int n, int n2, ByteBuffer byteBuffer) {
        return GL45.glMapNamedBuffer(n, n2, byteBuffer);
    }

    public static ByteBuffer glMapNamedBuffer(int n, int n2, long l, ByteBuffer byteBuffer) {
        return GL45.glMapNamedBuffer(n, n2, l, byteBuffer);
    }

    public static ByteBuffer glMapNamedBufferRange(int n, long l, long l2, int n2, ByteBuffer byteBuffer) {
        return GL45.glMapNamedBufferRange(n, l, l2, n2, byteBuffer);
    }

    public static boolean glUnmapNamedBuffer(int n) {
        return GL45.glUnmapNamedBuffer(n);
    }

    public static void glFlushMappedNamedBufferRange(int n, long l, long l2) {
        GL45.glFlushMappedNamedBufferRange(n, l, l2);
    }

    public static void glGetNamedBufferParameter(int n, int n2, IntBuffer intBuffer) {
        GL45.glGetNamedBufferParameter(n, n2, intBuffer);
    }

    public static int glGetNamedBufferParameteri(int n, int n2) {
        return GL45.glGetNamedBufferParameteri(n, n2);
    }

    public static void glGetNamedBufferParameter(int n, int n2, LongBuffer longBuffer) {
        GL45.glGetNamedBufferParameter(n, n2, longBuffer);
    }

    public static long glGetNamedBufferParameteri64(int n, int n2) {
        return GL45.glGetNamedBufferParameteri64(n, n2);
    }

    public static ByteBuffer glGetNamedBufferPointer(int n, int n2) {
        return GL45.glGetNamedBufferPointer(n, n2);
    }

    public static void glGetNamedBufferSubData(int n, long l, ByteBuffer byteBuffer) {
        GL45.glGetNamedBufferSubData(n, l, byteBuffer);
    }

    public static void glGetNamedBufferSubData(int n, long l, DoubleBuffer doubleBuffer) {
        GL45.glGetNamedBufferSubData(n, l, doubleBuffer);
    }

    public static void glGetNamedBufferSubData(int n, long l, FloatBuffer floatBuffer) {
        GL45.glGetNamedBufferSubData(n, l, floatBuffer);
    }

    public static void glGetNamedBufferSubData(int n, long l, IntBuffer intBuffer) {
        GL45.glGetNamedBufferSubData(n, l, intBuffer);
    }

    public static void glGetNamedBufferSubData(int n, long l, ShortBuffer shortBuffer) {
        GL45.glGetNamedBufferSubData(n, l, shortBuffer);
    }

    public static void glCreateFramebuffers(IntBuffer intBuffer) {
        GL45.glCreateFramebuffers(intBuffer);
    }

    public static int glCreateFramebuffers() {
        return GL45.glCreateFramebuffers();
    }

    public static void glNamedFramebufferRenderbuffer(int n, int n2, int n3, int n4) {
        GL45.glNamedFramebufferRenderbuffer(n, n2, n3, n4);
    }

    public static void glNamedFramebufferParameteri(int n, int n2, int n3) {
        GL45.glNamedFramebufferParameteri(n, n2, n3);
    }

    public static void glNamedFramebufferTexture(int n, int n2, int n3, int n4) {
        GL45.glNamedFramebufferTexture(n, n2, n3, n4);
    }

    public static void glNamedFramebufferTextureLayer(int n, int n2, int n3, int n4, int n5) {
        GL45.glNamedFramebufferTextureLayer(n, n2, n3, n4, n5);
    }

    public static void glNamedFramebufferDrawBuffer(int n, int n2) {
        GL45.glNamedFramebufferDrawBuffer(n, n2);
    }

    public static void glNamedFramebufferDrawBuffers(int n, IntBuffer intBuffer) {
        GL45.glNamedFramebufferDrawBuffers(n, intBuffer);
    }

    public static void glNamedFramebufferReadBuffer(int n, int n2) {
        GL45.glNamedFramebufferReadBuffer(n, n2);
    }

    public static void glInvalidateNamedFramebufferData(int n, IntBuffer intBuffer) {
        GL45.glInvalidateNamedFramebufferData(n, intBuffer);
    }

    public static void glInvalidateNamedFramebufferSubData(int n, IntBuffer intBuffer, int n2, int n3, int n4, int n5) {
        GL45.glInvalidateNamedFramebufferSubData(n, intBuffer, n2, n3, n4, n5);
    }

    public static void glClearNamedFramebuffer(int n, int n2, int n3, IntBuffer intBuffer) {
        GL45.glClearNamedFramebuffer(n, n2, n3, intBuffer);
    }

    public static void glClearNamedFramebufferu(int n, int n2, int n3, IntBuffer intBuffer) {
        GL45.glClearNamedFramebufferu(n, n2, n3, intBuffer);
    }

    public static void glClearNamedFramebuffer(int n, int n2, int n3, FloatBuffer floatBuffer) {
        GL45.glClearNamedFramebuffer(n, n2, n3, floatBuffer);
    }

    public static void glClearNamedFramebufferfi(int n, int n2, float f, int n3) {
        GL45.glClearNamedFramebufferfi(n, n2, f, n3);
    }

    public static void glBlitNamedFramebuffer(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, int n11, int n12) {
        GL45.glBlitNamedFramebuffer(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, n11, n12);
    }

    public static int glCheckNamedFramebufferStatus(int n, int n2) {
        return GL45.glCheckNamedFramebufferStatus(n, n2);
    }

    public static void glGetNamedFramebufferParameter(int n, int n2, IntBuffer intBuffer) {
        GL45.glGetNamedFramebufferParameter(n, n2, intBuffer);
    }

    public static int glGetNamedFramebufferParameter(int n, int n2) {
        return GL45.glGetNamedFramebufferParameter(n, n2);
    }

    public static void glGetNamedFramebufferAttachmentParameter(int n, int n2, int n3, IntBuffer intBuffer) {
        GL45.glGetNamedFramebufferAttachmentParameter(n, n2, n3, intBuffer);
    }

    public static int glGetNamedFramebufferAttachmentParameter(int n, int n2, int n3) {
        return GL45.glGetNamedFramebufferAttachmentParameter(n, n2, n3);
    }

    public static void glCreateRenderbuffers(IntBuffer intBuffer) {
        GL45.glCreateRenderbuffers(intBuffer);
    }

    public static int glCreateRenderbuffers() {
        return GL45.glCreateRenderbuffers();
    }

    public static void glNamedRenderbufferStorage(int n, int n2, int n3, int n4) {
        GL45.glNamedRenderbufferStorage(n, n2, n3, n4);
    }

    public static void glNamedRenderbufferStorageMultisample(int n, int n2, int n3, int n4, int n5) {
        GL45.glNamedRenderbufferStorageMultisample(n, n2, n3, n4, n5);
    }

    public static void glGetNamedRenderbufferParameter(int n, int n2, IntBuffer intBuffer) {
        GL45.glGetNamedRenderbufferParameter(n, n2, intBuffer);
    }

    public static int glGetNamedRenderbufferParameter(int n, int n2) {
        return GL45.glGetNamedRenderbufferParameter(n, n2);
    }

    public static void glCreateTextures(int n, IntBuffer intBuffer) {
        GL45.glCreateTextures(n, intBuffer);
    }

    public static int glCreateTextures(int n) {
        return GL45.glCreateTextures(n);
    }

    public static void glTextureBuffer(int n, int n2, int n3) {
        GL45.glTextureBuffer(n, n2, n3);
    }

    public static void glTextureBufferRange(int n, int n2, int n3, long l, long l2) {
        GL45.glTextureBufferRange(n, n2, n3, l, l2);
    }

    public static void glTextureStorage1D(int n, int n2, int n3, int n4) {
        GL45.glTextureStorage1D(n, n2, n3, n4);
    }

    public static void glTextureStorage2D(int n, int n2, int n3, int n4, int n5) {
        GL45.glTextureStorage2D(n, n2, n3, n4, n5);
    }

    public static void glTextureStorage3D(int n, int n2, int n3, int n4, int n5, int n6) {
        GL45.glTextureStorage3D(n, n2, n3, n4, n5, n6);
    }

    public static void glTextureStorage2DMultisample(int n, int n2, int n3, int n4, int n5, boolean bl) {
        GL45.glTextureStorage2DMultisample(n, n2, n3, n4, n5, bl);
    }

    public static void glTextureStorage3DMultisample(int n, int n2, int n3, int n4, int n5, int n6, boolean bl) {
        GL45.glTextureStorage3DMultisample(n, n2, n3, n4, n5, n6, bl);
    }

    public static void glTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer) {
        GL45.glTextureSubImage1D(n, n2, n3, n4, n5, n6, byteBuffer);
    }

    public static void glTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, DoubleBuffer doubleBuffer) {
        GL45.glTextureSubImage1D(n, n2, n3, n4, n5, n6, doubleBuffer);
    }

    public static void glTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, FloatBuffer floatBuffer) {
        GL45.glTextureSubImage1D(n, n2, n3, n4, n5, n6, floatBuffer);
    }

    public static void glTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, IntBuffer intBuffer) {
        GL45.glTextureSubImage1D(n, n2, n3, n4, n5, n6, intBuffer);
    }

    public static void glTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, ShortBuffer shortBuffer) {
        GL45.glTextureSubImage1D(n, n2, n3, n4, n5, n6, shortBuffer);
    }

    public static void glTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, long l) {
        GL45.glTextureSubImage1D(n, n2, n3, n4, n5, n6, l);
    }

    public static void glTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, ByteBuffer byteBuffer) {
        GL45.glTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, byteBuffer);
    }

    public static void glTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, DoubleBuffer doubleBuffer) {
        GL45.glTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, doubleBuffer);
    }

    public static void glTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, FloatBuffer floatBuffer) {
        GL45.glTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, floatBuffer);
    }

    public static void glTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, IntBuffer intBuffer) {
        GL45.glTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, intBuffer);
    }

    public static void glTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, ShortBuffer shortBuffer) {
        GL45.glTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, shortBuffer);
    }

    public static void glTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, long l) {
        GL45.glTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, l);
    }

    public static void glTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, ByteBuffer byteBuffer) {
        GL45.glTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, byteBuffer);
    }

    public static void glTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, DoubleBuffer doubleBuffer) {
        GL45.glTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, doubleBuffer);
    }

    public static void glTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, FloatBuffer floatBuffer) {
        GL45.glTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, floatBuffer);
    }

    public static void glTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, IntBuffer intBuffer) {
        GL45.glTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, intBuffer);
    }

    public static void glTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, ShortBuffer shortBuffer) {
        GL45.glTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, shortBuffer);
    }

    public static void glTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, long l) {
        GL45.glTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, l);
    }

    public static void glCompressedTextureSubImage1D(int n, int n2, int n3, int n4, int n5, ByteBuffer byteBuffer) {
        GL45.glCompressedTextureSubImage1D(n, n2, n3, n4, n5, byteBuffer);
    }

    public static void glCompressedTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, long l) {
        GL45.glCompressedTextureSubImage1D(n, n2, n3, n4, n5, n6, l);
    }

    public static void glCompressedTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, ByteBuffer byteBuffer) {
        GL45.glCompressedTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, byteBuffer);
    }

    public static void glCompressedTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, long l) {
        GL45.glCompressedTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, l);
    }

    public static void glCompressedTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, ByteBuffer byteBuffer) {
        GL45.glCompressedTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, byteBuffer);
    }

    public static void glCompressedTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, long l) {
        GL45.glCompressedTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, l);
    }

    public static void glCopyTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6) {
        GL45.glCopyTextureSubImage1D(n, n2, n3, n4, n5, n6);
    }

    public static void glCopyTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8) {
        GL45.glCopyTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, n8);
    }

    public static void glCopyTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9) {
        GL45.glCopyTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9);
    }

    public static void glTextureParameterf(int n, int n2, float f) {
        GL45.glTextureParameterf(n, n2, f);
    }

    public static void glTextureParameter(int n, int n2, FloatBuffer floatBuffer) {
        GL45.glTextureParameter(n, n2, floatBuffer);
    }

    public static void glTextureParameteri(int n, int n2, int n3) {
        GL45.glTextureParameteri(n, n2, n3);
    }

    public static void glTextureParameterI(int n, int n2, IntBuffer intBuffer) {
        GL45.glTextureParameterI(n, n2, intBuffer);
    }

    public static void glTextureParameterIu(int n, int n2, IntBuffer intBuffer) {
        GL45.glTextureParameterIu(n, n2, intBuffer);
    }

    public static void glTextureParameter(int n, int n2, IntBuffer intBuffer) {
        GL45.glTextureParameter(n, n2, intBuffer);
    }

    public static void glGenerateTextureMipmap(int n) {
        GL45.glGenerateTextureMipmap(n);
    }

    public static void glBindTextureUnit(int n, int n2) {
        GL45.glBindTextureUnit(n, n2);
    }

    public static void glGetTextureImage(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        GL45.glGetTextureImage(n, n2, n3, n4, byteBuffer);
    }

    public static void glGetTextureImage(int n, int n2, int n3, int n4, DoubleBuffer doubleBuffer) {
        GL45.glGetTextureImage(n, n2, n3, n4, doubleBuffer);
    }

    public static void glGetTextureImage(int n, int n2, int n3, int n4, FloatBuffer floatBuffer) {
        GL45.glGetTextureImage(n, n2, n3, n4, floatBuffer);
    }

    public static void glGetTextureImage(int n, int n2, int n3, int n4, IntBuffer intBuffer) {
        GL45.glGetTextureImage(n, n2, n3, n4, intBuffer);
    }

    public static void glGetTextureImage(int n, int n2, int n3, int n4, ShortBuffer shortBuffer) {
        GL45.glGetTextureImage(n, n2, n3, n4, shortBuffer);
    }

    public static void glGetTextureImage(int n, int n2, int n3, int n4, int n5, long l) {
        GL45.glGetTextureImage(n, n2, n3, n4, n5, l);
    }

    public static void glGetCompressedTextureImage(int n, int n2, ByteBuffer byteBuffer) {
        GL45.glGetCompressedTextureImage(n, n2, byteBuffer);
    }

    public static void glGetCompressedTextureImage(int n, int n2, IntBuffer intBuffer) {
        GL45.glGetCompressedTextureImage(n, n2, intBuffer);
    }

    public static void glGetCompressedTextureImage(int n, int n2, ShortBuffer shortBuffer) {
        GL45.glGetCompressedTextureImage(n, n2, shortBuffer);
    }

    public static void glGetCompressedTextureImage(int n, int n2, int n3, long l) {
        GL45.glGetCompressedTextureImage(n, n2, n3, l);
    }

    public static void glGetTextureLevelParameter(int n, int n2, int n3, FloatBuffer floatBuffer) {
        GL45.glGetTextureLevelParameter(n, n2, n3, floatBuffer);
    }

    public static float glGetTextureLevelParameterf(int n, int n2, int n3) {
        return GL45.glGetTextureLevelParameterf(n, n2, n3);
    }

    public static void glGetTextureLevelParameter(int n, int n2, int n3, IntBuffer intBuffer) {
        GL45.glGetTextureLevelParameter(n, n2, n3, intBuffer);
    }

    public static int glGetTextureLevelParameteri(int n, int n2, int n3) {
        return GL45.glGetTextureLevelParameteri(n, n2, n3);
    }

    public static void glGetTextureParameter(int n, int n2, FloatBuffer floatBuffer) {
        GL45.glGetTextureParameter(n, n2, floatBuffer);
    }

    public static float glGetTextureParameterf(int n, int n2) {
        return GL45.glGetTextureParameterf(n, n2);
    }

    public static void glGetTextureParameterI(int n, int n2, IntBuffer intBuffer) {
        GL45.glGetTextureParameterI(n, n2, intBuffer);
    }

    public static int glGetTextureParameterIi(int n, int n2) {
        return GL45.glGetTextureParameterIi(n, n2);
    }

    public static void glGetTextureParameterIu(int n, int n2, IntBuffer intBuffer) {
        GL45.glGetTextureParameterIu(n, n2, intBuffer);
    }

    public static int glGetTextureParameterIui(int n, int n2) {
        return GL45.glGetTextureParameterIui(n, n2);
    }

    public static void glGetTextureParameter(int n, int n2, IntBuffer intBuffer) {
        GL45.glGetTextureParameter(n, n2, intBuffer);
    }

    public static int glGetTextureParameteri(int n, int n2) {
        return GL45.glGetTextureParameteri(n, n2);
    }

    public static void glCreateVertexArrays(IntBuffer intBuffer) {
        GL45.glCreateVertexArrays(intBuffer);
    }

    public static int glCreateVertexArrays() {
        return GL45.glCreateVertexArrays();
    }

    public static void glDisableVertexArrayAttrib(int n, int n2) {
        GL45.glDisableVertexArrayAttrib(n, n2);
    }

    public static void glEnableVertexArrayAttrib(int n, int n2) {
        GL45.glEnableVertexArrayAttrib(n, n2);
    }

    public static void glVertexArrayElementBuffer(int n, int n2) {
        GL45.glVertexArrayElementBuffer(n, n2);
    }

    public static void glVertexArrayVertexBuffer(int n, int n2, int n3, long l, int n4) {
        GL45.glVertexArrayVertexBuffer(n, n2, n3, l, n4);
    }

    public static void glVertexArrayVertexBuffers(int n, int n2, int n3, IntBuffer intBuffer, PointerBuffer pointerBuffer, IntBuffer intBuffer2) {
        GL45.glVertexArrayVertexBuffers(n, n2, n3, intBuffer, pointerBuffer, intBuffer2);
    }

    public static void glVertexArrayAttribFormat(int n, int n2, int n3, int n4, boolean bl, int n5) {
        GL45.glVertexArrayAttribFormat(n, n2, n3, n4, bl, n5);
    }

    public static void glVertexArrayAttribIFormat(int n, int n2, int n3, int n4, int n5) {
        GL45.glVertexArrayAttribIFormat(n, n2, n3, n4, n5);
    }

    public static void glVertexArrayAttribLFormat(int n, int n2, int n3, int n4, int n5) {
        GL45.glVertexArrayAttribLFormat(n, n2, n3, n4, n5);
    }

    public static void glVertexArrayAttribBinding(int n, int n2, int n3) {
        GL45.glVertexArrayAttribBinding(n, n2, n3);
    }

    public static void glVertexArrayBindingDivisor(int n, int n2, int n3) {
        GL45.glVertexArrayBindingDivisor(n, n2, n3);
    }

    public static void glGetVertexArray(int n, int n2, IntBuffer intBuffer) {
        GL45.glGetVertexArray(n, n2, intBuffer);
    }

    public static int glGetVertexArray(int n, int n2) {
        return GL45.glGetVertexArray(n, n2);
    }

    public static void glGetVertexArrayIndexed(int n, int n2, int n3, IntBuffer intBuffer) {
        GL45.glGetVertexArrayIndexed(n, n2, n3, intBuffer);
    }

    public static int glGetVertexArrayIndexed(int n, int n2, int n3) {
        return GL45.glGetVertexArrayIndexed(n, n2, n3);
    }

    public static void glGetVertexArrayIndexed64i(int n, int n2, int n3, LongBuffer longBuffer) {
        GL45.glGetVertexArrayIndexed64i(n, n2, n3, longBuffer);
    }

    public static long glGetVertexArrayIndexed64i(int n, int n2, int n3) {
        return GL45.glGetVertexArrayIndexed64i(n, n2, n3);
    }

    public static void glCreateSamplers(IntBuffer intBuffer) {
        GL45.glCreateSamplers(intBuffer);
    }

    public static int glCreateSamplers() {
        return GL45.glCreateSamplers();
    }

    public static void glCreateProgramPipelines(IntBuffer intBuffer) {
        GL45.glCreateProgramPipelines(intBuffer);
    }

    public static int glCreateProgramPipelines() {
        return GL45.glCreateProgramPipelines();
    }

    public static void glCreateQueries(int n, IntBuffer intBuffer) {
        GL45.glCreateQueries(n, intBuffer);
    }

    public static int glCreateQueries(int n) {
        return GL45.glCreateQueries(n);
    }
}
