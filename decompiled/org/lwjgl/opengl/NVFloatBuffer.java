/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

public final class NVFloatBuffer {
    public static final int GL_FLOAT_R_NV = 34944;
    public static final int GL_FLOAT_RG_NV = 34945;
    public static final int GL_FLOAT_RGB_NV = 34946;
    public static final int GL_FLOAT_RGBA_NV = 34947;
    public static final int GL_FLOAT_R16_NV = 34948;
    public static final int GL_FLOAT_R32_NV = 34949;
    public static final int GL_FLOAT_RG16_NV = 34950;
    public static final int GL_FLOAT_RG32_NV = 34951;
    public static final int GL_FLOAT_RGB16_NV = 34952;
    public static final int GL_FLOAT_RGB32_NV = 34953;
    public static final int GL_FLOAT_RGBA16_NV = 34954;
    public static final int GL_FLOAT_RGBA32_NV = 34955;
    public static final int GL_TEXTURE_FLOAT_COMPONENTS_NV = 34956;
    public static final int GL_FLOAT_CLEAR_COLOR_VALUE_NV = 34957;
    public static final int GL_FLOAT_RGBA_MODE_NV = 34958;

    private NVFloatBuffer() {
    }
}
