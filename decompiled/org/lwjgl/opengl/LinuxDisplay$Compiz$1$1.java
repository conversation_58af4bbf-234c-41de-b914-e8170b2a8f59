/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.util.List;
import org.lwjgl.LWJGLException;
import org.lwjgl.opengl.LinuxDisplay;

class LinuxDisplay.Compiz.1
implements LinuxDisplay.Compiz.Provider {
    private static final String KEY = "/org/freedesktop/compiz/workarounds/allscreens/legacy_fullscreen";

    LinuxDisplay.Compiz.1() {
    }

    public boolean hasLegacyFullscreenSupport() {
        List list = LinuxDisplay.Compiz.run(new String[]{"dbus-send", "--print-reply", "--type=method_call", "--dest=org.freedesktop.compiz", KEY, "org.freedesktop.compiz.get"});
        if (list == null || list.size() < 2) {
            throw new LWJGLException("Invalid Dbus reply.");
        }
        String string = (String)list.get(0);
        if (!string.startsWith("method return")) {
            throw new LWJGLException("Invalid Dbus reply.");
        }
        string = ((String)list.get(1)).trim();
        if (!string.startsWith("boolean") || string.length() < 12) {
            throw new LWJGLException("Invalid Dbus reply.");
        }
        return "true".equalsIgnoreCase(string.substring(7 + 1));
    }

    public void setLegacyFullscreenSupport(boolean bl) {
        if (LinuxDisplay.Compiz.run(new String[]{"dbus-send", "--type=method_call", "--dest=org.freedesktop.compiz", KEY, "org.freedesktop.compiz.set", "boolean:" + Boolean.toString(bl)}) == null) {
            throw new LWJGLException("Failed to apply Compiz LFS workaround.");
        }
    }
}
