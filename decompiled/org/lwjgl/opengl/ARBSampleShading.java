/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ARBSampleShading {
    public static final int GL_SAMPLE_SHADING_ARB = 35894;
    public static final int GL_MIN_SAMPLE_SHADING_VALUE_ARB = 35895;

    private ARBSampleShading() {
    }

    public static void glMinSampleShadingARB(float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMinSampleShadingARB;
        BufferChecks.checkFunctionAddress(l);
        ARBSampleShading.nglMinSampleShadingARB(f, l);
    }

    static native void nglMinSampleShadingARB(float var0, long var1);
}
