/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.security.PrivilegedAction;
import org.lwjgl.LWJGLUtil;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
static final class WindowsCanvasImplementation.1
implements PrivilegedAction<Object> {
    WindowsCanvasImplementation.1() {
    }

    @Override
    public final Object run() {
        try {
            System.loadLibrary("jawt");
        }
        catch (UnsatisfiedLinkError unsatisfiedLinkError) {
            LWJGLUtil.log("Failed to load jawt: " + unsatisfiedLinkError.getMessage());
        }
        return null;
    }
}
