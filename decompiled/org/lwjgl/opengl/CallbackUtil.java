/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.util.HashMap;
import java.util.Map;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
final class CallbackUtil {
    private static final Map<ContextCapabilities, Long> contextUserParamsARB = new HashMap<ContextCapabilities, Long>();
    private static final Map<ContextCapabilities, Long> contextUserParamsAMD = new HashMap<ContextCapabilities, Long>();
    private static final Map<ContextCapabilities, Long> contextUserParamsKHR = new HashMap<ContextCapabilities, Long>();

    private CallbackUtil() {
    }

    static long createGlobalRef(Object object) {
        if (object == null) {
            return 0L;
        }
        return CallbackUtil.ncreateGlobalRef(object);
    }

    private static native long ncreateGlobalRef(Object var0);

    private static native void deleteGlobalRef(long var0);

    private static void registerContextCallback(long l, Map<ContextCapabilities, Long> map) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        if (contextCapabilities == null) {
            CallbackUtil.deleteGlobalRef(l);
            throw new IllegalStateException("No context is current.");
        }
        Long l2 = map.remove(contextCapabilities);
        if (l2 != null) {
            CallbackUtil.deleteGlobalRef(l2);
        }
        if (l != 0L) {
            map.put(contextCapabilities, l);
        }
    }

    static void unregisterCallbacks(Object object) {
        Long l = contextUserParamsARB.remove(object = GLContext.getCapabilities(object));
        if (l != null) {
            CallbackUtil.deleteGlobalRef(l);
        }
        if ((l = contextUserParamsAMD.remove(object)) != null) {
            CallbackUtil.deleteGlobalRef(l);
        }
        if ((l = contextUserParamsKHR.remove(object)) != null) {
            CallbackUtil.deleteGlobalRef(l);
        }
    }

    static native long getDebugOutputCallbackARB();

    static void registerContextCallbackARB(long l) {
        CallbackUtil.registerContextCallback(l, contextUserParamsARB);
    }

    static native long getDebugOutputCallbackAMD();

    static void registerContextCallbackAMD(long l) {
        CallbackUtil.registerContextCallback(l, contextUserParamsAMD);
    }

    static native long getDebugCallbackKHR();

    static void registerContextCallbackKHR(long l) {
        CallbackUtil.registerContextCallback(l, contextUserParamsKHR);
    }
}
