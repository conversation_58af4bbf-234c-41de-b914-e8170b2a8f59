/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

public final class NVTextureShader {
    public static final int GL_TEXTURE_SHADER_NV = 34526;
    public static final int GL_RGBA_UNSIGNED_DOT_PRODUCT_MAPPING_NV = 34521;
    public static final int GL_SHADER_OPERATION_NV = 34527;
    public static final int GL_OFFSET_TEXTURE_SCALE_NV = 34530;
    public static final int GL_OFFSET_TEXTURE_BIAS_NV = 34531;
    public static final int GL_OFFSET_TEXTURE_2D_SCALE_NV = 34530;
    public static final int GL_OFFSET_TEXTURE_2D_BIAS_NV = 34531;
    public static final int GL_PREVIOUS_TEXTURE_INPUT_NV = 34532;
    public static final int GL_CULL_MODES_NV = 34528;
    public static final int GL_OFFSET_TEXTURE_MATRIX_NV = 34529;
    public static final int GL_OFFSET_TEXTURE_2D_MATRIX_NV = 34529;
    public static final int GL_CONST_EYE_NV = 34533;
    public static final int GL_SHADER_CONSISTENT_NV = 34525;
    public static final int GL_PASS_THROUGH_NV = 34534;
    public static final int GL_CULL_FRAGMENT_NV = 34535;
    public static final int GL_OFFSET_TEXTURE_2D_NV = 34536;
    public static final int GL_OFFSET_TEXTURE_RECTANGLE_NV = 34380;
    public static final int GL_OFFSET_TEXTURE_RECTANGLE_SCALE_NV = 34381;
    public static final int GL_DEPENDENT_AR_TEXTURE_2D_NV = 34537;
    public static final int GL_DEPENDENT_GB_TEXTURE_2D_NV = 34538;
    public static final int GL_DOT_PRODUCT_NV = 34540;
    public static final int GL_DOT_PRODUCT_DEPTH_REPLACE_NV = 34541;
    public static final int GL_DOT_PRODUCT_TEXTURE_2D_NV = 34542;
    public static final int GL_DOT_PRODUCT_TEXTURE_RECTANGLE_NV = 34382;
    public static final int GL_DOT_PRODUCT_TEXTURE_CUBE_MAP_NV = 34544;
    public static final int GL_DOT_PRODUCT_DIFFUSE_CUBE_MAP_NV = 34545;
    public static final int GL_DOT_PRODUCT_REFLECT_CUBE_MAP_NV = 34546;
    public static final int GL_DOT_PRODUCT_CONST_EYE_REFLECT_CUBE_MAP_NV = 34547;
    public static final int GL_HILO_NV = 34548;
    public static final int GL_DSDT_NV = 34549;
    public static final int GL_DSDT_MAG_NV = 34550;
    public static final int GL_DSDT_MAG_VIB_NV = 34551;
    public static final int GL_UNSIGNED_INT_S8_S8_8_8_NV = 34522;
    public static final int GL_UNSIGNED_INT_8_8_S8_S8_REV_NV = 34523;
    public static final int GL_SIGNED_RGBA_NV = 34555;
    public static final int GL_SIGNED_RGBA8_NV = 34556;
    public static final int GL_SIGNED_RGB_NV = 34558;
    public static final int GL_SIGNED_RGB8_NV = 34559;
    public static final int GL_SIGNED_LUMINANCE_NV = 34561;
    public static final int GL_SIGNED_LUMINANCE8_NV = 34562;
    public static final int GL_SIGNED_LUMINANCE_ALPHA_NV = 34563;
    public static final int GL_SIGNED_LUMINANCE8_ALPHA8_NV = 34564;
    public static final int GL_SIGNED_ALPHA_NV = 34565;
    public static final int GL_SIGNED_ALPHA8_NV = 34566;
    public static final int GL_SIGNED_INTENSITY_NV = 34567;
    public static final int GL_SIGNED_INTENSITY8_NV = 34568;
    public static final int GL_SIGNED_RGB_UNSIGNED_ALPHA_NV = 34572;
    public static final int GL_SIGNED_RGB8_UNSIGNED_ALPHA8_NV = 34573;
    public static final int GL_HILO16_NV = 34552;
    public static final int GL_SIGNED_HILO_NV = 34553;
    public static final int GL_SIGNED_HILO16_NV = 34554;
    public static final int GL_DSDT8_NV = 34569;
    public static final int GL_DSDT8_MAG8_NV = 34570;
    public static final int GL_DSDT_MAG_INTENSITY_NV = 34524;
    public static final int GL_DSDT8_MAG8_INTENSITY8_NV = 34571;
    public static final int GL_HI_SCALE_NV = 34574;
    public static final int GL_LO_SCALE_NV = 34575;
    public static final int GL_DS_SCALE_NV = 34576;
    public static final int GL_DT_SCALE_NV = 34577;
    public static final int GL_MAGNITUDE_SCALE_NV = 34578;
    public static final int GL_VIBRANCE_SCALE_NV = 34579;
    public static final int GL_HI_BIAS_NV = 34580;
    public static final int GL_LO_BIAS_NV = 34581;
    public static final int GL_DS_BIAS_NV = 34582;
    public static final int GL_DT_BIAS_NV = 34583;
    public static final int GL_MAGNITUDE_BIAS_NV = 34584;
    public static final int GL_VIBRANCE_BIAS_NV = 34585;
    public static final int GL_TEXTURE_BORDER_VALUES_NV = 34586;
    public static final int GL_TEXTURE_HI_SIZE_NV = 34587;
    public static final int GL_TEXTURE_LO_SIZE_NV = 34588;
    public static final int GL_TEXTURE_DS_SIZE_NV = 34589;
    public static final int GL_TEXTURE_DT_SIZE_NV = 34590;
    public static final int GL_TEXTURE_MAG_SIZE_NV = 34591;

    private NVTextureShader() {
    }
}
