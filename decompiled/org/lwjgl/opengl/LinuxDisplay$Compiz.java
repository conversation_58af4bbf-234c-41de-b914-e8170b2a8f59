/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.security.AccessController;
import java.security.PrivilegedAction;
import java.util.ArrayList;
import java.util.List;
import org.lwjgl.LWJGLException;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.opengl.Display;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
private static final class LinuxDisplay.Compiz {
    private static boolean applyFix;
    private static Provider provider;

    private LinuxDisplay.Compiz() {
    }

    static void init() {
        if (Display.getPrivilegedBoolean("org.lwjgl.opengl.Window.nocompiz_lfs")) {
            return;
        }
        AccessController.doPrivileged(new PrivilegedAction<Object>(){

            @Override
            public final Object run() {
                block9: {
                    if (LinuxDisplay.Compiz.isProcessActive("compiz")) break block9;
                    return null;
                }
                try {
                    LinuxDisplay.Compiz.provider = null;
                    String string = null;
                    if (LinuxDisplay.Compiz.isProcessActive("dbus-daemon")) {
                        string = "Dbus";
                        LinuxDisplay.Compiz.provider = new Provider(){
                            private static final String KEY = "/org/freedesktop/compiz/workarounds/allscreens/legacy_fullscreen";

                            public boolean hasLegacyFullscreenSupport() {
                                List list = LinuxDisplay.Compiz.run(new String[]{"dbus-send", "--print-reply", "--type=method_call", "--dest=org.freedesktop.compiz", KEY, "org.freedesktop.compiz.get"});
                                if (list == null || list.size() < 2) {
                                    throw new LWJGLException("Invalid Dbus reply.");
                                }
                                String string = (String)list.get(0);
                                if (!string.startsWith("method return")) {
                                    throw new LWJGLException("Invalid Dbus reply.");
                                }
                                string = ((String)list.get(1)).trim();
                                if (!string.startsWith("boolean") || string.length() < 12) {
                                    throw new LWJGLException("Invalid Dbus reply.");
                                }
                                return "true".equalsIgnoreCase(string.substring(7 + 1));
                            }

                            public void setLegacyFullscreenSupport(boolean bl) {
                                if (LinuxDisplay.Compiz.run(new String[]{"dbus-send", "--type=method_call", "--dest=org.freedesktop.compiz", KEY, "org.freedesktop.compiz.set", "boolean:" + Boolean.toString(bl)}) == null) {
                                    throw new LWJGLException("Failed to apply Compiz LFS workaround.");
                                }
                            }
                        };
                    } else {
                        try {
                            Runtime.getRuntime().exec("gconftool");
                            string = "gconftool";
                            LinuxDisplay.Compiz.provider = new Provider(){
                                private static final String KEY = "/apps/compiz/plugins/workarounds/allscreens/options/legacy_fullscreen";

                                public boolean hasLegacyFullscreenSupport() {
                                    List list = LinuxDisplay.Compiz.run(new String[]{"gconftool", "-g", KEY});
                                    if (list == null || list.size() == 0) {
                                        throw new LWJGLException("Invalid gconftool reply.");
                                    }
                                    return Boolean.parseBoolean(((String)list.get(0)).trim());
                                }

                                public void setLegacyFullscreenSupport(boolean bl) {
                                    if (LinuxDisplay.Compiz.run(new String[]{"gconftool", "-s", KEY, "-s", Boolean.toString(bl), "-t", "bool"}) == null) {
                                        throw new LWJGLException("Failed to apply Compiz LFS workaround.");
                                    }
                                    if (bl) {
                                        try {
                                            Thread.sleep(200L);
                                            return;
                                        }
                                        catch (InterruptedException interruptedException) {
                                            InterruptedException interruptedException2 = interruptedException;
                                            interruptedException.printStackTrace();
                                        }
                                    }
                                }
                            };
                        }
                        catch (IOException iOException) {}
                    }
                    if (provider != null && !provider.hasLegacyFullscreenSupport()) {
                        applyFix = true;
                        LWJGLUtil.log("Using " + string + " to apply Compiz LFS workaround.");
                    }
                    return null;
                }
                catch (LWJGLException lWJGLException) {
                    return null;
                }
                catch (Throwable throwable) {
                    return null;
                }
            }
        });
    }

    static void setLegacyFullscreenSupport(final boolean bl) {
        if (!applyFix) {
            return;
        }
        AccessController.doPrivileged(new PrivilegedAction<Object>(){

            @Override
            public final Object run() {
                try {
                    provider.setLegacyFullscreenSupport(bl);
                }
                catch (LWJGLException lWJGLException) {
                    LWJGLUtil.log("Failed to change Compiz Legacy Fullscreen Support. Reason: " + lWJGLException.getMessage());
                }
                return null;
            }
        });
    }

    private static List<String> run(String ... object) {
        ArrayList<String> arrayList = new ArrayList<String>();
        try {
            object = Runtime.getRuntime().exec((String[])object);
            try {
                int n = ((Process)object).waitFor();
                if (n != 0) {
                    return null;
                }
            }
            catch (InterruptedException interruptedException) {
                throw new LWJGLException("Process interrupted.", interruptedException);
            }
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(((Process)object).getInputStream()));
            while ((object = bufferedReader.readLine()) != null) {
                arrayList.add((String)object);
            }
            bufferedReader.close();
        }
        catch (IOException iOException) {
            throw new LWJGLException("Process failed.", iOException);
        }
        return arrayList;
    }

    private static boolean isProcessActive(String string) {
        List<String> list = LinuxDisplay.Compiz.run("ps", "-C", string);
        if (list == null) {
            return false;
        }
        for (String string2 : list) {
            if (!string2.contains(string)) continue;
            return true;
        }
        return false;
    }

    private static interface Provider {
        public boolean hasLegacyFullscreenSupport();

        public void setLegacyFullscreenSupport(boolean var1);
    }
}
