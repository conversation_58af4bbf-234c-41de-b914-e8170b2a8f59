/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVTextureMultisample {
    public static final int GL_TEXTURE_COVERAGE_SAMPLES_NV = 36933;
    public static final int GL_TEXTURE_COLOR_SAMPLES_NV = 36934;

    private NVTextureMultisample() {
    }

    public static void glTexImage2DMultisampleCoverageNV(int n, int n2, int n3, int n4, int n5, int n6, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexImage2DMultisampleCoverageNV;
        BufferChecks.checkFunctionAddress(l);
        NVTextureMultisample.nglTexImage2DMultisampleCoverageNV(n, n2, n3, n4, n5, n6, bl, l);
    }

    static native void nglTexImage2DMultisampleCoverageNV(int var0, int var1, int var2, int var3, int var4, int var5, boolean var6, long var7);

    public static void glTexImage3DMultisampleCoverageNV(int n, int n2, int n3, int n4, int n5, int n6, int n7, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexImage3DMultisampleCoverageNV;
        BufferChecks.checkFunctionAddress(l);
        NVTextureMultisample.nglTexImage3DMultisampleCoverageNV(n, n2, n3, n4, n5, n6, n7, bl, l);
    }

    static native void nglTexImage3DMultisampleCoverageNV(int var0, int var1, int var2, int var3, int var4, int var5, int var6, boolean var7, long var8);

    public static void glTextureImage2DMultisampleNV(int n, int n2, int n3, int n4, int n5, int n6, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureImage2DMultisampleNV;
        BufferChecks.checkFunctionAddress(l);
        NVTextureMultisample.nglTextureImage2DMultisampleNV(n, n2, n3, n4, n5, n6, bl, l);
    }

    static native void nglTextureImage2DMultisampleNV(int var0, int var1, int var2, int var3, int var4, int var5, boolean var6, long var7);

    public static void glTextureImage3DMultisampleNV(int n, int n2, int n3, int n4, int n5, int n6, int n7, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureImage3DMultisampleNV;
        BufferChecks.checkFunctionAddress(l);
        NVTextureMultisample.nglTextureImage3DMultisampleNV(n, n2, n3, n4, n5, n6, n7, bl, l);
    }

    static native void nglTextureImage3DMultisampleNV(int var0, int var1, int var2, int var3, int var4, int var5, int var6, boolean var7, long var8);

    public static void glTextureImage2DMultisampleCoverageNV(int n, int n2, int n3, int n4, int n5, int n6, int n7, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureImage2DMultisampleCoverageNV;
        BufferChecks.checkFunctionAddress(l);
        NVTextureMultisample.nglTextureImage2DMultisampleCoverageNV(n, n2, n3, n4, n5, n6, n7, bl, l);
    }

    static native void nglTextureImage2DMultisampleCoverageNV(int var0, int var1, int var2, int var3, int var4, int var5, int var6, boolean var7, long var8);

    public static void glTextureImage3DMultisampleCoverageNV(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureImage3DMultisampleCoverageNV;
        BufferChecks.checkFunctionAddress(l);
        NVTextureMultisample.nglTextureImage3DMultisampleCoverageNV(n, n2, n3, n4, n5, n6, n7, n8, bl, l);
    }

    static native void nglTextureImage3DMultisampleCoverageNV(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, boolean var8, long var9);
}
