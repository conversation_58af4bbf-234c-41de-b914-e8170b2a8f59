/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ARBInstancedArrays {
    public static final int GL_VERTEX_ATTRIB_ARRAY_DIVISOR_ARB = 35070;

    private ARBInstancedArrays() {
    }

    public static void glVertexAttribDivisorARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribDivisorARB;
        BufferChecks.checkFunctionAddress(l);
        ARBInstancedArrays.nglVertexAttribDivisorARB(n, n2, l);
    }

    static native void nglVertexAttribDivisorARB(int var0, int var1, long var2);
}
