/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.DoubleBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GL40;
import org.lwjgl.opengl.GLContext;

public final class ARBGpuShaderFp64 {
    public static final int GL_DOUBLE_VEC2 = 36860;
    public static final int GL_DOUBLE_VEC3 = 36861;
    public static final int GL_DOUBLE_VEC4 = 36862;
    public static final int GL_DOUBLE_MAT2 = 36678;
    public static final int GL_DOUBLE_MAT3 = 36679;
    public static final int GL_DOUBLE_MAT4 = 36680;
    public static final int GL_DOUBLE_MAT2x3 = 36681;
    public static final int GL_DOUBLE_MAT2x4 = 36682;
    public static final int GL_DOUBLE_MAT3x2 = 36683;
    public static final int GL_DOUBLE_MAT3x4 = 36684;
    public static final int GL_DOUBLE_MAT4x2 = 36685;
    public static final int GL_DOUBLE_MAT4x3 = 36686;

    private ARBGpuShaderFp64() {
    }

    public static void glUniform1d(int n, double d) {
        GL40.glUniform1d(n, d);
    }

    public static void glUniform2d(int n, double d, double d2) {
        GL40.glUniform2d(n, d, d2);
    }

    public static void glUniform3d(int n, double d, double d2, double d3) {
        GL40.glUniform3d(n, d, d2, d3);
    }

    public static void glUniform4d(int n, double d, double d2, double d3, double d4) {
        GL40.glUniform4d(n, d, d2, d3, d4);
    }

    public static void glUniform1(int n, DoubleBuffer doubleBuffer) {
        GL40.glUniform1(n, doubleBuffer);
    }

    public static void glUniform2(int n, DoubleBuffer doubleBuffer) {
        GL40.glUniform2(n, doubleBuffer);
    }

    public static void glUniform3(int n, DoubleBuffer doubleBuffer) {
        GL40.glUniform3(n, doubleBuffer);
    }

    public static void glUniform4(int n, DoubleBuffer doubleBuffer) {
        GL40.glUniform4(n, doubleBuffer);
    }

    public static void glUniformMatrix2(int n, boolean bl, DoubleBuffer doubleBuffer) {
        GL40.glUniformMatrix2(n, bl, doubleBuffer);
    }

    public static void glUniformMatrix3(int n, boolean bl, DoubleBuffer doubleBuffer) {
        GL40.glUniformMatrix3(n, bl, doubleBuffer);
    }

    public static void glUniformMatrix4(int n, boolean bl, DoubleBuffer doubleBuffer) {
        GL40.glUniformMatrix4(n, bl, doubleBuffer);
    }

    public static void glUniformMatrix2x3(int n, boolean bl, DoubleBuffer doubleBuffer) {
        GL40.glUniformMatrix2x3(n, bl, doubleBuffer);
    }

    public static void glUniformMatrix2x4(int n, boolean bl, DoubleBuffer doubleBuffer) {
        GL40.glUniformMatrix2x4(n, bl, doubleBuffer);
    }

    public static void glUniformMatrix3x2(int n, boolean bl, DoubleBuffer doubleBuffer) {
        GL40.glUniformMatrix3x2(n, bl, doubleBuffer);
    }

    public static void glUniformMatrix3x4(int n, boolean bl, DoubleBuffer doubleBuffer) {
        GL40.glUniformMatrix3x4(n, bl, doubleBuffer);
    }

    public static void glUniformMatrix4x2(int n, boolean bl, DoubleBuffer doubleBuffer) {
        GL40.glUniformMatrix4x2(n, bl, doubleBuffer);
    }

    public static void glUniformMatrix4x3(int n, boolean bl, DoubleBuffer doubleBuffer) {
        GL40.glUniformMatrix4x3(n, bl, doubleBuffer);
    }

    public static void glGetUniform(int n, int n2, DoubleBuffer doubleBuffer) {
        GL40.glGetUniform(n, n2, doubleBuffer);
    }

    public static void glProgramUniform1dEXT(int n, int n2, double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform1dEXT;
        BufferChecks.checkFunctionAddress(l);
        ARBGpuShaderFp64.nglProgramUniform1dEXT(n, n2, d, l);
    }

    static native void nglProgramUniform1dEXT(int var0, int var1, double var2, long var4);

    public static void glProgramUniform2dEXT(int n, int n2, double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform2dEXT;
        BufferChecks.checkFunctionAddress(l);
        ARBGpuShaderFp64.nglProgramUniform2dEXT(n, n2, d, d2, l);
    }

    static native void nglProgramUniform2dEXT(int var0, int var1, double var2, double var4, long var6);

    public static void glProgramUniform3dEXT(int n, int n2, double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform3dEXT;
        BufferChecks.checkFunctionAddress(l);
        ARBGpuShaderFp64.nglProgramUniform3dEXT(n, n2, d, d2, d3, l);
    }

    static native void nglProgramUniform3dEXT(int var0, int var1, double var2, double var4, double var6, long var8);

    public static void glProgramUniform4dEXT(int n, int n2, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform4dEXT;
        BufferChecks.checkFunctionAddress(l);
        ARBGpuShaderFp64.nglProgramUniform4dEXT(n, n2, d, d2, d3, d4, l);
    }

    static native void nglProgramUniform4dEXT(int var0, int var1, double var2, double var4, double var6, double var8, long var10);

    public static void glProgramUniform1EXT(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform1dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBGpuShaderFp64.nglProgramUniform1dvEXT(n, n2, doubleBuffer.remaining(), MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniform1dvEXT(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform2EXT(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform2dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBGpuShaderFp64.nglProgramUniform2dvEXT(n, n2, doubleBuffer.remaining() >> 1, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniform2dvEXT(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform3EXT(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform3dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBGpuShaderFp64.nglProgramUniform3dvEXT(n, n2, doubleBuffer.remaining() / 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniform3dvEXT(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform4EXT(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform4dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBGpuShaderFp64.nglProgramUniform4dvEXT(n, n2, doubleBuffer.remaining() >> 2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniform4dvEXT(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniformMatrix2EXT(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix2dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBGpuShaderFp64.nglProgramUniformMatrix2dvEXT(n, n2, doubleBuffer.remaining() >> 2, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix2dvEXT(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix3EXT(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix3dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBGpuShaderFp64.nglProgramUniformMatrix3dvEXT(n, n2, doubleBuffer.remaining() / 9, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix3dvEXT(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix4EXT(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix4dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBGpuShaderFp64.nglProgramUniformMatrix4dvEXT(n, n2, doubleBuffer.remaining() >> 4, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix4dvEXT(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix2x3EXT(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix2x3dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBGpuShaderFp64.nglProgramUniformMatrix2x3dvEXT(n, n2, doubleBuffer.remaining() / 6, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix2x3dvEXT(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix2x4EXT(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix2x4dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBGpuShaderFp64.nglProgramUniformMatrix2x4dvEXT(n, n2, doubleBuffer.remaining() >> 3, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix2x4dvEXT(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix3x2EXT(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix3x2dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBGpuShaderFp64.nglProgramUniformMatrix3x2dvEXT(n, n2, doubleBuffer.remaining() / 6, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix3x2dvEXT(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix3x4EXT(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix3x4dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBGpuShaderFp64.nglProgramUniformMatrix3x4dvEXT(n, n2, doubleBuffer.remaining() / 12, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix3x4dvEXT(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix4x2EXT(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix4x2dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBGpuShaderFp64.nglProgramUniformMatrix4x2dvEXT(n, n2, doubleBuffer.remaining() >> 3, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix4x2dvEXT(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix4x3EXT(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix4x3dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBGpuShaderFp64.nglProgramUniformMatrix4x3dvEXT(n, n2, doubleBuffer.remaining() / 12, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix4x3dvEXT(int var0, int var1, int var2, boolean var3, long var4, long var6);
}
