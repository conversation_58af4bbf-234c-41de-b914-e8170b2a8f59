/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class APPLEFlushBufferRange {
    public static final int GL_BUFFER_SERIALIZED_MODIFY_APPLE = 35346;
    public static final int GL_BUFFER_FLUSHING_UNMAP_APPLE = 35347;

    private APPLEFlushBufferRange() {
    }

    public static void glBufferParameteriAPPLE(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBufferParameteriAPPLE;
        BufferChecks.checkFunctionAddress(l);
        APPLEFlushBufferRange.nglBufferParameteriAPPLE(n, n2, n3, l);
    }

    static native void nglBufferParameteriAPPLE(int var0, int var1, int var2, long var3);

    public static void glFlushMappedBufferRangeAPPLE(int n, long l, long l2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glFlushMappedBufferRangeAPPLE;
        BufferChecks.checkFunctionAddress(l3);
        APPLEFlushBufferRange.nglFlushMappedBufferRangeAPPLE(n, l, l2, l3);
    }

    static native void nglFlushMappedBufferRangeAPPLE(int var0, long var1, long var3, long var5);
}
