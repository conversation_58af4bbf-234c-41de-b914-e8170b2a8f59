/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Canvas;
import java.nio.ByteBuffer;
import org.lwjgl.LWJGLException;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.opengl.AWTSurfaceLock;
import org.lwjgl.opengl.LinuxPeerInfo;

final class LinuxAWTGLCanvasPeerInfo
extends LinuxPeerInfo {
    private final Canvas component;
    private final AWTSurfaceLock awt_surface = new AWTSurfaceLock();
    private int screen = -1;

    LinuxAWTGLCanvasPeerInfo(Canvas canvas) {
        this.component = canvas;
    }

    protected final void doLockAndInitHandle() {
        ByteBuffer byteBuffer = this.awt_surface.lockAndGetHandle(this.component);
        if (this.screen == -1) {
            try {
                this.screen = LinuxAWTGLCanvasPeerInfo.getScreenFromSurfaceInfo(byteBuffer);
            }
            catch (LWJGLException lWJGLException) {
                LWJGLUtil.log("Got exception while trying to determine screen: " + lWJGLException);
                this.screen = 0;
            }
        }
        LinuxAWTGLCanvasPeerInfo.nInitHandle(this.screen, byteBuffer, this.getHandle());
    }

    private static native int getScreenFromSurfaceInfo(ByteBuffer var0);

    private static native void nInitHandle(int var0, ByteBuffer var1, ByteBuffer var2);

    protected final void doUnlock() {
        this.awt_surface.unlock();
    }
}
