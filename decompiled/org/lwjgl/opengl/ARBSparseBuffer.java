/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ARBSparseBuffer {
    public static final int GL_SPARSE_STORAGE_BIT_ARB = 1024;
    public static final int GL_SPARSE_BUFFER_PAGE_SIZE_ARB = 33528;

    private ARBSparseBuffer() {
    }

    public static void glBufferPageCommitmentARB(int n, long l, long l2, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glBufferPageCommitmentARB;
        BufferChecks.checkFunctionAddress(l3);
        ARBSparseBuffer.nglBufferPageCommitmentARB(n, l, l2, bl, l3);
    }

    static native void nglBufferPageCommitmentARB(int var0, long var1, long var3, boolean var5, long var6);
}
