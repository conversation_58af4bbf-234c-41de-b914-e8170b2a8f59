/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

public final class ARBTextureCubeMap {
    public static final int GL_NORMAL_MAP_ARB = 34065;
    public static final int GL_REFLECTION_MAP_ARB = 34066;
    public static final int GL_TEXTURE_CUBE_MAP_ARB = 34067;
    public static final int GL_TEXTURE_BINDING_CUBE_MAP_ARB = 34068;
    public static final int GL_TEXTURE_CUBE_MAP_POSITIVE_X_ARB = 34069;
    public static final int GL_TEXTURE_CUBE_MAP_NEGATIVE_X_ARB = 34070;
    public static final int GL_TEXTURE_CUBE_MAP_POSITIVE_Y_ARB = 34071;
    public static final int GL_TEXTURE_CUBE_MAP_NEGATIVE_Y_ARB = 34072;
    public static final int GL_TEXTURE_CUBE_MAP_POSITIVE_Z_ARB = 34073;
    public static final int GL_TEXTURE_CUBE_MAP_NEGATIVE_Z_ARB = 34074;
    public static final int GL_PROXY_TEXTURE_CUBE_MAP_ARB = 34075;
    public static final int GL_MAX_CUBE_MAP_TEXTURE_SIZE_ARB = 34076;

    private ARBTextureCubeMap() {
    }
}
