/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.DoubleBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GL41;
import org.lwjgl.opengl.GLContext;

public final class ARBVertexAttrib64bit {
    public static final int GL_DOUBLE_VEC2 = 36860;
    public static final int GL_DOUBLE_VEC3 = 36861;
    public static final int GL_DOUBLE_VEC4 = 36862;
    public static final int GL_DOUBLE_MAT2 = 36678;
    public static final int GL_DOUBLE_MAT3 = 36679;
    public static final int GL_DOUBLE_MAT4 = 36680;
    public static final int GL_DOUBLE_MAT2x3 = 36681;
    public static final int GL_DOUBLE_MAT2x4 = 36682;
    public static final int GL_DOUBLE_MAT3x2 = 36683;
    public static final int GL_DOUBLE_MAT3x4 = 36684;
    public static final int GL_DOUBLE_MAT4x2 = 36685;
    public static final int GL_DOUBLE_MAT4x3 = 36686;

    private ARBVertexAttrib64bit() {
    }

    public static void glVertexAttribL1d(int n, double d) {
        GL41.glVertexAttribL1d(n, d);
    }

    public static void glVertexAttribL2d(int n, double d, double d2) {
        GL41.glVertexAttribL2d(n, d, d2);
    }

    public static void glVertexAttribL3d(int n, double d, double d2, double d3) {
        GL41.glVertexAttribL3d(n, d, d2, d3);
    }

    public static void glVertexAttribL4d(int n, double d, double d2, double d3, double d4) {
        GL41.glVertexAttribL4d(n, d, d2, d3, d4);
    }

    public static void glVertexAttribL1(int n, DoubleBuffer doubleBuffer) {
        GL41.glVertexAttribL1(n, doubleBuffer);
    }

    public static void glVertexAttribL2(int n, DoubleBuffer doubleBuffer) {
        GL41.glVertexAttribL2(n, doubleBuffer);
    }

    public static void glVertexAttribL3(int n, DoubleBuffer doubleBuffer) {
        GL41.glVertexAttribL3(n, doubleBuffer);
    }

    public static void glVertexAttribL4(int n, DoubleBuffer doubleBuffer) {
        GL41.glVertexAttribL4(n, doubleBuffer);
    }

    public static void glVertexAttribLPointer(int n, int n2, int n3, DoubleBuffer doubleBuffer) {
        GL41.glVertexAttribLPointer(n, n2, n3, doubleBuffer);
    }

    public static void glVertexAttribLPointer(int n, int n2, int n3, long l) {
        GL41.glVertexAttribLPointer(n, n2, n3, l);
    }

    public static void glGetVertexAttribL(int n, int n2, DoubleBuffer doubleBuffer) {
        GL41.glGetVertexAttribL(n, n2, doubleBuffer);
    }

    public static void glVertexArrayVertexAttribLOffsetEXT(int n, int n2, int n3, int n4, int n5, int n6, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glVertexArrayVertexAttribLOffsetEXT;
        BufferChecks.checkFunctionAddress(l2);
        ARBVertexAttrib64bit.nglVertexArrayVertexAttribLOffsetEXT(n, n2, n3, n4, n5, n6, l, l2);
    }

    static native void nglVertexArrayVertexAttribLOffsetEXT(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);
}
