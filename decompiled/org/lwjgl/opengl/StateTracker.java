/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.opengl.BaseReferences;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.FastIntMap;
import org.lwjgl.opengl.References;
import org.lwjgl.opengl.ReferencesStack;
import org.lwjgl.opengl.StateStack;

final class StateTracker {
    private ReferencesStack references_stack;
    private final StateStack attrib_stack;
    private boolean insideBeginEnd;
    private final FastIntMap<VAOState> vaoMap = new FastIntMap();

    StateTracker() {
        this.attrib_stack = new StateStack(0);
    }

    final void init() {
        this.references_stack = new ReferencesStack();
    }

    static void setBeginEnd(ContextCapabilities contextCapabilities, boolean bl) {
        contextCapabilities.tracker.insideBeginEnd = bl;
    }

    final boolean isBeginEnd() {
        return this.insideBeginEnd;
    }

    static void popAttrib(ContextCapabilities contextCapabilities) {
        contextCapabilities.tracker.doPopAttrib();
    }

    private void doPopAttrib() {
        this.references_stack.popState(this.attrib_stack.popState());
    }

    static void pushAttrib(ContextCapabilities contextCapabilities, int n) {
        contextCapabilities.tracker.doPushAttrib(n);
    }

    private void doPushAttrib(int n) {
        this.attrib_stack.pushState(n);
        this.references_stack.pushState();
    }

    static References getReferences(ContextCapabilities contextCapabilities) {
        return contextCapabilities.tracker.references_stack.getReferences();
    }

    static void bindBuffer(ContextCapabilities contextCapabilities, int n, int n2) {
        References references = StateTracker.getReferences(contextCapabilities);
        switch (n) {
            case 34962: {
                references.arrayBuffer = n2;
                return;
            }
            case 34963: {
                if (references.vertexArrayObject != 0) {
                    contextCapabilities.tracker.vaoMap.get((int)references.vertexArrayObject).elementArrayBuffer = n2;
                    return;
                }
                references.elementArrayBuffer = n2;
                return;
            }
            case 35051: {
                references.pixelPackBuffer = n2;
                return;
            }
            case 35052: {
                references.pixelUnpackBuffer = n2;
                return;
            }
            case 36671: {
                references.indirectBuffer = n2;
            }
        }
    }

    static void bindVAO(ContextCapabilities contextCapabilities, int n) {
        FastIntMap<VAOState> fastIntMap = contextCapabilities.tracker.vaoMap;
        if (!fastIntMap.containsKey(n)) {
            fastIntMap.put(n, new VAOState());
        }
        StateTracker.getReferences((ContextCapabilities)contextCapabilities).vertexArrayObject = n;
    }

    static void deleteVAO(ContextCapabilities contextCapabilities, IntBuffer intBuffer) {
        for (int i = intBuffer.position(); i < intBuffer.limit(); ++i) {
            StateTracker.deleteVAO(contextCapabilities, intBuffer.get(i));
        }
    }

    static void deleteVAO(ContextCapabilities object, int n) {
        ((ContextCapabilities)object).tracker.vaoMap.remove(n);
        object = StateTracker.getReferences((ContextCapabilities)object);
        if (((BaseReferences)object).vertexArrayObject == n) {
            ((BaseReferences)object).vertexArrayObject = 0;
        }
    }

    static int getElementArrayBufferBound(ContextCapabilities contextCapabilities) {
        References references = StateTracker.getReferences(contextCapabilities);
        if (references.vertexArrayObject == 0) {
            return references.elementArrayBuffer;
        }
        return contextCapabilities.tracker.vaoMap.get((int)references.vertexArrayObject).elementArrayBuffer;
    }

    private static class VAOState {
        int elementArrayBuffer;

        private VAOState() {
        }
    }
}
