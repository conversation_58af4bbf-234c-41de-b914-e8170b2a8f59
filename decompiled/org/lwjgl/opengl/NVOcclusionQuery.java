/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVOcclusionQuery {
    public static final int GL_OCCLUSION_TEST_HP = 33125;
    public static final int GL_OCCLUSION_TEST_RESULT_HP = 33126;
    public static final int GL_PIXEL_COUNTER_BITS_NV = 34916;
    public static final int GL_CURRENT_OCCLUSION_QUERY_ID_NV = 34917;
    public static final int GL_PIXEL_COUNT_NV = 34918;
    public static final int GL_PIXEL_COUNT_AVAILABLE_NV = 34919;

    private NVOcclusionQuery() {
    }

    public static void glGenOcclusionQueriesNV(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenOcclusionQueriesNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        NVOcclusionQuery.nglGenOcclusionQueriesNV(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenOcclusionQueriesNV(int var0, long var1, long var3);

    public static int glGenOcclusionQueriesNV() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenOcclusionQueriesNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        NVOcclusionQuery.nglGenOcclusionQueriesNV(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glDeleteOcclusionQueriesNV(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteOcclusionQueriesNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        NVOcclusionQuery.nglDeleteOcclusionQueriesNV(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeleteOcclusionQueriesNV(int var0, long var1, long var3);

    public static void glDeleteOcclusionQueriesNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteOcclusionQueriesNV;
        BufferChecks.checkFunctionAddress(l);
        NVOcclusionQuery.nglDeleteOcclusionQueriesNV(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static boolean glIsOcclusionQueryNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsOcclusionQueryNV;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = NVOcclusionQuery.nglIsOcclusionQueryNV(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsOcclusionQueryNV(int var0, long var1);

    public static void glBeginOcclusionQueryNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBeginOcclusionQueryNV;
        BufferChecks.checkFunctionAddress(l);
        NVOcclusionQuery.nglBeginOcclusionQueryNV(n, l);
    }

    static native void nglBeginOcclusionQueryNV(int var0, long var1);

    public static void glEndOcclusionQueryNV() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEndOcclusionQueryNV;
        BufferChecks.checkFunctionAddress(l);
        NVOcclusionQuery.nglEndOcclusionQueryNV(l);
    }

    static native void nglEndOcclusionQueryNV(long var0);

    public static void glGetOcclusionQueryuNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetOcclusionQueryuivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        NVOcclusionQuery.nglGetOcclusionQueryuivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetOcclusionQueryuivNV(int var0, int var1, long var2, long var4);

    public static int glGetOcclusionQueryuiNV(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetOcclusionQueryuivNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        NVOcclusionQuery.nglGetOcclusionQueryuivNV(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetOcclusionQueryNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetOcclusionQueryivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        NVOcclusionQuery.nglGetOcclusionQueryivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetOcclusionQueryivNV(int var0, int var1, long var2, long var4);

    public static int glGetOcclusionQueryiNV(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetOcclusionQueryivNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        NVOcclusionQuery.nglGetOcclusionQueryivNV(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }
}
