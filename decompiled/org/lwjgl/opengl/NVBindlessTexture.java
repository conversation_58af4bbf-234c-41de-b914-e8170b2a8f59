/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.LongBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVBindlessTexture {
    private NVBindlessTexture() {
    }

    public static long glGetTextureHandleNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureHandleNV;
        BufferChecks.checkFunctionAddress(l);
        long l2 = NVBindlessTexture.nglGetTextureHandleNV(n, l);
        return l2;
    }

    static native long nglGetTextureHandleNV(int var0, long var1);

    public static long glGetTextureSamplerHandleNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureSamplerHandleNV;
        BufferChecks.checkFunctionAddress(l);
        long l2 = NVBindlessTexture.nglGetTextureSamplerHandleNV(n, n2, l);
        return l2;
    }

    static native long nglGetTextureSamplerHandleNV(int var0, int var1, long var2);

    public static void glMakeTextureHandleResidentNV(long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMakeTextureHandleResidentNV;
        BufferChecks.checkFunctionAddress(l2);
        NVBindlessTexture.nglMakeTextureHandleResidentNV(l, l2);
    }

    static native void nglMakeTextureHandleResidentNV(long var0, long var2);

    public static void glMakeTextureHandleNonResidentNV(long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMakeTextureHandleNonResidentNV;
        BufferChecks.checkFunctionAddress(l2);
        NVBindlessTexture.nglMakeTextureHandleNonResidentNV(l, l2);
    }

    static native void nglMakeTextureHandleNonResidentNV(long var0, long var2);

    public static long glGetImageHandleNV(int n, int n2, boolean bl, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetImageHandleNV;
        BufferChecks.checkFunctionAddress(l);
        long l2 = NVBindlessTexture.nglGetImageHandleNV(n, n2, bl, n3, n4, l);
        return l2;
    }

    static native long nglGetImageHandleNV(int var0, int var1, boolean var2, int var3, int var4, long var5);

    public static void glMakeImageHandleResidentNV(long l, int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMakeImageHandleResidentNV;
        BufferChecks.checkFunctionAddress(l2);
        NVBindlessTexture.nglMakeImageHandleResidentNV(l, n, l2);
    }

    static native void nglMakeImageHandleResidentNV(long var0, int var2, long var3);

    public static void glMakeImageHandleNonResidentNV(long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMakeImageHandleNonResidentNV;
        BufferChecks.checkFunctionAddress(l2);
        NVBindlessTexture.nglMakeImageHandleNonResidentNV(l, l2);
    }

    static native void nglMakeImageHandleNonResidentNV(long var0, long var2);

    public static void glUniformHandleui64NV(int n, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glUniformHandleui64NV;
        BufferChecks.checkFunctionAddress(l2);
        NVBindlessTexture.nglUniformHandleui64NV(n, l, l2);
    }

    static native void nglUniformHandleui64NV(int var0, long var1, long var3);

    public static void glUniformHandleuNV(int n, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformHandleui64vNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(longBuffer);
        NVBindlessTexture.nglUniformHandleui64vNV(n, longBuffer.remaining(), MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglUniformHandleui64vNV(int var0, int var1, long var2, long var4);

    public static void glProgramUniformHandleui64NV(int n, int n2, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glProgramUniformHandleui64NV;
        BufferChecks.checkFunctionAddress(l2);
        NVBindlessTexture.nglProgramUniformHandleui64NV(n, n2, l, l2);
    }

    static native void nglProgramUniformHandleui64NV(int var0, int var1, long var2, long var4);

    public static void glProgramUniformHandleuNV(int n, int n2, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformHandleui64vNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(longBuffer);
        NVBindlessTexture.nglProgramUniformHandleui64vNV(n, n2, longBuffer.remaining(), MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglProgramUniformHandleui64vNV(int var0, int var1, int var2, long var3, long var5);

    public static boolean glIsTextureHandleResidentNV(long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glIsTextureHandleResidentNV;
        BufferChecks.checkFunctionAddress(l2);
        boolean bl = NVBindlessTexture.nglIsTextureHandleResidentNV(l, l2);
        return bl;
    }

    static native boolean nglIsTextureHandleResidentNV(long var0, long var2);

    public static boolean glIsImageHandleResidentNV(long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glIsImageHandleResidentNV;
        BufferChecks.checkFunctionAddress(l2);
        boolean bl = NVBindlessTexture.nglIsImageHandleResidentNV(l, l2);
        return bl;
    }

    static native boolean nglIsImageHandleResidentNV(long var0, long var2);
}
