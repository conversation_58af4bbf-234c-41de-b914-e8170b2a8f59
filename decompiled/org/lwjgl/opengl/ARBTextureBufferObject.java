/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ARBTextureBufferObject {
    public static final int GL_TEXTURE_BUFFER_ARB = 35882;
    public static final int GL_MAX_TEXTURE_BUFFER_SIZE_ARB = 35883;
    public static final int GL_TEXTURE_BINDING_BUFFER_ARB = 35884;
    public static final int GL_TEXTURE_BUFFER_DATA_STORE_BINDING_ARB = 35885;
    public static final int GL_TEXTURE_BUFFER_FORMAT_ARB = 35886;

    private ARBTextureBufferObject() {
    }

    public static void glTexBufferARB(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexBufferARB;
        BufferChecks.checkFunctionAddress(l);
        ARBTextureBufferObject.nglTexBufferARB(n, n2, n3, l);
    }

    static native void nglTexBufferARB(int var0, int var1, int var2, long var3);
}
