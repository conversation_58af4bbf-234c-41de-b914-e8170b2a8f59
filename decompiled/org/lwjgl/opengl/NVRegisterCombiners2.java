/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.FloatBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVRegisterCombiners2 {
    public static final int GL_PER_STAGE_CONSTANTS_NV = 34101;

    private NVRegisterCombiners2() {
    }

    public static void glCombinerStageParameterNV(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCombinerStageParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVRegisterCombiners2.nglCombinerStageParameterfvNV(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglCombinerStageParameterfvNV(int var0, int var1, long var2, long var4);

    public static void glGetCombinerStageParameterNV(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCombinerStageParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVRegisterCombiners2.nglGetCombinerStageParameterfvNV(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetCombinerStageParameterfvNV(int var0, int var1, long var2, long var4);
}
