/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.StateTracker;

public final class GL41 {
    public static final int GL_SHADER_COMPILER = 36346;
    public static final int GL_NUM_SHADER_BINARY_FORMATS = 36345;
    public static final int GL_MAX_VERTEX_UNIFORM_VECTORS = 36347;
    public static final int GL_MAX_VARYING_VECTORS = 36348;
    public static final int GL_MAX_FRAGMENT_UNIFORM_VECTORS = 36349;
    public static final int GL_IMPLEMENTATION_COLOR_READ_TYPE = 35738;
    public static final int GL_IMPLEMENTATION_COLOR_READ_FORMAT = 35739;
    public static final int GL_FIXED = 5132;
    public static final int GL_LOW_FLOAT = 36336;
    public static final int GL_MEDIUM_FLOAT = 36337;
    public static final int GL_HIGH_FLOAT = 36338;
    public static final int GL_LOW_INT = 36339;
    public static final int GL_MEDIUM_INT = 36340;
    public static final int GL_HIGH_INT = 36341;
    public static final int GL_RGB565 = 36194;
    public static final int GL_PROGRAM_BINARY_RETRIEVABLE_HINT = 33367;
    public static final int GL_PROGRAM_BINARY_LENGTH = 34625;
    public static final int GL_NUM_PROGRAM_BINARY_FORMATS = 34814;
    public static final int GL_PROGRAM_BINARY_FORMATS = 34815;
    public static final int GL_VERTEX_SHADER_BIT = 1;
    public static final int GL_FRAGMENT_SHADER_BIT = 2;
    public static final int GL_GEOMETRY_SHADER_BIT = 4;
    public static final int GL_TESS_CONTROL_SHADER_BIT = 8;
    public static final int GL_TESS_EVALUATION_SHADER_BIT = 16;
    public static final int GL_ALL_SHADER_BITS = -1;
    public static final int GL_PROGRAM_SEPARABLE = 33368;
    public static final int GL_ACTIVE_PROGRAM = 33369;
    public static final int GL_PROGRAM_PIPELINE_BINDING = 33370;
    public static final int GL_MAX_VIEWPORTS = 33371;
    public static final int GL_VIEWPORT_SUBPIXEL_BITS = 33372;
    public static final int GL_VIEWPORT_BOUNDS_RANGE = 33373;
    public static final int GL_LAYER_PROVOKING_VERTEX = 33374;
    public static final int GL_VIEWPORT_INDEX_PROVOKING_VERTEX = 33375;
    public static final int GL_UNDEFINED_VERTEX = 33376;

    private GL41() {
    }

    public static void glReleaseShaderCompiler() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReleaseShaderCompiler;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglReleaseShaderCompiler(l);
    }

    static native void nglReleaseShaderCompiler(long var0);

    public static void glShaderBinary(IntBuffer intBuffer, int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glShaderBinary;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        GL41.nglShaderBinary(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), n, MemoryUtil.getAddress(byteBuffer), byteBuffer.remaining(), l);
    }

    static native void nglShaderBinary(int var0, long var1, int var3, long var4, int var6, long var7);

    public static void glGetShaderPrecisionFormat(int n, int n2, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetShaderPrecisionFormat;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 2);
        BufferChecks.checkBuffer(intBuffer2, 1);
        GL41.nglGetShaderPrecisionFormat(n, n2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    static native void nglGetShaderPrecisionFormat(int var0, int var1, long var2, long var4, long var6);

    public static void glDepthRangef(float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDepthRangef;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglDepthRangef(f, f2, l);
    }

    static native void nglDepthRangef(float var0, float var1, long var2);

    public static void glClearDepthf(float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearDepthf;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglClearDepthf(f, l);
    }

    static native void nglClearDepthf(float var0, long var1);

    public static void glGetProgramBinary(int n, IntBuffer intBuffer, IntBuffer intBuffer2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramBinary;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkBuffer(intBuffer2, 1);
        BufferChecks.checkDirect(byteBuffer);
        GL41.nglGetProgramBinary(n, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetProgramBinary(int var0, int var1, long var2, long var4, long var6, long var8);

    public static void glProgramBinary(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramBinary;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        GL41.nglProgramBinary(n, n2, MemoryUtil.getAddress(byteBuffer), byteBuffer.remaining(), l);
    }

    static native void nglProgramBinary(int var0, int var1, long var2, int var4, long var5);

    public static void glProgramParameteri(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramParameteri;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramParameteri(n, n2, n3, l);
    }

    static native void nglProgramParameteri(int var0, int var1, int var2, long var3);

    public static void glUseProgramStages(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUseProgramStages;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglUseProgramStages(n, n2, n3, l);
    }

    static native void nglUseProgramStages(int var0, int var1, int var2, long var3);

    public static void glActiveShaderProgram(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glActiveShaderProgram;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglActiveShaderProgram(n, n2, l);
    }

    static native void nglActiveShaderProgram(int var0, int var1, long var2);

    public static int glCreateShaderProgram(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateShaderProgramv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = GL41.nglCreateShaderProgramv(n, 1, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglCreateShaderProgramv(int var0, int var1, long var2, long var4);

    public static int glCreateShaderProgram(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateShaderProgramv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer, n2);
        n = GL41.nglCreateShaderProgramv2(n, n2, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglCreateShaderProgramv2(int var0, int var1, long var2, long var4);

    public static int glCreateShaderProgram(int n, ByteBuffer[] byteBufferArray) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateShaderProgramv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkArray(byteBufferArray, 1);
        n = GL41.nglCreateShaderProgramv3(n, byteBufferArray.length, byteBufferArray, l);
        return n;
    }

    static native int nglCreateShaderProgramv3(int var0, int var1, ByteBuffer[] var2, long var3);

    public static int glCreateShaderProgram(int n, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateShaderProgramv;
        BufferChecks.checkFunctionAddress(l);
        n = GL41.nglCreateShaderProgramv(n, 1, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }

    public static int glCreateShaderProgram(int n, CharSequence[] charSequenceArray) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateShaderProgramv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkArray(charSequenceArray);
        n = GL41.nglCreateShaderProgramv2(n, charSequenceArray.length, APIUtil.getBufferNT(contextCapabilities, charSequenceArray), l);
        return n;
    }

    public static void glBindProgramPipeline(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindProgramPipeline;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglBindProgramPipeline(n, l);
    }

    static native void nglBindProgramPipeline(int var0, long var1);

    public static void glDeleteProgramPipelines(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteProgramPipelines;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL41.nglDeleteProgramPipelines(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeleteProgramPipelines(int var0, long var1, long var3);

    public static void glDeleteProgramPipelines(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteProgramPipelines;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglDeleteProgramPipelines(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static void glGenProgramPipelines(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenProgramPipelines;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL41.nglGenProgramPipelines(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenProgramPipelines(int var0, long var1, long var3);

    public static int glGenProgramPipelines() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenProgramPipelines;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL41.nglGenProgramPipelines(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static boolean glIsProgramPipeline(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsProgramPipeline;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = GL41.nglIsProgramPipeline(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsProgramPipeline(int var0, long var1);

    public static void glGetProgramPipeline(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramPipelineiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL41.nglGetProgramPipelineiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetProgramPipelineiv(int var0, int var1, long var2, long var4);

    public static int glGetProgramPipelinei(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetProgramPipelineiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL41.nglGetProgramPipelineiv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glProgramUniform1i(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform1i;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform1i(n, n2, n3, l);
    }

    static native void nglProgramUniform1i(int var0, int var1, int var2, long var3);

    public static void glProgramUniform2i(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform2i;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform2i(n, n2, n3, n4, l);
    }

    static native void nglProgramUniform2i(int var0, int var1, int var2, int var3, long var4);

    public static void glProgramUniform3i(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform3i;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform3i(n, n2, n3, n4, n5, l);
    }

    static native void nglProgramUniform3i(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glProgramUniform4i(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform4i;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform4i(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglProgramUniform4i(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glProgramUniform1f(int n, int n2, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform1f;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform1f(n, n2, f, l);
    }

    static native void nglProgramUniform1f(int var0, int var1, float var2, long var3);

    public static void glProgramUniform2f(int n, int n2, float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform2f;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform2f(n, n2, f, f2, l);
    }

    static native void nglProgramUniform2f(int var0, int var1, float var2, float var3, long var4);

    public static void glProgramUniform3f(int n, int n2, float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform3f;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform3f(n, n2, f, f2, f3, l);
    }

    static native void nglProgramUniform3f(int var0, int var1, float var2, float var3, float var4, long var5);

    public static void glProgramUniform4f(int n, int n2, float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform4f;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform4f(n, n2, f, f2, f3, f4, l);
    }

    static native void nglProgramUniform4f(int var0, int var1, float var2, float var3, float var4, float var5, long var6);

    public static void glProgramUniform1d(int n, int n2, double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform1d;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform1d(n, n2, d, l);
    }

    static native void nglProgramUniform1d(int var0, int var1, double var2, long var4);

    public static void glProgramUniform2d(int n, int n2, double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform2d;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform2d(n, n2, d, d2, l);
    }

    static native void nglProgramUniform2d(int var0, int var1, double var2, double var4, long var6);

    public static void glProgramUniform3d(int n, int n2, double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform3d;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform3d(n, n2, d, d2, d3, l);
    }

    static native void nglProgramUniform3d(int var0, int var1, double var2, double var4, double var6, long var8);

    public static void glProgramUniform4d(int n, int n2, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform4d;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform4d(n, n2, d, d2, d3, d4, l);
    }

    static native void nglProgramUniform4d(int var0, int var1, double var2, double var4, double var6, double var8, long var10);

    public static void glProgramUniform1(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform1iv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL41.nglProgramUniform1iv(n, n2, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramUniform1iv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform2(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform2iv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL41.nglProgramUniform2iv(n, n2, intBuffer.remaining() >> 1, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramUniform2iv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform3(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform3iv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL41.nglProgramUniform3iv(n, n2, intBuffer.remaining() / 3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramUniform3iv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform4(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform4iv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL41.nglProgramUniform4iv(n, n2, intBuffer.remaining() >> 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramUniform4iv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform1(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform1fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglProgramUniform1fv(n, n2, floatBuffer.remaining(), MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramUniform1fv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform2(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform2fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglProgramUniform2fv(n, n2, floatBuffer.remaining() >> 1, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramUniform2fv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform3(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform3fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglProgramUniform3fv(n, n2, floatBuffer.remaining() / 3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramUniform3fv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform4(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform4fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglProgramUniform4fv(n, n2, floatBuffer.remaining() >> 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramUniform4fv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform1(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform1dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglProgramUniform1dv(n, n2, doubleBuffer.remaining(), MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniform1dv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform2(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform2dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglProgramUniform2dv(n, n2, doubleBuffer.remaining() >> 1, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniform2dv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform3(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform3dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglProgramUniform3dv(n, n2, doubleBuffer.remaining() / 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniform3dv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform4(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform4dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglProgramUniform4dv(n, n2, doubleBuffer.remaining() >> 2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniform4dv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform1ui(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform1ui;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform1ui(n, n2, n3, l);
    }

    static native void nglProgramUniform1ui(int var0, int var1, int var2, long var3);

    public static void glProgramUniform2ui(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform2ui;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform2ui(n, n2, n3, n4, l);
    }

    static native void nglProgramUniform2ui(int var0, int var1, int var2, int var3, long var4);

    public static void glProgramUniform3ui(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform3ui;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform3ui(n, n2, n3, n4, n5, l);
    }

    static native void nglProgramUniform3ui(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glProgramUniform4ui(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform4ui;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglProgramUniform4ui(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglProgramUniform4ui(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glProgramUniform1u(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform1uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL41.nglProgramUniform1uiv(n, n2, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramUniform1uiv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform2u(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform2uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL41.nglProgramUniform2uiv(n, n2, intBuffer.remaining() >> 1, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramUniform2uiv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform3u(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform3uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL41.nglProgramUniform3uiv(n, n2, intBuffer.remaining() / 3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramUniform3uiv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniform4u(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniform4uiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL41.nglProgramUniform4uiv(n, n2, intBuffer.remaining() >> 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramUniform4uiv(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramUniformMatrix2(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix2fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglProgramUniformMatrix2fv(n, n2, floatBuffer.remaining() >> 2, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramUniformMatrix2fv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix3(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix3fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglProgramUniformMatrix3fv(n, n2, floatBuffer.remaining() / 9, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramUniformMatrix3fv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix4(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix4fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglProgramUniformMatrix4fv(n, n2, floatBuffer.remaining() >> 4, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramUniformMatrix4fv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix2(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix2dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglProgramUniformMatrix2dv(n, n2, doubleBuffer.remaining() >> 2, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix2dv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix3(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix3dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglProgramUniformMatrix3dv(n, n2, doubleBuffer.remaining() / 9, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix3dv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix4(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix4dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglProgramUniformMatrix4dv(n, n2, doubleBuffer.remaining() >> 4, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix4dv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix2x3(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix2x3fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglProgramUniformMatrix2x3fv(n, n2, floatBuffer.remaining() / 6, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramUniformMatrix2x3fv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix3x2(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix3x2fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglProgramUniformMatrix3x2fv(n, n2, floatBuffer.remaining() / 6, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramUniformMatrix3x2fv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix2x4(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix2x4fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglProgramUniformMatrix2x4fv(n, n2, floatBuffer.remaining() >> 3, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramUniformMatrix2x4fv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix4x2(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix4x2fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglProgramUniformMatrix4x2fv(n, n2, floatBuffer.remaining() >> 3, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramUniformMatrix4x2fv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix3x4(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix3x4fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglProgramUniformMatrix3x4fv(n, n2, floatBuffer.remaining() / 12, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramUniformMatrix3x4fv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix4x3(int n, int n2, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix4x3fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglProgramUniformMatrix4x3fv(n, n2, floatBuffer.remaining() / 12, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramUniformMatrix4x3fv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix2x3(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix2x3dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglProgramUniformMatrix2x3dv(n, n2, doubleBuffer.remaining() / 6, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix2x3dv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix3x2(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix3x2dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglProgramUniformMatrix3x2dv(n, n2, doubleBuffer.remaining() / 6, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix3x2dv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix2x4(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix2x4dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglProgramUniformMatrix2x4dv(n, n2, doubleBuffer.remaining() >> 3, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix2x4dv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix4x2(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix4x2dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglProgramUniformMatrix4x2dv(n, n2, doubleBuffer.remaining() >> 3, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix4x2dv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix3x4(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix3x4dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglProgramUniformMatrix3x4dv(n, n2, doubleBuffer.remaining() / 12, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix3x4dv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glProgramUniformMatrix4x3(int n, int n2, boolean bl, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramUniformMatrix4x3dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglProgramUniformMatrix4x3dv(n, n2, doubleBuffer.remaining() / 12, bl, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglProgramUniformMatrix4x3dv(int var0, int var1, int var2, boolean var3, long var4, long var6);

    public static void glValidateProgramPipeline(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glValidateProgramPipeline;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglValidateProgramPipeline(n, l);
    }

    static native void nglValidateProgramPipeline(int var0, long var1);

    public static void glGetProgramPipelineInfoLog(int n, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramPipelineInfoLog;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(byteBuffer);
        GL41.nglGetProgramPipelineInfoLog(n, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetProgramPipelineInfoLog(int var0, int var1, long var2, long var4, long var6);

    public static String glGetProgramPipelineInfoLog(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramPipelineInfoLog;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n2);
        GL41.nglGetProgramPipelineInfoLog(n, n2, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static void glVertexAttribL1d(int n, double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL1d;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglVertexAttribL1d(n, d, l);
    }

    static native void nglVertexAttribL1d(int var0, double var1, long var3);

    public static void glVertexAttribL2d(int n, double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL2d;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglVertexAttribL2d(n, d, d2, l);
    }

    static native void nglVertexAttribL2d(int var0, double var1, double var3, long var5);

    public static void glVertexAttribL3d(int n, double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL3d;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglVertexAttribL3d(n, d, d2, d3, l);
    }

    static native void nglVertexAttribL3d(int var0, double var1, double var3, double var5, long var7);

    public static void glVertexAttribL4d(int n, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL4d;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglVertexAttribL4d(n, d, d2, d3, d4, l);
    }

    static native void nglVertexAttribL4d(int var0, double var1, double var3, double var5, double var7, long var9);

    public static void glVertexAttribL1(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL1dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 1);
        GL41.nglVertexAttribL1dv(n, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVertexAttribL1dv(int var0, long var1, long var3);

    public static void glVertexAttribL2(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL2dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 2);
        GL41.nglVertexAttribL2dv(n, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVertexAttribL2dv(int var0, long var1, long var3);

    public static void glVertexAttribL3(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL3dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 3);
        GL41.nglVertexAttribL3dv(n, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVertexAttribL3dv(int var0, long var1, long var3);

    public static void glVertexAttribL4(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL4dv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        GL41.nglVertexAttribL4dv(n, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVertexAttribL4dv(int var0, long var1, long var3);

    public static void glVertexAttribLPointer(int n, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribLPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = doubleBuffer;
        }
        GL41.nglVertexAttribLPointer(n, n2, 5130, n3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVertexAttribLPointer(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glVertexAttribLPointer(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glVertexAttribLPointer;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        GL41.nglVertexAttribLPointerBO(n, n2, 5130, n3, l, l2);
    }

    static native void nglVertexAttribLPointerBO(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glGetVertexAttribL(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribLdv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        GL41.nglGetVertexAttribLdv(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetVertexAttribLdv(int var0, int var1, long var2, long var4);

    public static void glViewportArray(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glViewportArrayv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglViewportArrayv(n, floatBuffer.remaining() >> 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglViewportArrayv(int var0, int var1, long var2, long var4);

    public static void glViewportIndexedf(int n, float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glViewportIndexedf;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglViewportIndexedf(n, f, f2, f3, f4, l);
    }

    static native void nglViewportIndexedf(int var0, float var1, float var2, float var3, float var4, long var5);

    public static void glViewportIndexed(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glViewportIndexedfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL41.nglViewportIndexedfv(n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglViewportIndexedfv(int var0, long var1, long var3);

    public static void glScissorArray(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glScissorArrayv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL41.nglScissorArrayv(n, intBuffer.remaining() >> 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglScissorArrayv(int var0, int var1, long var2, long var4);

    public static void glScissorIndexed(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glScissorIndexed;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglScissorIndexed(n, n2, n3, n4, n5, l);
    }

    static native void nglScissorIndexed(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glScissorIndexed(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glScissorIndexedv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL41.nglScissorIndexedv(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglScissorIndexedv(int var0, long var1, long var3);

    public static void glDepthRangeArray(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDepthRangeArrayv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglDepthRangeArrayv(n, doubleBuffer.remaining() >> 1, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglDepthRangeArrayv(int var0, int var1, long var2, long var4);

    public static void glDepthRangeIndexed(int n, double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDepthRangeIndexed;
        BufferChecks.checkFunctionAddress(l);
        GL41.nglDepthRangeIndexed(n, d, d2, l);
    }

    static native void nglDepthRangeIndexed(int var0, double var1, double var3, long var5);

    public static void glGetFloat(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetFloati_v;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL41.nglGetFloati_v(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetFloati_v(int var0, int var1, long var2, long var4);

    public static float glGetFloat(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetFloati_v;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        GL41.nglGetFloati_v(n, n2, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetDouble(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetDoublei_v;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL41.nglGetDoublei_v(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetDoublei_v(int var0, int var1, long var2, long var4);

    public static double glGetDouble(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetDoublei_v;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferDouble((ContextCapabilities)object);
        GL41.nglGetDoublei_v(n, n2, MemoryUtil.getAddress((DoubleBuffer)object), l);
        return ((DoubleBuffer)object).get(0);
    }
}
