/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVEvaluators {
    public static final int GL_EVAL_2D_NV = 34496;
    public static final int GL_EVAL_TRIANGULAR_2D_NV = 34497;
    public static final int GL_MAP_TESSELLATION_NV = 34498;
    public static final int GL_MAP_ATTRIB_U_ORDER_NV = 34499;
    public static final int GL_MAP_ATTRIB_V_ORDER_NV = 34500;
    public static final int GL_EVAL_FRACTIONAL_TESSELLATION_NV = 34501;
    public static final int GL_EVAL_VERTEX_ATTRIB0_NV = 34502;
    public static final int GL_EVAL_VERTEX_ATTRIB1_NV = 34503;
    public static final int GL_EVAL_VERTEX_ATTRIB2_NV = 34504;
    public static final int GL_EVAL_VERTEX_ATTRIB3_NV = 34505;
    public static final int GL_EVAL_VERTEX_ATTRIB4_NV = 34506;
    public static final int GL_EVAL_VERTEX_ATTRIB5_NV = 34507;
    public static final int GL_EVAL_VERTEX_ATTRIB6_NV = 34508;
    public static final int GL_EVAL_VERTEX_ATTRIB7_NV = 34509;
    public static final int GL_EVAL_VERTEX_ATTRIB8_NV = 34510;
    public static final int GL_EVAL_VERTEX_ATTRIB9_NV = 34511;
    public static final int GL_EVAL_VERTEX_ATTRIB10_NV = 34512;
    public static final int GL_EVAL_VERTEX_ATTRIB11_NV = 34513;
    public static final int GL_EVAL_VERTEX_ATTRIB12_NV = 34514;
    public static final int GL_EVAL_VERTEX_ATTRIB13_NV = 34515;
    public static final int GL_EVAL_VERTEX_ATTRIB14_NV = 34516;
    public static final int GL_EVAL_VERTEX_ATTRIB15_NV = 34517;
    public static final int GL_MAX_MAP_TESSELLATION_NV = 34518;
    public static final int GL_MAX_RATIONAL_EVAL_ORDER_NV = 34519;

    private NVEvaluators() {
    }

    public static void glGetMapControlPointsNV(int n, int n2, int n3, int n4, int n5, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMapControlPointsNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        NVEvaluators.nglGetMapControlPointsNV(n, n2, n3, n4, n5, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetMapControlPointsNV(int var0, int var1, int var2, int var3, int var4, boolean var5, long var6, long var8);

    public static void glMapControlPointsNV(int n, int n2, int n3, int n4, int n5, int n6, int n7, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMapControlPointsNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        NVEvaluators.nglMapControlPointsNV(n, n2, n3, n4, n5, n6, n7, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglMapControlPointsNV(int var0, int var1, int var2, int var3, int var4, int var5, int var6, boolean var7, long var8, long var10);

    public static void glMapParameterNV(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMapParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVEvaluators.nglMapParameterfvNV(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglMapParameterfvNV(int var0, int var1, long var2, long var4);

    public static void glMapParameterNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMapParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVEvaluators.nglMapParameterivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglMapParameterivNV(int var0, int var1, long var2, long var4);

    public static void glGetMapParameterNV(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMapParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVEvaluators.nglGetMapParameterfvNV(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetMapParameterfvNV(int var0, int var1, long var2, long var4);

    public static void glGetMapParameterNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMapParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVEvaluators.nglGetMapParameterivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetMapParameterivNV(int var0, int var1, long var2, long var4);

    public static void glGetMapAttribParameterNV(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMapAttribParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVEvaluators.nglGetMapAttribParameterfvNV(n, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetMapAttribParameterfvNV(int var0, int var1, int var2, long var3, long var5);

    public static void glGetMapAttribParameterNV(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetMapAttribParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVEvaluators.nglGetMapAttribParameterivNV(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetMapAttribParameterivNV(int var0, int var1, int var2, long var3, long var5);

    public static void glEvalMapsNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEvalMapsNV;
        BufferChecks.checkFunctionAddress(l);
        NVEvaluators.nglEvalMapsNV(n, n2, l);
    }

    static native void nglEvalMapsNV(int var0, int var1, long var2);
}
