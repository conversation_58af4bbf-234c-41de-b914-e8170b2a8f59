/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.opengl.ARBProgram;
import org.lwjgl.opengl.ARBVertexShader;

public final class ARBVertexProgram
extends ARBProgram {
    public static final int GL_VERTEX_PROGRAM_ARB = 34336;
    public static final int GL_VERTEX_PROGRAM_POINT_SIZE_ARB = 34370;
    public static final int GL_VERTEX_PROGRAM_TWO_SIDE_ARB = 34371;
    public static final int GL_COLOR_SUM_ARB = 33880;
    public static final int GL_VERTEX_ATTRIB_ARRAY_ENABLED_ARB = 34338;
    public static final int GL_VERTEX_ATTRIB_ARRAY_SIZE_ARB = 34339;
    public static final int GL_VERTEX_ATTRIB_ARRAY_STRIDE_ARB = 34340;
    public static final int GL_VERTEX_ATTRIB_ARRAY_TYPE_ARB = 34341;
    public static final int GL_VERTEX_ATTRIB_ARRAY_NORMALIZED_ARB = 34922;
    public static final int GL_CURRENT_VERTEX_ATTRIB_ARB = 34342;
    public static final int GL_VERTEX_ATTRIB_ARRAY_POINTER_ARB = 34373;
    public static final int GL_PROGRAM_ADDRESS_REGISTERS_ARB = 34992;
    public static final int GL_MAX_PROGRAM_ADDRESS_REGISTERS_ARB = 34993;
    public static final int GL_PROGRAM_NATIVE_ADDRESS_REGISTERS_ARB = 34994;
    public static final int GL_MAX_PROGRAM_NATIVE_ADDRESS_REGISTERS_ARB = 34995;
    public static final int GL_MAX_VERTEX_ATTRIBS_ARB = 34921;

    private ARBVertexProgram() {
    }

    public static void glVertexAttrib1sARB(int n, short s) {
        ARBVertexShader.glVertexAttrib1sARB(n, s);
    }

    public static void glVertexAttrib1fARB(int n, float f) {
        ARBVertexShader.glVertexAttrib1fARB(n, f);
    }

    public static void glVertexAttrib1dARB(int n, double d) {
        ARBVertexShader.glVertexAttrib1dARB(n, d);
    }

    public static void glVertexAttrib2sARB(int n, short s, short s2) {
        ARBVertexShader.glVertexAttrib2sARB(n, s, s2);
    }

    public static void glVertexAttrib2fARB(int n, float f, float f2) {
        ARBVertexShader.glVertexAttrib2fARB(n, f, f2);
    }

    public static void glVertexAttrib2dARB(int n, double d, double d2) {
        ARBVertexShader.glVertexAttrib2dARB(n, d, d2);
    }

    public static void glVertexAttrib3sARB(int n, short s, short s2, short s3) {
        ARBVertexShader.glVertexAttrib3sARB(n, s, s2, s3);
    }

    public static void glVertexAttrib3fARB(int n, float f, float f2, float f3) {
        ARBVertexShader.glVertexAttrib3fARB(n, f, f2, f3);
    }

    public static void glVertexAttrib3dARB(int n, double d, double d2, double d3) {
        ARBVertexShader.glVertexAttrib3dARB(n, d, d2, d3);
    }

    public static void glVertexAttrib4sARB(int n, short s, short s2, short s3, short s4) {
        ARBVertexShader.glVertexAttrib4sARB(n, s, s2, s3, s4);
    }

    public static void glVertexAttrib4fARB(int n, float f, float f2, float f3, float f4) {
        ARBVertexShader.glVertexAttrib4fARB(n, f, f2, f3, f4);
    }

    public static void glVertexAttrib4dARB(int n, double d, double d2, double d3, double d4) {
        ARBVertexShader.glVertexAttrib4dARB(n, d, d2, d3, d4);
    }

    public static void glVertexAttrib4NubARB(int n, byte by, byte by2, byte by3, byte by4) {
        ARBVertexShader.glVertexAttrib4NubARB(n, by, by2, by3, by4);
    }

    public static void glVertexAttribPointerARB(int n, int n2, boolean bl, int n3, DoubleBuffer doubleBuffer) {
        ARBVertexShader.glVertexAttribPointerARB(n, n2, bl, n3, doubleBuffer);
    }

    public static void glVertexAttribPointerARB(int n, int n2, boolean bl, int n3, FloatBuffer floatBuffer) {
        ARBVertexShader.glVertexAttribPointerARB(n, n2, bl, n3, floatBuffer);
    }

    public static void glVertexAttribPointerARB(int n, int n2, boolean bl, boolean bl2, int n3, ByteBuffer byteBuffer) {
        ARBVertexShader.glVertexAttribPointerARB(n, n2, bl, bl2, n3, byteBuffer);
    }

    public static void glVertexAttribPointerARB(int n, int n2, boolean bl, boolean bl2, int n3, IntBuffer intBuffer) {
        ARBVertexShader.glVertexAttribPointerARB(n, n2, bl, bl2, n3, intBuffer);
    }

    public static void glVertexAttribPointerARB(int n, int n2, boolean bl, boolean bl2, int n3, ShortBuffer shortBuffer) {
        ARBVertexShader.glVertexAttribPointerARB(n, n2, bl, bl2, n3, shortBuffer);
    }

    public static void glVertexAttribPointerARB(int n, int n2, int n3, boolean bl, int n4, long l) {
        ARBVertexShader.glVertexAttribPointerARB(n, n2, n3, bl, n4, l);
    }

    public static void glEnableVertexAttribArrayARB(int n) {
        ARBVertexShader.glEnableVertexAttribArrayARB(n);
    }

    public static void glDisableVertexAttribArrayARB(int n) {
        ARBVertexShader.glDisableVertexAttribArrayARB(n);
    }

    public static void glGetVertexAttribARB(int n, int n2, FloatBuffer floatBuffer) {
        ARBVertexShader.glGetVertexAttribARB(n, n2, floatBuffer);
    }

    public static void glGetVertexAttribARB(int n, int n2, DoubleBuffer doubleBuffer) {
        ARBVertexShader.glGetVertexAttribARB(n, n2, doubleBuffer);
    }

    public static void glGetVertexAttribARB(int n, int n2, IntBuffer intBuffer) {
        ARBVertexShader.glGetVertexAttribARB(n, n2, intBuffer);
    }

    public static ByteBuffer glGetVertexAttribPointerARB(int n, int n2, long l) {
        return ARBVertexShader.glGetVertexAttribPointerARB(n, n2, l);
    }
}
