/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GL43;
import org.lwjgl.opengl.GLContext;

public final class ARBTextureBufferRange {
    public static final int GL_TEXTURE_BUFFER_OFFSET = 37277;
    public static final int GL_TEXTURE_BUFFER_SIZE = 37278;
    public static final int GL_TEXTURE_BUFFER_OFFSET_ALIGNMENT = 37279;

    private ARBTextureBufferRange() {
    }

    public static void glTexBufferRange(int n, int n2, int n3, long l, long l2) {
        GL43.glTexBufferRange(n, n2, n3, l, l2);
    }

    public static void glTextureBufferRangeEXT(int n, int n2, int n3, int n4, long l, long l2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glTextureBufferRangeEXT;
        BufferChecks.checkFunctionAddress(l3);
        ARBTextureBufferRange.nglTextureBufferRangeEXT(n, n2, n3, n4, l, l2, l3);
    }

    static native void nglTextureBufferRangeEXT(int var0, int var1, int var2, int var3, long var4, long var6, long var8);
}
