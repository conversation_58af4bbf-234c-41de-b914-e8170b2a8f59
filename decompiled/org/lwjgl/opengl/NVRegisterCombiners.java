/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVRegisterCombiners {
    public static final int GL_REGISTER_COMBINERS_NV = 34082;
    public static final int GL_COMBINER0_NV = 34128;
    public static final int GL_COMBINER1_NV = 34129;
    public static final int GL_COMBINER2_NV = 34130;
    public static final int GL_COMBINER3_NV = 34131;
    public static final int GL_COMBINER4_NV = 34132;
    public static final int GL_COMBINER5_NV = 34133;
    public static final int GL_COMBINER6_NV = 34134;
    public static final int GL_COMBINER7_NV = 34135;
    public static final int GL_VARIABLE_A_NV = 34083;
    public static final int GL_VARIABLE_B_NV = 34084;
    public static final int GL_VARIABLE_C_NV = 34085;
    public static final int GL_VARIABLE_D_NV = 34086;
    public static final int GL_VARIABLE_E_NV = 34087;
    public static final int GL_VARIABLE_F_NV = 34088;
    public static final int GL_VARIABLE_G_NV = 34089;
    public static final int GL_CONSTANT_COLOR0_NV = 34090;
    public static final int GL_CONSTANT_COLOR1_NV = 34091;
    public static final int GL_PRIMARY_COLOR_NV = 34092;
    public static final int GL_SECONDARY_COLOR_NV = 34093;
    public static final int GL_SPARE0_NV = 34094;
    public static final int GL_SPARE1_NV = 34095;
    public static final int GL_UNSIGNED_IDENTITY_NV = 34102;
    public static final int GL_UNSIGNED_INVERT_NV = 34103;
    public static final int GL_EXPAND_NORMAL_NV = 34104;
    public static final int GL_EXPAND_NEGATE_NV = 34105;
    public static final int GL_HALF_BIAS_NORMAL_NV = 34106;
    public static final int GL_HALF_BIAS_NEGATE_NV = 34107;
    public static final int GL_SIGNED_IDENTITY_NV = 34108;
    public static final int GL_SIGNED_NEGATE_NV = 34109;
    public static final int GL_E_TIMES_F_NV = 34097;
    public static final int GL_SPARE0_PLUS_SECONDARY_COLOR_NV = 34098;
    public static final int GL_SCALE_BY_TWO_NV = 34110;
    public static final int GL_SCALE_BY_FOUR_NV = 34111;
    public static final int GL_SCALE_BY_ONE_HALF_NV = 34112;
    public static final int GL_BIAS_BY_NEGATIVE_ONE_HALF_NV = 34113;
    public static final int GL_DISCARD_NV = 34096;
    public static final int GL_COMBINER_INPUT_NV = 34114;
    public static final int GL_COMBINER_MAPPING_NV = 34115;
    public static final int GL_COMBINER_COMPONENT_USAGE_NV = 34116;
    public static final int GL_COMBINER_AB_DOT_PRODUCT_NV = 34117;
    public static final int GL_COMBINER_CD_DOT_PRODUCT_NV = 34118;
    public static final int GL_COMBINER_MUX_SUM_NV = 34119;
    public static final int GL_COMBINER_SCALE_NV = 34120;
    public static final int GL_COMBINER_BIAS_NV = 34121;
    public static final int GL_COMBINER_AB_OUTPUT_NV = 34122;
    public static final int GL_COMBINER_CD_OUTPUT_NV = 34123;
    public static final int GL_COMBINER_SUM_OUTPUT_NV = 34124;
    public static final int GL_NUM_GENERAL_COMBINERS_NV = 34126;
    public static final int GL_COLOR_SUM_CLAMP_NV = 34127;
    public static final int GL_MAX_GENERAL_COMBINERS_NV = 34125;

    private NVRegisterCombiners() {
    }

    public static void glCombinerParameterfNV(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCombinerParameterfNV;
        BufferChecks.checkFunctionAddress(l);
        NVRegisterCombiners.nglCombinerParameterfNV(n, f, l);
    }

    static native void nglCombinerParameterfNV(int var0, float var1, long var2);

    public static void glCombinerParameterNV(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCombinerParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVRegisterCombiners.nglCombinerParameterfvNV(n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglCombinerParameterfvNV(int var0, long var1, long var3);

    public static void glCombinerParameteriNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCombinerParameteriNV;
        BufferChecks.checkFunctionAddress(l);
        NVRegisterCombiners.nglCombinerParameteriNV(n, n2, l);
    }

    static native void nglCombinerParameteriNV(int var0, int var1, long var2);

    public static void glCombinerParameterNV(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCombinerParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVRegisterCombiners.nglCombinerParameterivNV(n, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglCombinerParameterivNV(int var0, long var1, long var3);

    public static void glCombinerInputNV(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCombinerInputNV;
        BufferChecks.checkFunctionAddress(l);
        NVRegisterCombiners.nglCombinerInputNV(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglCombinerInputNV(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glCombinerOutputNV(int n, int n2, int n3, int n4, int n5, int n6, int n7, boolean bl, boolean bl2, boolean bl3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCombinerOutputNV;
        BufferChecks.checkFunctionAddress(l);
        NVRegisterCombiners.nglCombinerOutputNV(n, n2, n3, n4, n5, n6, n7, bl, bl2, bl3, l);
    }

    static native void nglCombinerOutputNV(int var0, int var1, int var2, int var3, int var4, int var5, int var6, boolean var7, boolean var8, boolean var9, long var10);

    public static void glFinalCombinerInputNV(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFinalCombinerInputNV;
        BufferChecks.checkFunctionAddress(l);
        NVRegisterCombiners.nglFinalCombinerInputNV(n, n2, n3, n4, l);
    }

    static native void nglFinalCombinerInputNV(int var0, int var1, int var2, int var3, long var4);

    public static void glGetCombinerInputParameterNV(int n, int n2, int n3, int n4, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCombinerInputParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVRegisterCombiners.nglGetCombinerInputParameterfvNV(n, n2, n3, n4, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetCombinerInputParameterfvNV(int var0, int var1, int var2, int var3, long var4, long var6);

    public static float glGetCombinerInputParameterfNV(int n, int n2, int n3, int n4) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetCombinerInputParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        NVRegisterCombiners.nglGetCombinerInputParameterfvNV(n, n2, n3, n4, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetCombinerInputParameterNV(int n, int n2, int n3, int n4, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCombinerInputParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVRegisterCombiners.nglGetCombinerInputParameterivNV(n, n2, n3, n4, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetCombinerInputParameterivNV(int var0, int var1, int var2, int var3, long var4, long var6);

    public static int glGetCombinerInputParameteriNV(int n, int n2, int n3, int n4) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetCombinerInputParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        NVRegisterCombiners.nglGetCombinerInputParameterivNV(n, n2, n3, n4, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetCombinerOutputParameterNV(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCombinerOutputParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVRegisterCombiners.nglGetCombinerOutputParameterfvNV(n, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetCombinerOutputParameterfvNV(int var0, int var1, int var2, long var3, long var5);

    public static float glGetCombinerOutputParameterfNV(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetCombinerOutputParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        NVRegisterCombiners.nglGetCombinerOutputParameterfvNV(n, n2, n3, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetCombinerOutputParameterNV(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCombinerOutputParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVRegisterCombiners.nglGetCombinerOutputParameterivNV(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetCombinerOutputParameterivNV(int var0, int var1, int var2, long var3, long var5);

    public static int glGetCombinerOutputParameteriNV(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetCombinerOutputParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        NVRegisterCombiners.nglGetCombinerOutputParameterivNV(n, n2, n3, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetFinalCombinerInputParameterNV(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetFinalCombinerInputParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVRegisterCombiners.nglGetFinalCombinerInputParameterfvNV(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetFinalCombinerInputParameterfvNV(int var0, int var1, long var2, long var4);

    public static float glGetFinalCombinerInputParameterfNV(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetFinalCombinerInputParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        NVRegisterCombiners.nglGetFinalCombinerInputParameterfvNV(n, n2, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetFinalCombinerInputParameterNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetFinalCombinerInputParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVRegisterCombiners.nglGetFinalCombinerInputParameterivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetFinalCombinerInputParameterivNV(int var0, int var1, long var2, long var4);

    public static int glGetFinalCombinerInputParameteriNV(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetFinalCombinerInputParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        NVRegisterCombiners.nglGetFinalCombinerInputParameterivNV(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }
}
