/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.lwjgl.opengles.PixelFormat
 */
package org.lwjgl.opengl;

import java.awt.Canvas;
import java.awt.KeyboardFocusManager;
import java.awt.event.FocusAdapter;
import java.awt.event.FocusEvent;
import java.lang.reflect.Method;
import java.nio.Buffer;
import java.nio.ByteBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.util.concurrent.atomic.AtomicBoolean;
import javax.swing.SwingUtilities;
import org.lwjgl.BufferUtils;
import org.lwjgl.LWJGLException;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.input.Mouse;
import org.lwjgl.opengl.AWTCanvasImplementation;
import org.lwjgl.opengl.AWTGLCanvas;
import org.lwjgl.opengl.Context;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.Display;
import org.lwjgl.opengl.DisplayImplementation;
import org.lwjgl.opengl.DisplayMode;
import org.lwjgl.opengl.DrawableGL;
import org.lwjgl.opengl.DrawableGLES;
import org.lwjgl.opengl.DrawableLWJGL;
import org.lwjgl.opengl.PeerInfo;
import org.lwjgl.opengl.WindowsDisplayPeerInfo;
import org.lwjgl.opengl.WindowsFileVersion;
import org.lwjgl.opengl.WindowsKeyboard;
import org.lwjgl.opengl.WindowsMouse;
import org.lwjgl.opengl.WindowsPbufferPeerInfo;
import org.lwjgl.opengl.WindowsPeerInfo;
import org.lwjgl.opengl.WindowsRegistry;
import org.lwjgl.opengles.PixelFormat;

final class WindowsDisplay
implements DisplayImplementation {
    private static final int GAMMA_LENGTH = 256;
    private static final int WM_WINDOWPOSCHANGED = 71;
    private static final int WM_MOVE = 3;
    private static final int WM_CANCELMODE = 31;
    private static final int WM_MOUSEMOVE = 512;
    private static final int WM_LBUTTONDOWN = 513;
    private static final int WM_LBUTTONUP = 514;
    private static final int WM_LBUTTONDBLCLK = 515;
    private static final int WM_RBUTTONDOWN = 516;
    private static final int WM_RBUTTONUP = 517;
    private static final int WM_RBUTTONDBLCLK = 518;
    private static final int WM_MBUTTONDOWN = 519;
    private static final int WM_MBUTTONUP = 520;
    private static final int WM_MBUTTONDBLCLK = 521;
    private static final int WM_XBUTTONDOWN = 523;
    private static final int WM_XBUTTONUP = 524;
    private static final int WM_XBUTTONDBLCLK = 525;
    private static final int WM_MOUSEWHEEL = 522;
    private static final int WM_CAPTURECHANGED = 533;
    private static final int WM_MOUSELEAVE = 675;
    private static final int WM_ENTERSIZEMOVE = 561;
    private static final int WM_EXITSIZEMOVE = 562;
    private static final int WM_SIZING = 532;
    private static final int WM_KEYDOWN = 256;
    private static final int WM_KEYUP = 257;
    private static final int WM_SYSKEYUP = 261;
    private static final int WM_SYSKEYDOWN = 260;
    private static final int WM_SYSCHAR = 262;
    private static final int WM_CHAR = 258;
    private static final int WM_GETICON = 127;
    private static final int WM_SETICON = 128;
    private static final int WM_SETCURSOR = 32;
    private static final int WM_MOUSEACTIVATE = 33;
    private static final int WM_QUIT = 18;
    private static final int WM_SYSCOMMAND = 274;
    private static final int WM_PAINT = 15;
    private static final int WM_KILLFOCUS = 8;
    private static final int WM_SETFOCUS = 7;
    private static final int SC_SIZE = 61440;
    private static final int SC_MOVE = 61456;
    private static final int SC_MINIMIZE = 61472;
    private static final int SC_MAXIMIZE = 61488;
    private static final int SC_NEXTWINDOW = 61504;
    private static final int SC_PREVWINDOW = 61520;
    private static final int SC_CLOSE = 61536;
    private static final int SC_VSCROLL = 61552;
    private static final int SC_HSCROLL = 61568;
    private static final int SC_MOUSEMENU = 61584;
    private static final int SC_KEYMENU = 61696;
    private static final int SC_ARRANGE = 61712;
    private static final int SC_RESTORE = 61728;
    private static final int SC_TASKLIST = 61744;
    private static final int SC_SCREENSAVE = 61760;
    private static final int SC_HOTKEY = 61776;
    private static final int SC_DEFAULT = 61792;
    private static final int SC_MONITORPOWER = 61808;
    private static final int SC_CONTEXTHELP = 61824;
    private static final int SC_SEPARATOR = 61455;
    static final int SM_CXCURSOR = 13;
    static final int SM_CYCURSOR = 14;
    static final int SM_CMOUSEBUTTONS = 43;
    static final int SM_MOUSEWHEELPRESENT = 75;
    private static final int SIZE_RESTORED = 0;
    private static final int SIZE_MINIMIZED = 1;
    private static final int SIZE_MAXIMIZED = 2;
    private static final int WM_SIZE = 5;
    private static final int WM_ACTIVATE = 6;
    private static final int WA_INACTIVE = 0;
    private static final int WA_ACTIVE = 1;
    private static final int WA_CLICKACTIVE = 2;
    private static final int SW_NORMAL = 1;
    private static final int SW_SHOWMINNOACTIVE = 7;
    private static final int SW_SHOWDEFAULT = 10;
    private static final int SW_RESTORE = 9;
    private static final int SW_MAXIMIZE = 3;
    private static final int ICON_SMALL = 0;
    private static final int ICON_BIG = 1;
    private static final IntBuffer rect_buffer = BufferUtils.createIntBuffer(4);
    private static final Rect rect = new Rect();
    private static final long HWND_TOP = 0L;
    private static final long HWND_BOTTOM = 1L;
    private static final long HWND_TOPMOST = -1L;
    private static final long HWND_NOTOPMOST = -2L;
    private static final int SWP_NOSIZE = 1;
    private static final int SWP_NOMOVE = 2;
    private static final int SWP_NOZORDER = 4;
    private static final int SWP_FRAMECHANGED = 32;
    private static final int GWL_STYLE = -16;
    private static final int GWL_EXSTYLE = -20;
    private static final int WS_THICKFRAME = 262144;
    private static final int WS_MAXIMIZEBOX = 65536;
    private static final int HTCLIENT = 1;
    private static final int MK_XBUTTON1 = 32;
    private static final int MK_XBUTTON2 = 64;
    private static final int XBUTTON1 = 1;
    private static final int XBUTTON2 = 2;
    private static WindowsDisplay current_display;
    private static boolean cursor_clipped;
    private WindowsDisplayPeerInfo peer_info;
    private Object current_cursor;
    private static boolean hasParent;
    private Canvas parent;
    private long parent_hwnd;
    private FocusAdapter parent_focus_tracker;
    private AtomicBoolean parent_focused;
    private WindowsKeyboard keyboard;
    private WindowsMouse mouse;
    private boolean close_requested;
    private boolean is_dirty;
    private ByteBuffer current_gamma;
    private ByteBuffer saved_gamma;
    private DisplayMode current_mode;
    private boolean mode_set;
    private boolean isMinimized;
    private boolean isFocused;
    private boolean redoMakeContextCurrent;
    private boolean inAppActivate;
    private boolean resized;
    private boolean resizable;
    private int x;
    private int y;
    private int width;
    private int height;
    private long hwnd;
    private long hdc;
    private long small_icon;
    private long large_icon;
    private boolean iconsLoaded;
    private int captureMouse = -1;
    private boolean mouseInside;

    WindowsDisplay() {
        current_display = this;
    }

    public final void createWindow(DrawableLWJGL drawableLWJGL, DisplayMode displayMode, Canvas canvas, int n, int n2) {
        this.parent = canvas;
        hasParent = canvas != null;
        this.parent_hwnd = canvas != null ? WindowsDisplay.getHwnd(canvas) : 0L;
        this.hwnd = WindowsDisplay.nCreateWindow(n, n2, displayMode.getWidth(), displayMode.getHeight(), Display.isFullscreen() || WindowsDisplay.isUndecorated(), canvas != null, this.parent_hwnd);
        if (Display.isResizable() && canvas == null) {
            this.setResizable(true);
        }
        if (this.hwnd == 0L) {
            throw new LWJGLException("Failed to create window");
        }
        this.hdc = WindowsDisplay.getDC(this.hwnd);
        if (this.hdc == 0L) {
            WindowsDisplay.nDestroyWindow(this.hwnd);
            throw new LWJGLException("Failed to get dc");
        }
        try {
            if (drawableLWJGL instanceof DrawableGL) {
                int n3 = WindowsPeerInfo.choosePixelFormat(this.getHdc(), 0, 0, (org.lwjgl.opengl.PixelFormat)drawableLWJGL.getPixelFormat(), null, true, true, false, true);
                WindowsPeerInfo.setPixelFormat(this.getHdc(), n3);
            } else {
                this.peer_info = new WindowsDisplayPeerInfo(true);
                ((DrawableGLES)drawableLWJGL).initialize(this.hwnd, this.hdc, 4, (PixelFormat)drawableLWJGL.getPixelFormat());
            }
            this.peer_info.initDC(this.getHwnd(), this.getHdc());
            WindowsDisplay.showWindow(this.getHwnd(), 10);
            this.updateWidthAndHeight();
            if (canvas == null) {
                WindowsDisplay.setForegroundWindow(this.getHwnd());
            } else {
                this.parent_focused = new AtomicBoolean(false);
                this.parent_focus_tracker = new FocusAdapter(){

                    public void focusGained(FocusEvent focusEvent) {
                        WindowsDisplay.this.parent_focused.set(true);
                        WindowsDisplay.this.clearAWTFocus();
                    }
                };
                canvas.addFocusListener(this.parent_focus_tracker);
                SwingUtilities.invokeLater(new Runnable(){

                    public void run() {
                        WindowsDisplay.this.clearAWTFocus();
                    }
                });
            }
            this.grabFocus();
            return;
        }
        catch (LWJGLException lWJGLException) {
            WindowsDisplay.nReleaseDC(this.hwnd, this.hdc);
            WindowsDisplay.nDestroyWindow(this.hwnd);
            throw lWJGLException;
        }
    }

    private void updateWidthAndHeight() {
        WindowsDisplay.getClientRect(this.hwnd, rect_buffer);
        rect.copyFromBuffer(rect_buffer);
        this.width = WindowsDisplay.rect.right - WindowsDisplay.rect.left;
        this.height = WindowsDisplay.rect.bottom - WindowsDisplay.rect.top;
    }

    private static native long nCreateWindow(int var0, int var1, int var2, int var3, boolean var4, boolean var5, long var6);

    private static boolean isUndecorated() {
        return Display.getPrivilegedBoolean("org.lwjgl.opengl.Window.undecorated");
    }

    private static long getHwnd(Canvas object) {
        AWTCanvasImplementation aWTCanvasImplementation = AWTGLCanvas.createImplementation();
        object = (WindowsPeerInfo)aWTCanvasImplementation.createPeerInfo((Canvas)object, null, null);
        ((PeerInfo)object).lockAndGetHandle();
        try {
            long l = ((WindowsPeerInfo)object).getHwnd();
            return l;
        }
        finally {
            ((PeerInfo)object).unlock();
        }
    }

    public final void destroyWindow() {
        if (this.parent != null) {
            this.parent.removeFocusListener(this.parent_focus_tracker);
            this.parent_focus_tracker = null;
        }
        WindowsDisplay.nReleaseDC(this.hwnd, this.hdc);
        WindowsDisplay.nDestroyWindow(this.hwnd);
        this.freeLargeIcon();
        this.freeSmallIcon();
        WindowsDisplay.resetCursorClipping();
        this.close_requested = false;
        this.is_dirty = false;
        this.isMinimized = false;
        this.isFocused = false;
        this.redoMakeContextCurrent = false;
        this.mouseInside = false;
    }

    private static native void nReleaseDC(long var0, long var2);

    private static native void nDestroyWindow(long var0);

    static void resetCursorClipping() {
        if (cursor_clipped) {
            try {
                WindowsDisplay.clipCursor(null);
            }
            catch (LWJGLException lWJGLException) {
                LWJGLUtil.log("Failed to reset cursor clipping: " + lWJGLException);
            }
            cursor_clipped = false;
        }
    }

    private static void getGlobalClientRect(long l, Rect rect) {
        rect_buffer.put(0, 0).put(1, 0);
        WindowsDisplay.clientToScreen(l, rect_buffer);
        int n = rect_buffer.get(0);
        int n2 = rect_buffer.get(1);
        WindowsDisplay.getClientRect(l, rect_buffer);
        rect.copyFromBuffer(rect_buffer);
        rect.offset(n, n2);
    }

    static void setupCursorClipping(long l) {
        cursor_clipped = true;
        WindowsDisplay.getGlobalClientRect(l, rect);
        rect.copyToBuffer(rect_buffer);
        WindowsDisplay.clipCursor(rect_buffer);
    }

    private static native void clipCursor(IntBuffer var0);

    public final void switchDisplayMode(DisplayMode displayMode) {
        WindowsDisplay.nSwitchDisplayMode(displayMode);
        this.current_mode = displayMode;
        this.mode_set = true;
    }

    private static native void nSwitchDisplayMode(DisplayMode var0);

    /*
     * Unable to fully structure code
     */
    private void appActivate(boolean var1_1, long var2_2) {
        block7: {
            block6: {
                if (this.inAppActivate) {
                    return;
                }
                this.inAppActivate = true;
                this.isFocused = var1_1;
                if (!var1_1) break block6;
                if (Display.isFullscreen()) {
                    this.restoreDisplayMode();
                }
                if (this.parent == null) {
                    WindowsDisplay.setForegroundWindow(this.getHwnd());
                }
                WindowsDisplay.setFocus(this.getHwnd());
                this.redoMakeContextCurrent = true;
                if (!Display.isFullscreen()) break block7;
                ** GOTO lbl-1000
            }
            if (this.keyboard != null) {
                this.keyboard.releaseAll(var2_2);
            }
            if (Display.isFullscreen()) {
                WindowsDisplay.showWindow(this.getHwnd(), 7);
                this.resetDisplayMode();
            } else lbl-1000:
            // 2 sources

            {
                this.updateClipping();
            }
        }
        this.updateCursor();
        this.inAppActivate = false;
    }

    private static native void showWindow(long var0, int var2);

    private static native void setForegroundWindow(long var0);

    private static native void setFocus(long var0);

    private void clearAWTFocus() {
        this.parent.setFocusable(false);
        this.parent.setFocusable(true);
        KeyboardFocusManager.getCurrentKeyboardFocusManager().clearGlobalFocusOwner();
    }

    private void grabFocus() {
        if (this.parent == null) {
            WindowsDisplay.setFocus(this.getHwnd());
            return;
        }
        SwingUtilities.invokeLater(new Runnable(){

            public void run() {
                WindowsDisplay.this.parent.requestFocus();
            }
        });
    }

    private void restoreDisplayMode() {
        try {
            WindowsDisplay windowsDisplay = this;
            windowsDisplay.doSetGammaRamp(windowsDisplay.current_gamma);
        }
        catch (LWJGLException lWJGLException) {
            LWJGLUtil.log("Failed to restore gamma: " + lWJGLException.getMessage());
        }
        if (!this.mode_set) {
            this.mode_set = true;
            try {
                WindowsDisplay.nSwitchDisplayMode(this.current_mode);
                return;
            }
            catch (LWJGLException lWJGLException) {
                LWJGLUtil.log("Failed to restore display mode: " + lWJGLException.getMessage());
            }
        }
    }

    public final void resetDisplayMode() {
        try {
            WindowsDisplay windowsDisplay = this;
            windowsDisplay.doSetGammaRamp(windowsDisplay.saved_gamma);
        }
        catch (LWJGLException lWJGLException) {
            LWJGLUtil.log("Failed to reset gamma ramp: " + lWJGLException.getMessage());
        }
        this.current_gamma = this.saved_gamma;
        if (this.mode_set) {
            this.mode_set = false;
            WindowsDisplay.nResetDisplayMode();
        }
        WindowsDisplay.resetCursorClipping();
    }

    private static native void nResetDisplayMode();

    public final int getGammaRampLength() {
        return 256;
    }

    public final void setGammaRamp(FloatBuffer floatBuffer) {
        this.doSetGammaRamp(WindowsDisplay.convertToNativeRamp(floatBuffer));
    }

    private static native ByteBuffer convertToNativeRamp(FloatBuffer var0);

    private static native ByteBuffer getCurrentGammaRamp();

    private void doSetGammaRamp(ByteBuffer byteBuffer) {
        WindowsDisplay.nSetGammaRamp(byteBuffer);
        this.current_gamma = byteBuffer;
    }

    private static native void nSetGammaRamp(ByteBuffer var0);

    public final String getAdapter() {
        try {
            String string = WindowsRegistry.queryRegistrationKey(3, "HARDWARE\\DeviceMap\\Video", "MaxObjectNumber");
            int n = string.charAt(0);
            String string2 = "";
            for (int i = 0; i < n; ++i) {
                String string3 = WindowsRegistry.queryRegistrationKey(3, "HARDWARE\\DeviceMap\\Video", "\\Device\\Video" + i);
                String string4 = "\\registry\\machine\\";
                if (!string3.toLowerCase().startsWith(string4)) continue;
                if ((string3 = WindowsRegistry.queryRegistrationKey(3, string3.substring(string4.length()), "InstalledDisplayDrivers")).toUpperCase().startsWith("VGA")) {
                    string2 = string3;
                    continue;
                }
                if (string3.toUpperCase().startsWith("RDP") || string3.toUpperCase().startsWith("NMNDD")) continue;
                return string3;
            }
            if (!string2.equals("")) {
                return string2;
            }
        }
        catch (LWJGLException lWJGLException) {
            LWJGLUtil.log("Exception occurred while querying registry: " + lWJGLException);
        }
        return null;
    }

    public final String getVersion() {
        Object object = this.getAdapter();
        if (object != null && ((String[])(object = ((String)object).split(","))).length > 0 && (object = this.nGetVersion(object[0] + ".dll")) != null) {
            return ((WindowsFileVersion)object).toString();
        }
        return null;
    }

    private native WindowsFileVersion nGetVersion(String var1);

    public final DisplayMode init() {
        WindowsDisplay windowsDisplay = this;
        windowsDisplay.current_gamma = windowsDisplay.saved_gamma = WindowsDisplay.getCurrentGammaRamp();
        this.current_mode = WindowsDisplay.getCurrentDisplayMode();
        return this.current_mode;
    }

    private static native DisplayMode getCurrentDisplayMode();

    public final void setTitle(String object) {
        object = MemoryUtil.encodeUTF16((CharSequence)object);
        WindowsDisplay.nSetTitle(this.hwnd, MemoryUtil.getAddress0((Buffer)object));
    }

    private static native void nSetTitle(long var0, long var2);

    public final boolean isCloseRequested() {
        boolean bl = this.close_requested;
        this.close_requested = false;
        return bl;
    }

    public final boolean isVisible() {
        return !this.isMinimized;
    }

    public final boolean isActive() {
        return this.isFocused;
    }

    public final boolean isDirty() {
        boolean bl = this.is_dirty;
        this.is_dirty = false;
        return bl;
    }

    public final PeerInfo createPeerInfo(org.lwjgl.opengl.PixelFormat pixelFormat, ContextAttribs contextAttribs) {
        this.peer_info = new WindowsDisplayPeerInfo(false);
        return this.peer_info;
    }

    public final void update() {
        WindowsDisplay.nUpdate();
        if (!this.isFocused && this.parent != null && this.parent_focused.compareAndSet(true, false)) {
            WindowsDisplay.setFocus(this.getHwnd());
        }
        if (this.redoMakeContextCurrent) {
            this.redoMakeContextCurrent = false;
            try {
                Context context = ((DrawableLWJGL)Display.getDrawable()).getContext();
                if (context != null && context.isCurrent()) {
                    context.makeCurrent();
                }
                return;
            }
            catch (LWJGLException lWJGLException) {
                LWJGLUtil.log("Exception occurred while trying to make context current: " + lWJGLException);
            }
        }
    }

    private static native void nUpdate();

    public final void reshape(int n, int n2, int n3, int n4) {
        WindowsDisplay.nReshape(this.getHwnd(), n, n2, n3, n4, Display.isFullscreen() || WindowsDisplay.isUndecorated(), this.parent != null);
    }

    private static native void nReshape(long var0, int var2, int var3, int var4, int var5, boolean var6, boolean var7);

    public final native DisplayMode[] getAvailableDisplayModes();

    public final boolean hasWheel() {
        return this.mouse.hasWheel();
    }

    public final int getButtonCount() {
        return this.mouse.getButtonCount();
    }

    public final void createMouse() {
        this.mouse = new WindowsMouse(this.getHwnd());
    }

    public final void destroyMouse() {
        if (this.mouse != null) {
            this.mouse.destroy();
        }
        this.mouse = null;
    }

    public final void pollMouse(IntBuffer intBuffer, ByteBuffer byteBuffer) {
        this.mouse.poll(intBuffer, byteBuffer, this);
    }

    public final void readMouse(ByteBuffer byteBuffer) {
        this.mouse.read(byteBuffer);
    }

    public final void grabMouse(boolean bl) {
        this.mouse.grab(bl, this.shouldGrab());
        this.updateCursor();
    }

    public final int getNativeCursorCapabilities() {
        return 1;
    }

    public final void setCursorPosition(int n, int n2) {
        WindowsDisplay.getGlobalClientRect(this.getHwnd(), rect);
        int n3 = WindowsDisplay.rect.left + n;
        int n4 = WindowsDisplay.rect.bottom - 1 - n2;
        WindowsDisplay.nSetCursorPosition(n3, n4);
        this.setMousePosition(n, n2);
    }

    private static native void nSetCursorPosition(int var0, int var1);

    public final void setNativeCursor(Object object) {
        this.current_cursor = object;
        this.updateCursor();
    }

    private void updateCursor() {
        try {
            if (this.mouse == null || !this.shouldGrab()) {
                WindowsDisplay.nSetNativeCursor(this.getHwnd(), this.current_cursor);
                return;
            }
            WindowsDisplay.nSetNativeCursor(this.getHwnd(), this.mouse.getBlankCursor());
        }
        catch (LWJGLException lWJGLException) {
            LWJGLUtil.log("Failed to update cursor: " + lWJGLException);
        }
    }

    static native void nSetNativeCursor(long var0, Object var2);

    public final int getMinCursorSize() {
        return WindowsDisplay.getSystemMetrics(13);
    }

    public final int getMaxCursorSize() {
        return WindowsDisplay.getSystemMetrics(13);
    }

    static native int getSystemMetrics(int var0);

    private static native long getDllInstance();

    private long getHwnd() {
        return this.hwnd;
    }

    private long getHdc() {
        return this.hdc;
    }

    private static native long getDC(long var0);

    private static native long getDesktopWindow();

    private static native long getForegroundWindow();

    static void centerCursor(long l) {
        if (WindowsDisplay.getForegroundWindow() != l && !hasParent) {
            return;
        }
        WindowsDisplay.getGlobalClientRect(l, rect);
        int n = WindowsDisplay.rect.left;
        int n2 = WindowsDisplay.rect.top;
        int n3 = (WindowsDisplay.rect.left + WindowsDisplay.rect.right) / 2;
        int n4 = (WindowsDisplay.rect.top + WindowsDisplay.rect.bottom) / 2;
        WindowsDisplay.nSetCursorPosition(n3, n4);
        n = n3 - n;
        n2 = n4 - n2;
        if (current_display != null) {
            current_display.setMousePosition(n, WindowsDisplay.transformY(l, n2));
        }
    }

    private void setMousePosition(int n, int n2) {
        if (this.mouse != null) {
            this.mouse.setPosition(n, n2);
        }
    }

    public final void createKeyboard() {
        this.keyboard = new WindowsKeyboard();
    }

    public final void destroyKeyboard() {
        this.keyboard = null;
    }

    public final void pollKeyboard(ByteBuffer byteBuffer) {
        this.keyboard.poll(byteBuffer);
    }

    public final void readKeyboard(ByteBuffer byteBuffer) {
        this.keyboard.read(byteBuffer);
    }

    public static native ByteBuffer nCreateCursor(int var0, int var1, int var2, int var3, int var4, IntBuffer var5, int var6, IntBuffer var7, int var8);

    public final Object createCursor(int n, int n2, int n3, int n4, int n5, IntBuffer intBuffer, IntBuffer intBuffer2) {
        return WindowsDisplay.doCreateCursor(n, n2, n3, n4, n5, intBuffer, intBuffer2);
    }

    static Object doCreateCursor(int n, int n2, int n3, int n4, int n5, IntBuffer intBuffer, IntBuffer intBuffer2) {
        IntBuffer intBuffer3 = intBuffer;
        IntBuffer intBuffer4 = intBuffer2;
        return WindowsDisplay.nCreateCursor(n, n2, n3, n4, n5, intBuffer3, intBuffer3.position(), intBuffer4, intBuffer4 != null ? intBuffer2.position() : -1);
    }

    public final void destroyCursor(Object object) {
        WindowsDisplay.doDestroyCursor(object);
    }

    static native void doDestroyCursor(Object var0);

    public final int getPbufferCapabilities() {
        try {
            return this.nGetPbufferCapabilities(new org.lwjgl.opengl.PixelFormat(0, 0, 0, 0, 0, 0, 0, 0, false));
        }
        catch (LWJGLException lWJGLException) {
            LWJGLUtil.log("Exception occurred while determining pbuffer capabilities: " + lWJGLException);
            return 0;
        }
    }

    private native int nGetPbufferCapabilities(org.lwjgl.opengl.PixelFormat var1);

    public final boolean isBufferLost(PeerInfo peerInfo) {
        return ((WindowsPbufferPeerInfo)peerInfo).isBufferLost();
    }

    public final PeerInfo createPbuffer(int n, int n2, org.lwjgl.opengl.PixelFormat pixelFormat, ContextAttribs contextAttribs, IntBuffer intBuffer, IntBuffer intBuffer2) {
        return new WindowsPbufferPeerInfo(n, n2, pixelFormat, intBuffer, intBuffer2);
    }

    public final void setPbufferAttrib(PeerInfo peerInfo, int n, int n2) {
        ((WindowsPbufferPeerInfo)peerInfo).setPbufferAttrib(n, n2);
    }

    public final void bindTexImageToPbuffer(PeerInfo peerInfo, int n) {
        ((WindowsPbufferPeerInfo)peerInfo).bindTexImageToPbuffer(n);
    }

    public final void releaseTexImageFromPbuffer(PeerInfo peerInfo, int n) {
        ((WindowsPbufferPeerInfo)peerInfo).releaseTexImageFromPbuffer(n);
    }

    private void freeSmallIcon() {
        if (this.small_icon != 0L) {
            WindowsDisplay.destroyIcon(this.small_icon);
            this.small_icon = 0L;
        }
    }

    private void freeLargeIcon() {
        if (this.large_icon != 0L) {
            WindowsDisplay.destroyIcon(this.large_icon);
            this.large_icon = 0L;
        }
    }

    public final int setIcon(ByteBuffer[] byteBufferArray) {
        boolean bl = false;
        boolean bl2 = false;
        int n = 0;
        block0: for (ByteBuffer byteBuffer : byteBufferArray) {
            long l;
            int n2 = byteBuffer.limit() / 4;
            if ((int)Math.sqrt(n2) == 16 && !bl) {
                l = WindowsDisplay.createIcon(16, 16, byteBuffer.asIntBuffer());
                WindowsDisplay.sendMessage(this.hwnd, 128L, 0L, l);
                this.freeSmallIcon();
                this.small_icon = l;
                ++n;
                bl = true;
            }
            if ((int)Math.sqrt(n2) != 32 || bl2) continue;
            l = WindowsDisplay.createIcon(32, 32, byteBuffer.asIntBuffer());
            WindowsDisplay.sendMessage(this.hwnd, 128L, 1L, l);
            this.freeLargeIcon();
            this.large_icon = l;
            ++n;
            bl2 = true;
            this.iconsLoaded = false;
            long l2 = System.nanoTime();
            while (true) {
                WindowsDisplay.nUpdate();
                if (this.iconsLoaded || 500000000L < System.nanoTime() - l2) continue block0;
                Thread.yield();
            }
        }
        return n;
    }

    private static native long createIcon(int var0, int var1, IntBuffer var2);

    private static native void destroyIcon(long var0);

    private static native long sendMessage(long var0, long var2, long var4, long var6);

    private static native long setWindowLongPtr(long var0, int var2, long var3);

    private static native long getWindowLongPtr(long var0, int var2);

    private static native boolean setWindowPos(long var0, long var2, int var4, int var5, int var6, int var7, long var8);

    private void handleMouseButton(int n, int n2, long l) {
        if (this.mouse != null) {
            this.mouse.handleMouseButton((byte)n, (byte)n2, l);
            if (this.captureMouse == -1 && n != -1 && n2 == 1) {
                this.captureMouse = n;
                WindowsDisplay.nSetCapture(this.hwnd);
            }
            if (this.captureMouse != -1 && n == this.captureMouse && n2 == 0) {
                this.captureMouse = -1;
                WindowsDisplay.nReleaseCapture();
            }
        }
    }

    private boolean shouldGrab() {
        return !this.isMinimized && this.isFocused && Mouse.isGrabbed();
    }

    private static native long nSetCapture(long var0);

    private static native boolean nReleaseCapture();

    private void handleMouseScrolled(int n, long l) {
        if (this.mouse != null) {
            this.mouse.handleMouseScrolled(n, l);
        }
    }

    private static native void getClientRect(long var0, IntBuffer var2);

    private void handleChar(long l, long l2, long l3) {
        byte by = (byte)(1L - (l2 >>> 31 & 1L));
        byte by2 = (byte)(l2 >>> 30 & 1L);
        byte by3 = by = by == by2 ? (byte)1 : 0;
        if (this.keyboard != null) {
            this.keyboard.handleChar((int)(l & 0xFFFFL), l3, by != 0);
        }
    }

    private void handleKeyButton(long l, long l2, long l3) {
        if (this.keyboard == null) {
            return;
        }
        byte by = (byte)(1L - (l2 >>> 31 & 1L));
        byte by2 = (byte)(l2 >>> 30 & 1L);
        by2 = by == by2 ? (byte)1 : 0;
        byte by3 = (byte)(l2 >>> 24 & 1L);
        int n = (int)(l2 >>> 16 & 0xFFL);
        this.keyboard.handleKey((int)l, n, by3 != 0, by, l3, by2 != 0);
    }

    private static int transformY(long l, int n) {
        WindowsDisplay.getClientRect(l, rect_buffer);
        rect.copyFromBuffer(rect_buffer);
        return WindowsDisplay.rect.bottom - WindowsDisplay.rect.top - 1 - n;
    }

    private static native void clientToScreen(long var0, IntBuffer var2);

    private static native void setWindowProc(Method var0);

    private static long handleMessage(long l, int n, long l2, long l3, long l4) {
        if (current_display != null) {
            return current_display.doHandleMessage(l, n, l2, l3, l4);
        }
        return WindowsDisplay.defWindowProc(l, n, l2, l3);
    }

    private static native long defWindowProc(long var0, int var2, long var3, long var5);

    private void updateClipping() {
        if ((Display.isFullscreen() || this.mouse != null && this.mouse.isGrabbed()) && !this.isMinimized && this.isFocused && (WindowsDisplay.getForegroundWindow() == this.getHwnd() || hasParent)) {
            try {
                WindowsDisplay.setupCursorClipping(this.getHwnd());
                return;
            }
            catch (LWJGLException lWJGLException) {
                LWJGLUtil.log("setupCursorClipping failed: " + lWJGLException.getMessage());
                return;
            }
        }
        WindowsDisplay.resetCursorClipping();
    }

    private void setMinimized(boolean bl) {
        if (bl != this.isMinimized) {
            this.isMinimized = bl;
            this.updateClipping();
        }
    }

    private long doHandleMessage(long l, int n, long l2, long l3, long l4) {
        if (this.parent != null && !this.isFocused) {
            switch (n) {
                case 513: 
                case 516: 
                case 519: 
                case 523: {
                    WindowsDisplay.sendMessage(this.parent_hwnd, n, l2, l3);
                }
            }
        }
        switch (n) {
            case 6: {
                return 0L;
            }
            case 5: {
                switch ((int)l2) {
                    case 0: 
                    case 2: {
                        this.resized = true;
                        this.updateWidthAndHeight();
                        this.setMinimized(false);
                        break;
                    }
                    case 1: {
                        this.setMinimized(true);
                    }
                }
                break;
            }
            case 532: {
                this.resized = true;
                this.updateWidthAndHeight();
                break;
            }
            case 32: {
                if ((l3 & 0xFFFFL) == 1L) {
                    this.updateCursor();
                    return -1L;
                }
                return WindowsDisplay.defWindowProc(l, n, l2, l3);
            }
            case 8: {
                this.appActivate(false, l4);
                return 0L;
            }
            case 7: {
                this.appActivate(true, l4);
                return 0L;
            }
            case 33: {
                if (this.parent == null) break;
                if (!this.isFocused) {
                    this.grabFocus();
                }
                return 3L;
            }
            case 512: {
                if (this.mouse != null) {
                    n = (short)(l3 & 0xFFFFL);
                    int n2 = WindowsDisplay.transformY(this.getHwnd(), (short)(l3 >>> 16));
                    this.mouse.handleMouseMoved(n, n2, l4);
                }
                if (!this.mouseInside) {
                    this.mouseInside = true;
                    this.updateClipping();
                    this.nTrackMouseEvent(l);
                }
                return 0L;
            }
            case 522: {
                n = (short)(l2 >> 16 & 0xFFFFL);
                this.handleMouseScrolled(n, l4);
                return 0L;
            }
            case 513: {
                this.handleMouseButton(0, 1, l4);
                return 0L;
            }
            case 514: {
                this.handleMouseButton(0, 0, l4);
                return 0L;
            }
            case 516: {
                this.handleMouseButton(1, 1, l4);
                return 0L;
            }
            case 517: {
                this.handleMouseButton(1, 0, l4);
                return 0L;
            }
            case 519: {
                this.handleMouseButton(2, 1, l4);
                return 0L;
            }
            case 520: {
                this.handleMouseButton(2, 0, l4);
                return 0L;
            }
            case 524: {
                if (l2 >> 16 == 1L) {
                    this.handleMouseButton(3, 0, l4);
                } else {
                    this.handleMouseButton(4, 0, l4);
                }
                return 1L;
            }
            case 523: {
                if ((l2 & 0xFFL) == 32L) {
                    this.handleMouseButton(3, 1, l4);
                } else {
                    this.handleMouseButton(4, 1, l4);
                }
                return 1L;
            }
            case 258: 
            case 262: {
                this.handleChar(l2, l3, l4);
                return 0L;
            }
            case 261: {
                if (l2 == 18L || l2 == 121L) {
                    this.handleKeyButton(l2, l3, l4);
                    return 0L;
                }
            }
            case 257: {
                if (l2 == 44L && this.keyboard != null && !this.keyboard.isKeyDown(183)) {
                    long l5 = l3 & Integer.MAX_VALUE;
                    this.handleKeyButton(l2, l5 &= 0xFFFFFFFFBFFFFFFFL, l4);
                }
            }
            case 256: 
            case 260: {
                this.handleKeyButton(l2, l3, l4);
                break;
            }
            case 18: {
                this.close_requested = true;
                return 0L;
            }
            case 274: {
                switch ((int)(l2 & 0xFFF0L)) {
                    case 61760: 
                    case 61808: {
                        return 0L;
                    }
                    case 61536: {
                        this.close_requested = true;
                        return 0L;
                    }
                }
                break;
            }
            case 15: {
                this.is_dirty = true;
                break;
            }
            case 675: {
                this.mouseInside = false;
                break;
            }
            case 31: {
                WindowsDisplay.nReleaseCapture();
            }
            case 533: {
                if (this.captureMouse != -1) {
                    WindowsDisplay windowsDisplay = this;
                    windowsDisplay.handleMouseButton(windowsDisplay.captureMouse, 0, l4);
                    this.captureMouse = -1;
                }
                return 0L;
            }
            case 71: {
                if (this.getWindowRect(l, rect_buffer)) {
                    rect.copyFromBuffer(rect_buffer);
                    this.x = WindowsDisplay.rect.left;
                    this.y = WindowsDisplay.rect.top;
                    break;
                }
                LWJGLUtil.log("WM_WINDOWPOSCHANGED: Unable to get window rect");
                break;
            }
            case 127: {
                this.iconsLoaded = true;
            }
        }
        return WindowsDisplay.defWindowProc(l, n, l2, l3);
    }

    private native boolean getWindowRect(long var1, IntBuffer var3);

    public final int getX() {
        return this.x;
    }

    public final int getY() {
        return this.y;
    }

    public final int getWidth() {
        return this.width;
    }

    public final int getHeight() {
        return this.height;
    }

    private native boolean nTrackMouseEvent(long var1);

    public final boolean isInsideWindow() {
        return this.mouseInside;
    }

    public final void setResizable(boolean bl) {
        if (this.resizable == bl) {
            return;
        }
        this.resized = false;
        this.resizable = bl;
        int n = (int)WindowsDisplay.getWindowLongPtr(this.hwnd, -16);
        int n2 = (int)WindowsDisplay.getWindowLongPtr(this.hwnd, -20);
        n = bl && !Display.isFullscreen() ? n | 0x50000 : n & 0xFFFAFFFF;
        WindowsDisplay.setWindowLongPtr(this.hwnd, -16, n);
        WindowsDisplay.getGlobalClientRect(this.hwnd, rect);
        rect.copyToBuffer(rect_buffer);
        this.adjustWindowRectEx(rect_buffer, n, false, n2);
        rect.copyFromBuffer(rect_buffer);
        WindowsDisplay.setWindowPos(this.hwnd, 0L, WindowsDisplay.rect.left, WindowsDisplay.rect.top, WindowsDisplay.rect.right - WindowsDisplay.rect.left, WindowsDisplay.rect.bottom - WindowsDisplay.rect.top, 36L);
        this.updateWidthAndHeight();
    }

    private native boolean adjustWindowRectEx(IntBuffer var1, int var2, boolean var3, int var4);

    public final boolean wasResized() {
        if (this.resized) {
            this.resized = false;
            return true;
        }
        return false;
    }

    public final float getPixelScaleFactor() {
        return 1.0f;
    }

    static {
        try {
            Method method = WindowsDisplay.class.getDeclaredMethod("handleMessage", Long.TYPE, Integer.TYPE, Long.TYPE, Long.TYPE, Long.TYPE);
            WindowsDisplay.setWindowProc(method);
            return;
        }
        catch (NoSuchMethodException noSuchMethodException) {
            throw new RuntimeException(noSuchMethodException);
        }
    }

    private static final class Rect {
        public int left;
        public int top;
        public int right;
        public int bottom;

        private Rect() {
        }

        public final void copyToBuffer(IntBuffer intBuffer) {
            intBuffer.put(0, this.left).put(1, this.top).put(2, this.right).put(3, this.bottom);
        }

        public final void copyFromBuffer(IntBuffer intBuffer) {
            this.left = intBuffer.get(0);
            this.top = intBuffer.get(1);
            this.right = intBuffer.get(2);
            this.bottom = intBuffer.get(3);
        }

        public final void offset(int n, int n2) {
            this.left += n;
            this.top += n2;
            this.right += n;
            this.bottom += n2;
        }

        public static void intersect(Rect rect, Rect rect2, Rect rect3) {
            rect3.left = Math.max(rect.left, rect2.left);
            rect3.top = Math.max(rect.top, rect2.top);
            rect3.right = Math.min(rect.right, rect2.right);
            rect3.bottom = Math.min(rect.bottom, rect2.bottom);
        }

        public final String toString() {
            return "Rect: left = " + this.left + " top = " + this.top + " right = " + this.right + " bottom = " + this.bottom + ", width: " + (this.right - this.left) + ", height: " + (this.bottom - this.top);
        }
    }
}
