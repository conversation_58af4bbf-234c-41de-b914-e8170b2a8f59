/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class EXTSeparateShaderObjects {
    public static final int GL_ACTIVE_PROGRAM_EXT = 35725;

    private EXTSeparateShaderObjects() {
    }

    public static void glUseShaderProgramEXT(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUseShaderProgramEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTSeparateShaderObjects.nglUseShaderProgramEXT(n, n2, l);
    }

    static native void nglUseShaderProgramEXT(int var0, int var1, long var2);

    public static void glActiveProgramEXT(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glActiveProgramEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTSeparateShaderObjects.nglActiveProgramEXT(n, l);
    }

    static native void nglActiveProgramEXT(int var0, long var1);

    public static int glCreateShaderProgramEXT(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateShaderProgramEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = EXTSeparateShaderObjects.nglCreateShaderProgramEXT(n, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglCreateShaderProgramEXT(int var0, long var1, long var3);

    public static int glCreateShaderProgramEXT(int n, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateShaderProgramEXT;
        BufferChecks.checkFunctionAddress(l);
        n = EXTSeparateShaderObjects.nglCreateShaderProgramEXT(n, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }
}
