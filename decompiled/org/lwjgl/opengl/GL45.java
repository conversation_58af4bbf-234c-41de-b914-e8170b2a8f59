/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.LongBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.PointerBuffer;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;

public final class GL45 {
    public static final int GL_NEGATIVE_ONE_TO_ONE = 37726;
    public static final int GL_ZERO_TO_ONE = 37727;
    public static final int GL_CLIP_ORIGIN = 37724;
    public static final int GL_CLIP_DEPTH_MODE = 37725;
    public static final int GL_QUERY_WAIT_INVERTED = 36375;
    public static final int GL_QUERY_NO_WAIT_INVERTED = 36376;
    public static final int GL_QUERY_BY_REGION_WAIT_INVERTED = 36377;
    public static final int GL_QUERY_BY_REGION_NO_WAIT_INVERTED = 36378;
    public static final int GL_MAX_CULL_DISTANCES = 33529;
    public static final int GL_MAX_COMBINED_CLIP_AND_CULL_DISTANCES = 33530;
    public static final int GL_TEXTURE_TARGET = 4102;
    public static final int GL_QUERY_TARGET = 33514;
    public static final int GL_TEXTURE_BINDING = 33515;
    public static final int GL_CONTEXT_RELEASE_BEHAVIOR = 33531;
    public static final int GL_CONTEXT_RELEASE_BEHAVIOR_FLUSH = 33532;
    public static final int GL_GUILTY_CONTEXT_RESET = 33363;
    public static final int GL_INNOCENT_CONTEXT_RESET = 33364;
    public static final int GL_UNKNOWN_CONTEXT_RESET = 33365;
    public static final int GL_CONTEXT_ROBUST_ACCESS = 37107;
    public static final int GL_RESET_NOTIFICATION_STRATEGY = 33366;
    public static final int GL_LOSE_CONTEXT_ON_RESET = 33362;
    public static final int GL_NO_RESET_NOTIFICATION = 33377;
    public static final int GL_CONTEXT_LOST = 1287;

    private GL45() {
    }

    public static void glClipControl(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClipControl;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglClipControl(n, n2, l);
    }

    static native void nglClipControl(int var0, int var1, long var2);

    public static void glCreateTransformFeedbacks(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateTransformFeedbacks;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglCreateTransformFeedbacks(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglCreateTransformFeedbacks(int var0, long var1, long var3);

    public static int glCreateTransformFeedbacks() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glCreateTransformFeedbacks;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglCreateTransformFeedbacks(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glTransformFeedbackBufferBase(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTransformFeedbackBufferBase;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglTransformFeedbackBufferBase(n, n2, n3, l);
    }

    static native void nglTransformFeedbackBufferBase(int var0, int var1, int var2, long var3);

    public static void glTransformFeedbackBufferRange(int n, int n2, int n3, long l, long l2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glTransformFeedbackBufferRange;
        BufferChecks.checkFunctionAddress(l3);
        GL45.nglTransformFeedbackBufferRange(n, n2, n3, l, l2, l3);
    }

    static native void nglTransformFeedbackBufferRange(int var0, int var1, int var2, long var3, long var5, long var7);

    public static void glGetTransformFeedback(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTransformFeedbackiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL45.nglGetTransformFeedbackiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetTransformFeedbackiv(int var0, int var1, long var2, long var4);

    public static int glGetTransformFeedbacki(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTransformFeedbackiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglGetTransformFeedbackiv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetTransformFeedback(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTransformFeedbacki_v;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL45.nglGetTransformFeedbacki_v(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetTransformFeedbacki_v(int var0, int var1, int var2, long var3, long var5);

    public static int glGetTransformFeedbacki(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTransformFeedbacki_v;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglGetTransformFeedbacki_v(n, n2, n3, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetTransformFeedback(int n, int n2, int n3, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTransformFeedbacki64_v;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 1);
        GL45.nglGetTransformFeedbacki64_v(n, n2, n3, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetTransformFeedbacki64_v(int var0, int var1, int var2, long var3, long var5);

    public static long glGetTransformFeedbacki64(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTransformFeedbacki64_v;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferLong((ContextCapabilities)object);
        GL45.nglGetTransformFeedbacki64_v(n, n2, n3, MemoryUtil.getAddress((LongBuffer)object), l);
        return ((LongBuffer)object).get(0);
    }

    public static void glCreateBuffers(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateBuffers;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglCreateBuffers(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglCreateBuffers(int var0, long var1, long var3);

    public static int glCreateBuffers() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glCreateBuffers;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglCreateBuffers(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glNamedBufferStorage(int n, ByteBuffer byteBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedBufferStorage;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        GL45.nglNamedBufferStorage(n, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), n2, l);
    }

    public static void glNamedBufferStorage(int n, DoubleBuffer doubleBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedBufferStorage;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL45.nglNamedBufferStorage(n, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), n2, l);
    }

    public static void glNamedBufferStorage(int n, FloatBuffer floatBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedBufferStorage;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL45.nglNamedBufferStorage(n, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), n2, l);
    }

    public static void glNamedBufferStorage(int n, IntBuffer intBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedBufferStorage;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglNamedBufferStorage(n, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), n2, l);
    }

    public static void glNamedBufferStorage(int n, ShortBuffer shortBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedBufferStorage;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        GL45.nglNamedBufferStorage(n, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), n2, l);
    }

    public static void glNamedBufferStorage(int n, LongBuffer longBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedBufferStorage;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(longBuffer);
        GL45.nglNamedBufferStorage(n, longBuffer.remaining() << 3, MemoryUtil.getAddress(longBuffer), n2, l);
    }

    static native void nglNamedBufferStorage(int var0, long var1, long var3, int var5, long var6);

    public static void glNamedBufferStorage(int n, long l, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glNamedBufferStorage;
        BufferChecks.checkFunctionAddress(l2);
        GL45.nglNamedBufferStorage(n, l, 0L, n2, l2);
    }

    public static void glNamedBufferData(int n, long l, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glNamedBufferData;
        BufferChecks.checkFunctionAddress(l2);
        GL45.nglNamedBufferData(n, l, 0L, n2, l2);
    }

    public static void glNamedBufferData(int n, ByteBuffer byteBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedBufferData;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        GL45.nglNamedBufferData(n, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), n2, l);
    }

    public static void glNamedBufferData(int n, DoubleBuffer doubleBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedBufferData;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        GL45.nglNamedBufferData(n, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), n2, l);
    }

    public static void glNamedBufferData(int n, FloatBuffer floatBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedBufferData;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL45.nglNamedBufferData(n, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), n2, l);
    }

    public static void glNamedBufferData(int n, IntBuffer intBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedBufferData;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglNamedBufferData(n, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), n2, l);
    }

    public static void glNamedBufferData(int n, ShortBuffer shortBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedBufferData;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        GL45.nglNamedBufferData(n, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), n2, l);
    }

    static native void nglNamedBufferData(int var0, long var1, long var3, int var5, long var6);

    public static void glNamedBufferSubData(int n, long l, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glNamedBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(byteBuffer);
        GL45.nglNamedBufferSubData(n, l, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l2);
    }

    public static void glNamedBufferSubData(int n, long l, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glNamedBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(doubleBuffer);
        GL45.nglNamedBufferSubData(n, l, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l2);
    }

    public static void glNamedBufferSubData(int n, long l, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glNamedBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(floatBuffer);
        GL45.nglNamedBufferSubData(n, l, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l2);
    }

    public static void glNamedBufferSubData(int n, long l, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glNamedBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglNamedBufferSubData(n, l, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l2);
    }

    public static void glNamedBufferSubData(int n, long l, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glNamedBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(shortBuffer);
        GL45.nglNamedBufferSubData(n, l, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l2);
    }

    static native void nglNamedBufferSubData(int var0, long var1, long var3, long var5, long var7);

    public static void glCopyNamedBufferSubData(int n, int n2, long l, long l2, long l3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l4 = contextCapabilities.glCopyNamedBufferSubData;
        BufferChecks.checkFunctionAddress(l4);
        GL45.nglCopyNamedBufferSubData(n, n2, l, l2, l3, l4);
    }

    static native void nglCopyNamedBufferSubData(int var0, int var1, long var2, long var4, long var6, long var8);

    public static void glClearNamedBufferData(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearNamedBufferData;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 1);
        GL45.nglClearNamedBufferData(n, n2, n3, n4, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglClearNamedBufferData(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glClearNamedBufferSubData(int n, int n2, long l, long l2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glClearNamedBufferSubData;
        BufferChecks.checkFunctionAddress(l3);
        BufferChecks.checkBuffer(byteBuffer, 1);
        GL45.nglClearNamedBufferSubData(n, n2, l, l2, n3, n4, MemoryUtil.getAddress(byteBuffer), l3);
    }

    static native void nglClearNamedBufferSubData(int var0, int var1, long var2, long var4, int var6, int var7, long var8, long var10);

    public static ByteBuffer glMapNamedBuffer(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMapNamedBuffer;
        BufferChecks.checkFunctionAddress(l);
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        ByteBuffer byteBuffer2 = GL45.nglMapNamedBuffer(n, n2, GL45.glGetNamedBufferParameteri(n, 34660), byteBuffer, l);
        if (LWJGLUtil.CHECKS && byteBuffer2 == null) {
            return null;
        }
        return byteBuffer2.order(ByteOrder.nativeOrder());
    }

    public static ByteBuffer glMapNamedBuffer(int n, int n2, long l, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMapNamedBuffer;
        BufferChecks.checkFunctionAddress(l2);
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        ByteBuffer byteBuffer2 = GL45.nglMapNamedBuffer(n, n2, l, byteBuffer, l2);
        if (LWJGLUtil.CHECKS && byteBuffer2 == null) {
            return null;
        }
        return byteBuffer2.order(ByteOrder.nativeOrder());
    }

    static native ByteBuffer nglMapNamedBuffer(int var0, int var1, long var2, ByteBuffer var4, long var5);

    public static ByteBuffer glMapNamedBufferRange(int n, long l, long l2, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glMapNamedBufferRange;
        BufferChecks.checkFunctionAddress(l3);
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        ByteBuffer byteBuffer2 = GL45.nglMapNamedBufferRange(n, l, l2, n2, byteBuffer, l3);
        if (LWJGLUtil.CHECKS && byteBuffer2 == null) {
            return null;
        }
        return byteBuffer2.order(ByteOrder.nativeOrder());
    }

    static native ByteBuffer nglMapNamedBufferRange(int var0, long var1, long var3, int var5, ByteBuffer var6, long var7);

    public static boolean glUnmapNamedBuffer(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUnmapNamedBuffer;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = GL45.nglUnmapNamedBuffer(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglUnmapNamedBuffer(int var0, long var1);

    public static void glFlushMappedNamedBufferRange(int n, long l, long l2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glFlushMappedNamedBufferRange;
        BufferChecks.checkFunctionAddress(l3);
        GL45.nglFlushMappedNamedBufferRange(n, l, l2, l3);
    }

    static native void nglFlushMappedNamedBufferRange(int var0, long var1, long var3, long var5);

    public static void glGetNamedBufferParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetNamedBufferParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglGetNamedBufferParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetNamedBufferParameteriv(int var0, int var1, long var2, long var4);

    public static int glGetNamedBufferParameteri(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetNamedBufferParameteriv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglGetNamedBufferParameteriv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetNamedBufferParameter(int n, int n2, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetNamedBufferParameteri64v;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 1);
        GL45.nglGetNamedBufferParameteri64v(n, n2, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetNamedBufferParameteri64v(int var0, int var1, long var2, long var4);

    public static long glGetNamedBufferParameteri64(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetNamedBufferParameteri64v;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferLong((ContextCapabilities)object);
        GL45.nglGetNamedBufferParameteri64v(n, n2, MemoryUtil.getAddress((LongBuffer)object), l);
        return ((LongBuffer)object).get(0);
    }

    public static ByteBuffer glGetNamedBufferPointer(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetNamedBufferPointerv;
        BufferChecks.checkFunctionAddress(l);
        ByteBuffer byteBuffer = GL45.nglGetNamedBufferPointerv(n, n2, GL45.glGetNamedBufferParameteri(n, 34660), l);
        if (LWJGLUtil.CHECKS && byteBuffer == null) {
            return null;
        }
        return byteBuffer.order(ByteOrder.nativeOrder());
    }

    static native ByteBuffer nglGetNamedBufferPointerv(int var0, int var1, long var2, long var4);

    public static void glGetNamedBufferSubData(int n, long l, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetNamedBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(byteBuffer);
        GL45.nglGetNamedBufferSubData(n, l, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l2);
    }

    public static void glGetNamedBufferSubData(int n, long l, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetNamedBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(doubleBuffer);
        GL45.nglGetNamedBufferSubData(n, l, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l2);
    }

    public static void glGetNamedBufferSubData(int n, long l, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetNamedBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(floatBuffer);
        GL45.nglGetNamedBufferSubData(n, l, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l2);
    }

    public static void glGetNamedBufferSubData(int n, long l, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetNamedBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglGetNamedBufferSubData(n, l, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l2);
    }

    public static void glGetNamedBufferSubData(int n, long l, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetNamedBufferSubData;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkDirect(shortBuffer);
        GL45.nglGetNamedBufferSubData(n, l, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l2);
    }

    static native void nglGetNamedBufferSubData(int var0, long var1, long var3, long var5, long var7);

    public static void glCreateFramebuffers(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateFramebuffers;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglCreateFramebuffers(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglCreateFramebuffers(int var0, long var1, long var3);

    public static int glCreateFramebuffers() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glCreateFramebuffers;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglCreateFramebuffers(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glNamedFramebufferRenderbuffer(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedFramebufferRenderbuffer;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglNamedFramebufferRenderbuffer(n, n2, n3, n4, l);
    }

    static native void nglNamedFramebufferRenderbuffer(int var0, int var1, int var2, int var3, long var4);

    public static void glNamedFramebufferParameteri(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedFramebufferParameteri;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglNamedFramebufferParameteri(n, n2, n3, l);
    }

    static native void nglNamedFramebufferParameteri(int var0, int var1, int var2, long var3);

    public static void glNamedFramebufferTexture(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedFramebufferTexture;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglNamedFramebufferTexture(n, n2, n3, n4, l);
    }

    static native void nglNamedFramebufferTexture(int var0, int var1, int var2, int var3, long var4);

    public static void glNamedFramebufferTextureLayer(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedFramebufferTextureLayer;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglNamedFramebufferTextureLayer(n, n2, n3, n4, n5, l);
    }

    static native void nglNamedFramebufferTextureLayer(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glNamedFramebufferDrawBuffer(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedFramebufferDrawBuffer;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglNamedFramebufferDrawBuffer(n, n2, l);
    }

    static native void nglNamedFramebufferDrawBuffer(int var0, int var1, long var2);

    public static void glNamedFramebufferDrawBuffers(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedFramebufferDrawBuffers;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglNamedFramebufferDrawBuffers(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglNamedFramebufferDrawBuffers(int var0, int var1, long var2, long var4);

    public static void glNamedFramebufferReadBuffer(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedFramebufferReadBuffer;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglNamedFramebufferReadBuffer(n, n2, l);
    }

    static native void nglNamedFramebufferReadBuffer(int var0, int var1, long var2);

    public static void glInvalidateNamedFramebufferData(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInvalidateNamedFramebufferData;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglInvalidateNamedFramebufferData(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglInvalidateNamedFramebufferData(int var0, int var1, long var2, long var4);

    public static void glInvalidateNamedFramebufferSubData(int n, IntBuffer intBuffer, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInvalidateNamedFramebufferSubData;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglInvalidateNamedFramebufferSubData(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), n2, n3, n4, n5, l);
    }

    static native void nglInvalidateNamedFramebufferSubData(int var0, int var1, long var2, int var4, int var5, int var6, int var7, long var8);

    public static void glClearNamedFramebuffer(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearNamedFramebufferiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL45.nglClearNamedFramebufferiv(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglClearNamedFramebufferiv(int var0, int var1, int var2, long var3, long var5);

    public static void glClearNamedFramebufferu(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearNamedFramebufferuiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL45.nglClearNamedFramebufferuiv(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglClearNamedFramebufferuiv(int var0, int var1, int var2, long var3, long var5);

    public static void glClearNamedFramebuffer(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearNamedFramebufferfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 1);
        GL45.nglClearNamedFramebufferfv(n, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglClearNamedFramebufferfv(int var0, int var1, int var2, long var3, long var5);

    public static void glClearNamedFramebufferfi(int n, int n2, float f, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearNamedFramebufferfi;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglClearNamedFramebufferfi(n, n2, f, n3, l);
    }

    static native void nglClearNamedFramebufferfi(int var0, int var1, float var2, int var3, long var4);

    public static void glBlitNamedFramebuffer(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, int n11, int n12) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlitNamedFramebuffer;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglBlitNamedFramebuffer(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, n11, n12, l);
    }

    static native void nglBlitNamedFramebuffer(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9, int var10, int var11, long var12);

    public static int glCheckNamedFramebufferStatus(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCheckNamedFramebufferStatus;
        BufferChecks.checkFunctionAddress(l);
        n = GL45.nglCheckNamedFramebufferStatus(n, n2, l);
        return n;
    }

    static native int nglCheckNamedFramebufferStatus(int var0, int var1, long var2);

    public static void glGetNamedFramebufferParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetNamedFramebufferParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL45.nglGetNamedFramebufferParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetNamedFramebufferParameteriv(int var0, int var1, long var2, long var4);

    public static int glGetNamedFramebufferParameter(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetNamedFramebufferParameteriv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglGetNamedFramebufferParameteriv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetNamedFramebufferAttachmentParameter(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetNamedFramebufferAttachmentParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL45.nglGetNamedFramebufferAttachmentParameteriv(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetNamedFramebufferAttachmentParameteriv(int var0, int var1, int var2, long var3, long var5);

    public static int glGetNamedFramebufferAttachmentParameter(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetNamedFramebufferAttachmentParameteriv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglGetNamedFramebufferAttachmentParameteriv(n, n2, n3, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glCreateRenderbuffers(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateRenderbuffers;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglCreateRenderbuffers(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglCreateRenderbuffers(int var0, long var1, long var3);

    public static int glCreateRenderbuffers() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glCreateRenderbuffers;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglCreateRenderbuffers(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glNamedRenderbufferStorage(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedRenderbufferStorage;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglNamedRenderbufferStorage(n, n2, n3, n4, l);
    }

    static native void nglNamedRenderbufferStorage(int var0, int var1, int var2, int var3, long var4);

    public static void glNamedRenderbufferStorageMultisample(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glNamedRenderbufferStorageMultisample;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglNamedRenderbufferStorageMultisample(n, n2, n3, n4, n5, l);
    }

    static native void nglNamedRenderbufferStorageMultisample(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glGetNamedRenderbufferParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetNamedRenderbufferParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL45.nglGetNamedRenderbufferParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetNamedRenderbufferParameteriv(int var0, int var1, long var2, long var4);

    public static int glGetNamedRenderbufferParameter(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetNamedRenderbufferParameteriv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglGetNamedRenderbufferParameteriv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glCreateTextures(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateTextures;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglCreateTextures(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglCreateTextures(int var0, int var1, long var2, long var4);

    public static int glCreateTextures(int n) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glCreateTextures;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglCreateTextures(n, 1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glTextureBuffer(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureBuffer;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglTextureBuffer(n, n2, n3, l);
    }

    static native void nglTextureBuffer(int var0, int var1, int var2, long var3);

    public static void glTextureBufferRange(int n, int n2, int n3, long l, long l2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glTextureBufferRange;
        BufferChecks.checkFunctionAddress(l3);
        GL45.nglTextureBufferRange(n, n2, n3, l, l2, l3);
    }

    static native void nglTextureBufferRange(int var0, int var1, int var2, long var3, long var5, long var7);

    public static void glTextureStorage1D(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureStorage1D;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglTextureStorage1D(n, n2, n3, n4, l);
    }

    static native void nglTextureStorage1D(int var0, int var1, int var2, int var3, long var4);

    public static void glTextureStorage2D(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureStorage2D;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglTextureStorage2D(n, n2, n3, n4, n5, l);
    }

    static native void nglTextureStorage2D(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glTextureStorage3D(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureStorage3D;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglTextureStorage3D(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglTextureStorage3D(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glTextureStorage2DMultisample(int n, int n2, int n3, int n4, int n5, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureStorage2DMultisample;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglTextureStorage2DMultisample(n, n2, n3, n4, n5, bl, l);
    }

    static native void nglTextureStorage2DMultisample(int var0, int var1, int var2, int var3, int var4, boolean var5, long var6);

    public static void glTextureStorage3DMultisample(int n, int n2, int n3, int n4, int n5, int n6, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureStorage3DMultisample;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglTextureStorage3DMultisample(n, n2, n3, n4, n5, n6, bl, l);
    }

    static native void nglTextureStorage3DMultisample(int var0, int var1, int var2, int var3, int var4, int var5, boolean var6, long var7);

    public static void glTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ByteBuffer byteBuffer2 = byteBuffer;
        BufferChecks.checkBuffer(byteBuffer2, GLChecks.calculateImageStorage(byteBuffer2, n5, n6, n4, 1, 1));
        GL45.nglTextureSubImage1D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        DoubleBuffer doubleBuffer2 = doubleBuffer;
        BufferChecks.checkBuffer(doubleBuffer2, GLChecks.calculateImageStorage(doubleBuffer2, n5, n6, n4, 1, 1));
        GL45.nglTextureSubImage1D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        FloatBuffer floatBuffer2 = floatBuffer;
        BufferChecks.checkBuffer(floatBuffer2, GLChecks.calculateImageStorage(floatBuffer2, n5, n6, n4, 1, 1));
        GL45.nglTextureSubImage1D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        IntBuffer intBuffer2 = intBuffer;
        BufferChecks.checkBuffer(intBuffer2, GLChecks.calculateImageStorage(intBuffer2, n5, n6, n4, 1, 1));
        GL45.nglTextureSubImage1D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ShortBuffer shortBuffer2 = shortBuffer;
        BufferChecks.checkBuffer(shortBuffer2, GLChecks.calculateImageStorage(shortBuffer2, n5, n6, n4, 1, 1));
        GL45.nglTextureSubImage1D(n, n2, n3, n4, n5, n6, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglTextureSubImage1D(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glTextureSubImage1D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL45.nglTextureSubImage1DBO(n, n2, n3, n4, n5, n6, l, l2);
    }

    static native void nglTextureSubImage1DBO(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ByteBuffer byteBuffer2 = byteBuffer;
        BufferChecks.checkBuffer(byteBuffer2, GLChecks.calculateImageStorage(byteBuffer2, n7, n8, n5, n6, 1));
        GL45.nglTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        DoubleBuffer doubleBuffer2 = doubleBuffer;
        BufferChecks.checkBuffer(doubleBuffer2, GLChecks.calculateImageStorage(doubleBuffer2, n7, n8, n5, n6, 1));
        GL45.nglTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        FloatBuffer floatBuffer2 = floatBuffer;
        BufferChecks.checkBuffer(floatBuffer2, GLChecks.calculateImageStorage(floatBuffer2, n7, n8, n5, n6, 1));
        GL45.nglTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        IntBuffer intBuffer2 = intBuffer;
        BufferChecks.checkBuffer(intBuffer2, GLChecks.calculateImageStorage(intBuffer2, n7, n8, n5, n6, 1));
        GL45.nglTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ShortBuffer shortBuffer2 = shortBuffer;
        BufferChecks.checkBuffer(shortBuffer2, GLChecks.calculateImageStorage(shortBuffer2, n7, n8, n5, n6, 1));
        GL45.nglTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglTextureSubImage2D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glTextureSubImage2D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL45.nglTextureSubImage2DBO(n, n2, n3, n4, n5, n6, n7, n8, l, l2);
    }

    static native void nglTextureSubImage2DBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage3D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ByteBuffer byteBuffer2 = byteBuffer;
        BufferChecks.checkBuffer(byteBuffer2, GLChecks.calculateImageStorage(byteBuffer2, n9, n10, n6, n7, n8));
        GL45.nglTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage3D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        DoubleBuffer doubleBuffer2 = doubleBuffer;
        BufferChecks.checkBuffer(doubleBuffer2, GLChecks.calculateImageStorage(doubleBuffer2, n9, n10, n6, n7, n8));
        GL45.nglTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage3D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        FloatBuffer floatBuffer2 = floatBuffer;
        BufferChecks.checkBuffer(floatBuffer2, GLChecks.calculateImageStorage(floatBuffer2, n9, n10, n6, n7, n8));
        GL45.nglTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage3D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        IntBuffer intBuffer2 = intBuffer;
        BufferChecks.checkBuffer(intBuffer2, GLChecks.calculateImageStorage(intBuffer2, n9, n10, n6, n7, n8));
        GL45.nglTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureSubImage3D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        ShortBuffer shortBuffer2 = shortBuffer;
        BufferChecks.checkBuffer(shortBuffer2, GLChecks.calculateImageStorage(shortBuffer2, n9, n10, n6, n7, n8));
        GL45.nglTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglTextureSubImage3D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9, long var10, long var12);

    public static void glTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glTextureSubImage3D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL45.nglTextureSubImage3DBO(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, l, l2);
    }

    static native void nglTextureSubImage3DBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9, long var10, long var12);

    public static void glCompressedTextureSubImage1D(int n, int n2, int n3, int n4, int n5, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTextureSubImage1D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL45.nglCompressedTextureSubImage1D(n, n2, n3, n4, n5, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTextureSubImage1D(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glCompressedTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTextureSubImage1D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL45.nglCompressedTextureSubImage1DBO(n, n2, n3, n4, n5, n6, l, l2);
    }

    static native void nglCompressedTextureSubImage1DBO(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glCompressedTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTextureSubImage2D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL45.nglCompressedTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTextureSubImage2D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glCompressedTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTextureSubImage2D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL45.nglCompressedTextureSubImage2DBO(n, n2, n3, n4, n5, n6, n7, n8, l, l2);
    }

    static native void nglCompressedTextureSubImage2DBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glCompressedTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTextureSubImage3D;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL45.nglCompressedTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTextureSubImage3D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9, long var10, long var12);

    public static void glCompressedTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTextureSubImage3D;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        GL45.nglCompressedTextureSubImage3DBO(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, l, l2);
    }

    static native void nglCompressedTextureSubImage3DBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9, long var10, long var12);

    public static void glCopyTextureSubImage1D(int n, int n2, int n3, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCopyTextureSubImage1D;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglCopyTextureSubImage1D(n, n2, n3, n4, n5, n6, l);
    }

    static native void nglCopyTextureSubImage1D(int var0, int var1, int var2, int var3, int var4, int var5, long var6);

    public static void glCopyTextureSubImage2D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCopyTextureSubImage2D;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglCopyTextureSubImage2D(n, n2, n3, n4, n5, n6, n7, n8, l);
    }

    static native void nglCopyTextureSubImage2D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8);

    public static void glCopyTextureSubImage3D(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCopyTextureSubImage3D;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglCopyTextureSubImage3D(n, n2, n3, n4, n5, n6, n7, n8, n9, l);
    }

    static native void nglCopyTextureSubImage3D(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, long var9);

    public static void glTextureParameterf(int n, int n2, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureParameterf;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglTextureParameterf(n, n2, f, l);
    }

    static native void nglTextureParameterf(int var0, int var1, float var2, long var3);

    public static void glTextureParameter(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL45.nglTextureParameterfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglTextureParameterfv(int var0, int var1, long var2, long var4);

    public static void glTextureParameteri(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureParameteri;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglTextureParameteri(n, n2, n3, l);
    }

    static native void nglTextureParameteri(int var0, int var1, int var2, long var3);

    public static void glTextureParameterI(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureParameterIiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL45.nglTextureParameterIiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglTextureParameterIiv(int var0, int var1, long var2, long var4);

    public static void glTextureParameterIu(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureParameterIuiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL45.nglTextureParameterIuiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglTextureParameterIuiv(int var0, int var1, long var2, long var4);

    public static void glTextureParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL45.nglTextureParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglTextureParameteriv(int var0, int var1, long var2, long var4);

    public static void glGenerateTextureMipmap(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenerateTextureMipmap;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglGenerateTextureMipmap(n, l);
    }

    static native void nglGenerateTextureMipmap(int var0, long var1);

    public static void glBindTextureUnit(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindTextureUnit;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglBindTextureUnit(n, n2, l);
    }

    static native void nglBindTextureUnit(int var0, int var1, long var2);

    public static void glGetTextureImage(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL45.nglGetTextureImage(n, n2, n3, n4, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetTextureImage(int n, int n2, int n3, int n4, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        GL45.nglGetTextureImage(n, n2, n3, n4, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetTextureImage(int n, int n2, int n3, int n4, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        GL45.nglGetTextureImage(n, n2, n3, n4, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetTextureImage(int n, int n2, int n3, int n4, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglGetTextureImage(n, n2, n3, n4, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetTextureImage(int n, int n2, int n3, int n4, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        GL45.nglGetTextureImage(n, n2, n3, n4, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetTextureImage(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glGetTextureImage(int n, int n2, int n3, int n4, int n5, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetTextureImage;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        GL45.nglGetTextureImageBO(n, n2, n3, n4, n5, l, l2);
    }

    static native void nglGetTextureImageBO(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glGetCompressedTextureImage(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCompressedTextureImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL45.nglGetCompressedTextureImage(n, n2, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetCompressedTextureImage(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCompressedTextureImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglGetCompressedTextureImage(n, n2, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetCompressedTextureImage(int n, int n2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCompressedTextureImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        GL45.nglGetCompressedTextureImage(n, n2, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetCompressedTextureImage(int var0, int var1, int var2, long var3, long var5);

    public static void glGetCompressedTextureImage(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetCompressedTextureImage;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        GL45.nglGetCompressedTextureImageBO(n, n2, n3, l, l2);
    }

    static native void nglGetCompressedTextureImageBO(int var0, int var1, int var2, long var3, long var5);

    public static void glGetTextureLevelParameter(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureLevelParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 1);
        GL45.nglGetTextureLevelParameterfv(n, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetTextureLevelParameterfv(int var0, int var1, int var2, long var3, long var5);

    public static float glGetTextureLevelParameterf(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTextureLevelParameterfv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        GL45.nglGetTextureLevelParameterfv(n, n2, n3, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetTextureLevelParameter(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureLevelParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL45.nglGetTextureLevelParameteriv(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetTextureLevelParameteriv(int var0, int var1, int var2, long var3, long var5);

    public static int glGetTextureLevelParameteri(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTextureLevelParameteriv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglGetTextureLevelParameteriv(n, n2, n3, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetTextureParameter(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureParameterfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 1);
        GL45.nglGetTextureParameterfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetTextureParameterfv(int var0, int var1, long var2, long var4);

    public static float glGetTextureParameterf(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTextureParameterfv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        GL45.nglGetTextureParameterfv(n, n2, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetTextureParameterI(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureParameterIiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL45.nglGetTextureParameterIiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetTextureParameterIiv(int var0, int var1, long var2, long var4);

    public static int glGetTextureParameterIi(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTextureParameterIiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglGetTextureParameterIiv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetTextureParameterIu(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureParameterIuiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL45.nglGetTextureParameterIuiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetTextureParameterIuiv(int var0, int var1, long var2, long var4);

    public static int glGetTextureParameterIui(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTextureParameterIuiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglGetTextureParameterIuiv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetTextureParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL45.nglGetTextureParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetTextureParameteriv(int var0, int var1, long var2, long var4);

    public static int glGetTextureParameteri(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetTextureParameteriv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglGetTextureParameteriv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glCreateVertexArrays(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateVertexArrays;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglCreateVertexArrays(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglCreateVertexArrays(int var0, long var1, long var3);

    public static int glCreateVertexArrays() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glCreateVertexArrays;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglCreateVertexArrays(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glDisableVertexArrayAttrib(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDisableVertexArrayAttrib;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglDisableVertexArrayAttrib(n, n2, l);
    }

    static native void nglDisableVertexArrayAttrib(int var0, int var1, long var2);

    public static void glEnableVertexArrayAttrib(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEnableVertexArrayAttrib;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglEnableVertexArrayAttrib(n, n2, l);
    }

    static native void nglEnableVertexArrayAttrib(int var0, int var1, long var2);

    public static void glVertexArrayElementBuffer(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexArrayElementBuffer;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglVertexArrayElementBuffer(n, n2, l);
    }

    static native void nglVertexArrayElementBuffer(int var0, int var1, long var2);

    public static void glVertexArrayVertexBuffer(int n, int n2, int n3, long l, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glVertexArrayVertexBuffer;
        BufferChecks.checkFunctionAddress(l2);
        GL45.nglVertexArrayVertexBuffer(n, n2, n3, l, n4, l2);
    }

    static native void nglVertexArrayVertexBuffer(int var0, int var1, int var2, long var3, int var5, long var6);

    public static void glVertexArrayVertexBuffers(int n, int n2, int n3, IntBuffer intBuffer, PointerBuffer pointerBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexArrayVertexBuffers;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, n3);
        }
        if (pointerBuffer != null) {
            BufferChecks.checkBuffer(pointerBuffer, n3);
        }
        if (intBuffer2 != null) {
            BufferChecks.checkBuffer(intBuffer2, n3);
        }
        GL45.nglVertexArrayVertexBuffers(n, n2, n3, MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddressSafe(pointerBuffer), MemoryUtil.getAddressSafe(intBuffer2), l);
    }

    static native void nglVertexArrayVertexBuffers(int var0, int var1, int var2, long var3, long var5, long var7, long var9);

    public static void glVertexArrayAttribFormat(int n, int n2, int n3, int n4, boolean bl, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexArrayAttribFormat;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglVertexArrayAttribFormat(n, n2, n3, n4, bl, n5, l);
    }

    static native void nglVertexArrayAttribFormat(int var0, int var1, int var2, int var3, boolean var4, int var5, long var6);

    public static void glVertexArrayAttribIFormat(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexArrayAttribIFormat;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglVertexArrayAttribIFormat(n, n2, n3, n4, n5, l);
    }

    static native void nglVertexArrayAttribIFormat(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glVertexArrayAttribLFormat(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexArrayAttribLFormat;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglVertexArrayAttribLFormat(n, n2, n3, n4, n5, l);
    }

    static native void nglVertexArrayAttribLFormat(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glVertexArrayAttribBinding(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexArrayAttribBinding;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglVertexArrayAttribBinding(n, n2, n3, l);
    }

    static native void nglVertexArrayAttribBinding(int var0, int var1, int var2, long var3);

    public static void glVertexArrayBindingDivisor(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexArrayBindingDivisor;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglVertexArrayBindingDivisor(n, n2, n3, l);
    }

    static native void nglVertexArrayBindingDivisor(int var0, int var1, int var2, long var3);

    public static void glGetVertexArray(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexArrayiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL45.nglGetVertexArrayiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetVertexArrayiv(int var0, int var1, long var2, long var4);

    public static int glGetVertexArray(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetVertexArrayiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglGetVertexArrayiv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetVertexArrayIndexed(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexArrayIndexediv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL45.nglGetVertexArrayIndexediv(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetVertexArrayIndexediv(int var0, int var1, int var2, long var3, long var5);

    public static int glGetVertexArrayIndexed(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetVertexArrayIndexediv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglGetVertexArrayIndexediv(n, n2, n3, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetVertexArrayIndexed64i(int n, int n2, int n3, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexArrayIndexed64iv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 1);
        GL45.nglGetVertexArrayIndexed64iv(n, n2, n3, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetVertexArrayIndexed64iv(int var0, int var1, int var2, long var3, long var5);

    public static long glGetVertexArrayIndexed64i(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetVertexArrayIndexed64iv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferLong((ContextCapabilities)object);
        GL45.nglGetVertexArrayIndexed64iv(n, n2, n3, MemoryUtil.getAddress((LongBuffer)object), l);
        return ((LongBuffer)object).get(0);
    }

    public static void glCreateSamplers(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateSamplers;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglCreateSamplers(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglCreateSamplers(int var0, long var1, long var3);

    public static int glCreateSamplers() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glCreateSamplers;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglCreateSamplers(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glCreateProgramPipelines(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateProgramPipelines;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglCreateProgramPipelines(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglCreateProgramPipelines(int var0, long var1, long var3);

    public static int glCreateProgramPipelines() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glCreateProgramPipelines;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglCreateProgramPipelines(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glCreateQueries(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateQueries;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglCreateQueries(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglCreateQueries(int var0, int var1, long var2, long var4);

    public static int glCreateQueries(int n) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glCreateQueries;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL45.nglCreateQueries(n, 1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glMemoryBarrierByRegion(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMemoryBarrierByRegion;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglMemoryBarrierByRegion(n, l);
    }

    static native void nglMemoryBarrierByRegion(int var0, long var1);

    public static void glGetTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureSubImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL45.nglGetTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureSubImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        GL45.nglGetTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureSubImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        GL45.nglGetTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureSubImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglGetTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetTextureSubImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        GL45.nglGetTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetTextureSubImage(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9, int var10, long var11, long var13);

    public static void glGetTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, int n11, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetTextureSubImage;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        GL45.nglGetTextureSubImageBO(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, n11, l, l2);
    }

    static native void nglGetTextureSubImageBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9, int var10, long var11, long var13);

    public static void glGetCompressedTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCompressedTextureSubImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL45.nglGetCompressedTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetCompressedTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCompressedTextureSubImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        GL45.nglGetCompressedTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetCompressedTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCompressedTextureSubImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        GL45.nglGetCompressedTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetCompressedTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCompressedTextureSubImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglGetCompressedTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetCompressedTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCompressedTextureSubImage;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        GL45.nglGetCompressedTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetCompressedTextureSubImage(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, long var9, long var11);

    public static void glGetCompressedTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetCompressedTextureSubImage;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        GL45.nglGetCompressedTextureSubImageBO(n, n2, n3, n4, n5, n6, n7, n8, n9, l, l2);
    }

    static native void nglGetCompressedTextureSubImageBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, long var9, long var11);

    public static void glTextureBarrier() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureBarrier;
        BufferChecks.checkFunctionAddress(l);
        GL45.nglTextureBarrier(l);
    }

    static native void nglTextureBarrier(long var0);

    public static int glGetGraphicsResetStatus() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetGraphicsResetStatus;
        BufferChecks.checkFunctionAddress(l);
        int n = GL45.nglGetGraphicsResetStatus(l);
        return n;
    }

    static native int nglGetGraphicsResetStatus(long var0);

    public static void glReadnPixels(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadnPixels;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        GL45.nglReadnPixels(n, n2, n3, n4, n5, n6, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glReadnPixels(int n, int n2, int n3, int n4, int n5, int n6, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadnPixels;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        GL45.nglReadnPixels(n, n2, n3, n4, n5, n6, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glReadnPixels(int n, int n2, int n3, int n4, int n5, int n6, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadnPixels;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        GL45.nglReadnPixels(n, n2, n3, n4, n5, n6, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glReadnPixels(int n, int n2, int n3, int n4, int n5, int n6, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadnPixels;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglReadnPixels(n, n2, n3, n4, n5, n6, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glReadnPixels(int n, int n2, int n3, int n4, int n5, int n6, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadnPixels;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        GL45.nglReadnPixels(n, n2, n3, n4, n5, n6, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglReadnPixels(int var0, int var1, int var2, int var3, int var4, int var5, int var6, long var7, long var9);

    public static void glReadnPixels(int n, int n2, int n3, int n4, int n5, int n6, int n7, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glReadnPixels;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        GL45.nglReadnPixelsBO(n, n2, n3, n4, n5, n6, n7, l, l2);
    }

    static native void nglReadnPixelsBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, long var7, long var9);

    public static void glGetnUniform(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnUniformfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL45.nglGetnUniformfv(n, n2, floatBuffer.remaining(), MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetnUniformfv(int var0, int var1, int var2, long var3, long var5);

    public static void glGetnUniform(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnUniformiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglGetnUniformiv(n, n2, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetnUniformiv(int var0, int var1, int var2, long var3, long var5);

    public static void glGetnUniformu(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnUniformuiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL45.nglGetnUniformuiv(n, n2, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetnUniformuiv(int var0, int var1, int var2, long var3, long var5);
}
