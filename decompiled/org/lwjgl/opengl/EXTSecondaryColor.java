/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.StateTracker;

public final class EXTSecondaryColor {
    public static final int GL_COLOR_SUM_EXT = 33880;
    public static final int GL_CURRENT_SECONDARY_COLOR_EXT = 33881;
    public static final int GL_SECONDARY_COLOR_ARRAY_SIZE_EXT = 33882;
    public static final int GL_SECONDARY_COLOR_ARRAY_TYPE_EXT = 33883;
    public static final int GL_SECONDARY_COLOR_ARRAY_STRIDE_EXT = 33884;
    public static final int GL_SECONDARY_COLOR_ARRAY_POINTER_EXT = 33885;
    public static final int GL_SECONDARY_COLOR_ARRAY_EXT = 33886;

    private EXTSecondaryColor() {
    }

    public static void glSecondaryColor3bEXT(byte by, byte by2, byte by3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSecondaryColor3bEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTSecondaryColor.nglSecondaryColor3bEXT(by, by2, by3, l);
    }

    static native void nglSecondaryColor3bEXT(byte var0, byte var1, byte var2, long var3);

    public static void glSecondaryColor3fEXT(float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSecondaryColor3fEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTSecondaryColor.nglSecondaryColor3fEXT(f, f2, f3, l);
    }

    static native void nglSecondaryColor3fEXT(float var0, float var1, float var2, long var3);

    public static void glSecondaryColor3dEXT(double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSecondaryColor3dEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTSecondaryColor.nglSecondaryColor3dEXT(d, d2, d3, l);
    }

    static native void nglSecondaryColor3dEXT(double var0, double var2, double var4, long var6);

    public static void glSecondaryColor3ubEXT(byte by, byte by2, byte by3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSecondaryColor3ubEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTSecondaryColor.nglSecondaryColor3ubEXT(by, by2, by3, l);
    }

    static native void nglSecondaryColor3ubEXT(byte var0, byte var1, byte var2, long var3);

    public static void glSecondaryColorPointerEXT(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSecondaryColorPointerEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).EXT_secondary_color_glSecondaryColorPointerEXT_pPointer = doubleBuffer;
        }
        EXTSecondaryColor.nglSecondaryColorPointerEXT(n, 5130, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glSecondaryColorPointerEXT(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSecondaryColorPointerEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).EXT_secondary_color_glSecondaryColorPointerEXT_pPointer = floatBuffer;
        }
        EXTSecondaryColor.nglSecondaryColorPointerEXT(n, 5126, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glSecondaryColorPointerEXT(int n, boolean bl, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSecondaryColorPointerEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).EXT_secondary_color_glSecondaryColorPointerEXT_pPointer = byteBuffer;
        }
        EXTSecondaryColor.nglSecondaryColorPointerEXT(n, bl ? 5121 : 5120, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglSecondaryColorPointerEXT(int var0, int var1, int var2, long var3, long var5);

    public static void glSecondaryColorPointerEXT(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glSecondaryColorPointerEXT;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        EXTSecondaryColor.nglSecondaryColorPointerEXTBO(n, n2, n3, l, l2);
    }

    static native void nglSecondaryColorPointerEXTBO(int var0, int var1, int var2, long var3, long var5);
}
