/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.MouseInfo;
import java.awt.PointerInfo;
import java.security.PrivilegedExceptionAction;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
static final class AWTUtil.2
implements PrivilegedExceptionAction<PointerInfo> {
    AWTUtil.2() {
    }

    @Override
    public final PointerInfo run() {
        return MouseInfo.getPointerInfo();
    }
}
