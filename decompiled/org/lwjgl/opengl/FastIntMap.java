/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.util.Iterator;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
final class FastIntMap<V>
implements Iterable<Entry<V>> {
    private Entry[] table;
    private int size;
    private int mask;
    private int capacity;
    private int threshold;

    FastIntMap() {
        this(16, 0.75f);
    }

    FastIntMap(int n) {
        this(n, 0.75f);
    }

    FastIntMap(int n, float f) {
        if (n > 0x40000000) {
            throw new IllegalArgumentException("initialCapacity is too large.");
        }
        if (n < 0) {
            throw new IllegalArgumentException("initialCapacity must be greater than zero.");
        }
        if (f <= 0.0f) {
            throw new IllegalArgumentException("initialCapacity must be greater than zero.");
        }
        this.capacity = 1;
        while (this.capacity < n) {
            this.capacity <<= 1;
        }
        this.threshold = (int)((float)this.capacity * f);
        this.table = new Entry[this.capacity];
        this.mask = this.capacity - 1;
    }

    private int index(int n) {
        return FastIntMap.index(n, this.mask);
    }

    private static int index(int n, int n2) {
        return n & n2;
    }

    public final V put(int n, V v) {
        Entry[] entryArray = this.table;
        int n2 = this.index(n);
        Entry entry = entryArray[n2];
        while (entry != null) {
            if (entry.key == n) {
                Object t = entry.value;
                entry.value = v;
                return (V)t;
            }
            entry = entry.next;
        }
        entryArray[n2] = new Entry<V>(n, v, entryArray[n2]);
        if (this.size++ >= this.threshold) {
            this.rehash(entryArray);
        }
        return null;
    }

    private void rehash(Entry<V>[] entryArray) {
        int n = 2 * this.capacity;
        int n2 = n - 1;
        Entry[] entryArray2 = new Entry[n];
        for (int i = 0; i < entryArray.length; ++i) {
            Entry entry;
            Entry<Object> entry2 = entryArray[i];
            if (entry2 == null) continue;
            do {
                entry = entry2.next;
                int n3 = FastIntMap.index(entry2.key, n2);
                entry2.next = entryArray2[n3];
                entryArray2[n3] = entry2;
            } while ((entry2 = entry) != null);
        }
        this.table = entryArray2;
        this.capacity = n;
        this.mask = n2;
        this.threshold <<= 1;
    }

    public final V get(int n) {
        int n2 = this.index(n);
        Entry entry = this.table[n2];
        while (entry != null) {
            if (entry.key == n) {
                return (V)entry.value;
            }
            entry = entry.next;
        }
        return null;
    }

    public final boolean containsValue(Object object) {
        Entry[] entryArray = this.table;
        for (int i = this.table.length - 1; i >= 0; --i) {
            Entry entry = entryArray[i];
            while (entry != null) {
                if (entry.value.equals(object)) {
                    return true;
                }
                entry = entry.next;
            }
        }
        return false;
    }

    public final boolean containsKey(int n) {
        int n2 = this.index(n);
        Entry entry = this.table[n2];
        while (entry != null) {
            if (entry.key == n) {
                return true;
            }
            entry = entry.next;
        }
        return false;
    }

    public final V remove(int n) {
        Entry entry;
        int n2 = this.index(n);
        Entry entry2 = entry = this.table[n2];
        while (entry2 != null) {
            Entry entry3 = entry2.next;
            if (entry2.key == n) {
                --this.size;
                if (entry == entry2) {
                    this.table[n2] = entry3;
                } else {
                    entry.next = entry3;
                }
                return (V)entry2.value;
            }
            entry = entry2;
            entry2 = entry3;
        }
        return null;
    }

    public final int size() {
        return this.size;
    }

    public final boolean isEmpty() {
        return this.size == 0;
    }

    public final void clear() {
        Entry[] entryArray = this.table;
        for (int i = this.table.length - 1; i >= 0; --i) {
            entryArray[i] = null;
        }
        this.size = 0;
    }

    public final EntryIterator iterator() {
        return new EntryIterator();
    }

    /*
     * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
     */
    static final class Entry<T> {
        final int key;
        T value;
        Entry<T> next;

        Entry(int n, T t, Entry<T> entry) {
            this.key = n;
            this.value = t;
            this.next = entry;
        }

        public final int getKey() {
            return this.key;
        }

        public final T getValue() {
            return this.value;
        }
    }

    /*
     * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
     */
    public class EntryIterator
    implements Iterator<Entry<V>> {
        private int nextIndex;
        private Entry<V> current;

        EntryIterator() {
            this.reset();
        }

        public void reset() {
            int n;
            this.current = null;
            Entry[] entryArray = FastIntMap.this.table;
            for (n = entryArray.length - 1; n >= 0 && entryArray[n] == null; --n) {
            }
            this.nextIndex = n;
        }

        @Override
        public boolean hasNext() {
            if (this.nextIndex >= 0) {
                return true;
            }
            Entry entry = this.current;
            return entry != null && entry.next != null;
        }

        @Override
        public Entry<V> next() {
            Entry entry = this.current;
            if (entry != null && (entry = entry.next) != null) {
                this.current = entry;
                return entry;
            }
            Entry[] entryArray = FastIntMap.this.table;
            int n = this.nextIndex;
            entry = this.current = entryArray[n];
            while (--n >= 0 && entryArray[n] == null) {
            }
            this.nextIndex = n;
            return entry;
        }

        @Override
        public void remove() {
            FastIntMap.this.remove(this.current.key);
        }
    }
}
