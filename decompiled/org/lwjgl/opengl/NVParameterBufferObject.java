/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVParameterBufferObject {
    public static final int GL_MAX_PROGRAM_PARAMETER_BUFFER_BINDINGS_NV = 36256;
    public static final int GL_MAX_PROGRAM_PARAMETER_BUFFER_SIZE_NV = 36257;
    public static final int GL_VERTEX_PROGRAM_PARAMETER_BUFFER_NV = 36258;
    public static final int GL_GEOMETRY_PROGRAM_PARAMETER_BUFFER_NV = 36259;
    public static final int GL_FRAGMENT_PROGRAM_PARAMETER_BUFFER_NV = 36260;

    private NVParameterBufferObject() {
    }

    public static void glProgramBufferParametersNV(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramBufferParametersfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        NVParameterBufferObject.nglProgramBufferParametersfvNV(n, n2, n3, floatBuffer.remaining() >> 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramBufferParametersfvNV(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glProgramBufferParametersINV(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramBufferParametersIivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        NVParameterBufferObject.nglProgramBufferParametersIivNV(n, n2, n3, intBuffer.remaining() >> 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramBufferParametersIivNV(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glProgramBufferParametersIuNV(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramBufferParametersIuivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        NVParameterBufferObject.nglProgramBufferParametersIuivNV(n, n2, n3, intBuffer.remaining() >> 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglProgramBufferParametersIuivNV(int var0, int var1, int var2, int var3, long var4, long var6);
}
