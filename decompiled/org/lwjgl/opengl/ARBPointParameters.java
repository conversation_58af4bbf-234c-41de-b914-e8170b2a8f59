/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.FloatBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ARBPointParameters {
    public static final int GL_POINT_SIZE_MIN_ARB = 33062;
    public static final int GL_POINT_SIZE_MAX_ARB = 33063;
    public static final int GL_POINT_FADE_THRESHOLD_SIZE_ARB = 33064;
    public static final int GL_POINT_DISTANCE_ATTENUATION_ARB = 33065;

    private ARBPointParameters() {
    }

    public static void glPointParameterfARB(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPointParameterfARB;
        BufferChecks.checkFunctionAddress(l);
        ARBPointParameters.nglPointParameterfARB(n, f, l);
    }

    static native void nglPointParameterfARB(int var0, float var1, long var2);

    public static void glPointParameterARB(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPointParameterfvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ARBPointParameters.nglPointParameterfvARB(n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglPointParameterfvARB(int var0, long var1, long var3);
}
