/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferUtils;
import org.lwjgl.LWJGLException;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.opengl.EventQueue;
import org.lwjgl.opengl.WindowsDisplay;

final class WindowsMouse {
    private final long hwnd;
    private final int mouse_button_count;
    private final boolean has_wheel;
    private final EventQueue event_queue = new EventQueue(22);
    private final ByteBuffer mouse_event = ByteBuffer.allocate(22);
    private final Object blank_cursor;
    private boolean mouse_grabbed;
    private byte[] button_states;
    private int accum_dx;
    private int accum_dy;
    private int accum_dwheel;
    private int last_x;
    private int last_y;

    WindowsMouse(long l) {
        this.hwnd = l;
        this.mouse_button_count = Math.min(5, WindowsDisplay.getSystemMetrics(43));
        this.has_wheel = WindowsDisplay.getSystemMetrics(75) != 0;
        this.blank_cursor = this.createBlankCursor();
        this.button_states = new byte[this.mouse_button_count];
    }

    private Object createBlankCursor() {
        int n = WindowsDisplay.getSystemMetrics(13);
        int n2 = WindowsDisplay.getSystemMetrics(14);
        IntBuffer intBuffer = BufferUtils.createIntBuffer(n * n2);
        return WindowsDisplay.doCreateCursor(n, n2, 0, 0, 1, intBuffer, null);
    }

    public final boolean isGrabbed() {
        return this.mouse_grabbed;
    }

    public final boolean hasWheel() {
        return this.has_wheel;
    }

    public final int getButtonCount() {
        return this.mouse_button_count;
    }

    public final void poll(IntBuffer intBuffer, ByteBuffer byteBuffer, WindowsDisplay windowsDisplay) {
        int n;
        for (n = 0; n < intBuffer.remaining(); ++n) {
            IntBuffer intBuffer2 = intBuffer;
            intBuffer2.put(intBuffer2.position() + n, 0);
        }
        n = this.mouse_button_count;
        IntBuffer intBuffer3 = intBuffer;
        intBuffer3.put(intBuffer3.position() + 2, this.accum_dwheel);
        if (n > this.button_states.length) {
            n = this.button_states.length;
        }
        for (int i = 0; i < n; ++i) {
            ByteBuffer byteBuffer2 = byteBuffer;
            byteBuffer2.put(byteBuffer2.position() + i, this.button_states[i]);
        }
        if (this.isGrabbed()) {
            IntBuffer intBuffer4 = intBuffer;
            intBuffer4.put(intBuffer4.position(), this.accum_dx);
            IntBuffer intBuffer5 = intBuffer;
            intBuffer5.put(intBuffer5.position() + 1, this.accum_dy);
            if (windowsDisplay.isActive() && windowsDisplay.isVisible() && (this.accum_dx != 0 || this.accum_dy != 0)) {
                this.centerCursor();
            }
        } else {
            IntBuffer intBuffer6 = intBuffer;
            intBuffer6.put(intBuffer6.position(), this.last_x);
            IntBuffer intBuffer7 = intBuffer;
            intBuffer7.put(intBuffer7.position() + 1, this.last_y);
        }
        WindowsMouse windowsMouse = this;
        this.accum_dwheel = 0;
        windowsMouse.accum_dy = 0;
        windowsMouse.accum_dx = 0;
    }

    private void putMouseEventWithCoords(byte by, byte by2, int n, int n2, int n3, long l) {
        this.mouse_event.clear();
        this.mouse_event.put(by).put(by2).putInt(n).putInt(n2).putInt(n3).putLong(l);
        this.mouse_event.flip();
        this.event_queue.putEvent(this.mouse_event);
    }

    private void putMouseEvent(byte by, byte by2, int n, long l) {
        if (this.mouse_grabbed) {
            this.putMouseEventWithCoords(by, by2, 0, 0, n, l);
            return;
        }
        this.putMouseEventWithCoords(by, by2, this.last_x, this.last_y, n, l);
    }

    public final void read(ByteBuffer byteBuffer) {
        this.event_queue.copyEvents(byteBuffer);
    }

    public final Object getBlankCursor() {
        return this.blank_cursor;
    }

    public final void grab(boolean bl, boolean bl2) {
        if (bl) {
            if (!this.mouse_grabbed) {
                this.mouse_grabbed = true;
                if (bl2) {
                    try {
                        WindowsDisplay.setupCursorClipping(this.hwnd);
                    }
                    catch (LWJGLException lWJGLException) {
                        LWJGLUtil.log("Failed to setup cursor clipping: " + lWJGLException);
                    }
                    this.centerCursor();
                }
            }
        } else if (this.mouse_grabbed) {
            this.mouse_grabbed = false;
            WindowsDisplay.resetCursorClipping();
        }
        this.event_queue.clearEvents();
    }

    public final void handleMouseScrolled(int n, long l) {
        this.accum_dwheel += n;
        this.putMouseEvent((byte)-1, (byte)0, n, l * 1000000L);
    }

    private void centerCursor() {
        WindowsDisplay.centerCursor(this.hwnd);
    }

    public final void setPosition(int n, int n2) {
        this.last_x = n;
        this.last_y = n2;
    }

    public final void destroy() {
        WindowsDisplay.doDestroyCursor(this.blank_cursor);
    }

    public final void handleMouseMoved(int n, int n2, long l) {
        int n3 = n - this.last_x;
        int n4 = n2 - this.last_y;
        if (n3 != 0 || n4 != 0) {
            this.accum_dx += n3;
            this.accum_dy += n4;
            this.last_x = n;
            this.last_y = n2;
            long l2 = l * 1000000L;
            if (this.mouse_grabbed) {
                this.putMouseEventWithCoords((byte)-1, (byte)0, n3, n4, 0, l2);
                return;
            }
            this.putMouseEventWithCoords((byte)-1, (byte)0, n, n2, 0, l2);
        }
    }

    public final void handleMouseButton(byte by, byte by2, long l) {
        this.putMouseEvent(by, by2, 0, l * 1000000L);
        if (by < this.button_states.length) {
            this.button_states[by] = by2 != 0 ? (byte)1 : 0;
        }
    }
}
