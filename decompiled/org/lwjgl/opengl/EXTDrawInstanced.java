/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;

public final class EXTDrawInstanced {
    private EXTDrawInstanced() {
    }

    public static void glDrawArraysInstancedEXT(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawArraysInstancedEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTDrawInstanced.nglDrawArraysInstancedEXT(n, n2, n3, n4, l);
    }

    static native void nglDrawArraysInstancedEXT(int var0, int var1, int var2, int var3, long var4);

    public static void glDrawElementsInstancedEXT(int n, ByteBuffer byteBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsInstancedEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        EXTDrawInstanced.nglDrawElementsInstancedEXT(n, byteBuffer.remaining(), 5121, MemoryUtil.getAddress(byteBuffer), n2, l);
    }

    public static void glDrawElementsInstancedEXT(int n, IntBuffer intBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsInstancedEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        EXTDrawInstanced.nglDrawElementsInstancedEXT(n, intBuffer.remaining(), 5125, MemoryUtil.getAddress(intBuffer), n2, l);
    }

    public static void glDrawElementsInstancedEXT(int n, ShortBuffer shortBuffer, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementsInstancedEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureElementVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        EXTDrawInstanced.nglDrawElementsInstancedEXT(n, shortBuffer.remaining(), 5123, MemoryUtil.getAddress(shortBuffer), n2, l);
    }

    static native void nglDrawElementsInstancedEXT(int var0, int var1, int var2, long var3, int var5, long var6);

    public static void glDrawElementsInstancedEXT(int n, int n2, int n3, long l, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glDrawElementsInstancedEXT;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureElementVBOenabled(contextCapabilities);
        EXTDrawInstanced.nglDrawElementsInstancedEXTBO(n, n2, n3, l, n4, l2);
    }

    static native void nglDrawElementsInstancedEXTBO(int var0, int var1, int var2, long var3, int var5, long var6);
}
