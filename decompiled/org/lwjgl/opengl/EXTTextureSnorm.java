/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

public final class EXTTextureSnorm {
    public static final int GL_RED_SNORM = 36752;
    public static final int GL_RG_SNORM = 36753;
    public static final int GL_RGB_SNORM = 36754;
    public static final int GL_RGBA_SNORM = 36755;
    public static final int GL_ALPHA_SNORM = 36880;
    public static final int GL_LUMINANCE_SNORM = 36881;
    public static final int GL_LUMINANCE_ALPHA_SNORM = 36882;
    public static final int GL_INTENSITY_SNORM = 36883;
    public static final int GL_R8_SNORM = 36756;
    public static final int GL_RG8_SNORM = 36757;
    public static final int GL_RGB8_SNORM = 36758;
    public static final int GL_RGBA8_SNORM = 36759;
    public static final int GL_ALPHA8_SNORM = 36884;
    public static final int GL_LUMINANCE8_SNORM = 36885;
    public static final int GL_LUMINANCE8_ALPHA8_SNORM = 36886;
    public static final int GL_INTENSITY8_SNORM = 36887;
    public static final int GL_R16_SNORM = 36760;
    public static final int GL_RG16_SNORM = 36761;
    public static final int GL_RGB16_SNORM = 36762;
    public static final int GL_RGBA16_SNORM = 36763;
    public static final int GL_ALPHA16_SNORM = 36888;
    public static final int GL_LUMINANCE16_SNORM = 36889;
    public static final int GL_LUMINANCE16_ALPHA16_SNORM = 36890;
    public static final int GL_INTENSITY16_SNORM = 36891;
    public static final int GL_SIGNED_NORMALIZED = 36764;

    private EXTTextureSnorm() {
    }
}
