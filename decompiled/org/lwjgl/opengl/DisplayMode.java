/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

public final class DisplayMode {
    private final int width;
    private final int height;
    private final int bpp;
    private final int freq;
    private final boolean fullscreen;

    public DisplayMode(int n, int n2) {
        this(n, n2, 0, 0, false);
    }

    DisplayMode(int n, int n2, int n3, int n4) {
        this(n, n2, n3, n4, true);
    }

    private DisplayMode(int n, int n2, int n3, int n4, boolean bl) {
        this.width = n;
        this.height = n2;
        this.bpp = n3;
        this.freq = n4;
        this.fullscreen = bl;
    }

    public final boolean isFullscreenCapable() {
        return this.fullscreen;
    }

    public final int getWidth() {
        return this.width;
    }

    public final int getHeight() {
        return this.height;
    }

    public final int getBitsPerPixel() {
        return this.bpp;
    }

    public final int getFrequency() {
        return this.freq;
    }

    public final boolean equals(Object object) {
        if (object == null || !(object instanceof DisplayMode)) {
            return false;
        }
        object = (DisplayMode)object;
        return ((DisplayMode)object).width == this.width && ((DisplayMode)object).height == this.height && ((DisplayMode)object).bpp == this.bpp && ((DisplayMode)object).freq == this.freq;
    }

    public final int hashCode() {
        return this.width ^ this.height ^ this.freq ^ this.bpp;
    }

    public final String toString() {
        StringBuilder stringBuilder = new StringBuilder(32);
        stringBuilder.append(this.width);
        stringBuilder.append(" x ");
        stringBuilder.append(this.height);
        stringBuilder.append(" x ");
        stringBuilder.append(this.bpp);
        stringBuilder.append(" @");
        stringBuilder.append(this.freq);
        stringBuilder.append("Hz");
        return stringBuilder.toString();
    }
}
