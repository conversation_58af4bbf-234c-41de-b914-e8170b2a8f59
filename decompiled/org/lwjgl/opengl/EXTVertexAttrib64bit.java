/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.DoubleBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ARBVertexAttrib64bit;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.StateTracker;

public final class EXTVertexAttrib64bit {
    public static final int GL_DOUBLE_VEC2_EXT = 36860;
    public static final int GL_DOUBLE_VEC3_EXT = 36861;
    public static final int GL_DOUBLE_VEC4_EXT = 36862;
    public static final int GL_DOUBLE_MAT2_EXT = 36678;
    public static final int GL_DOUBLE_MAT3_EXT = 36679;
    public static final int GL_DOUBLE_MAT4_EXT = 36680;
    public static final int GL_DOUBLE_MAT2x3_EXT = 36681;
    public static final int GL_DOUBLE_MAT2x4_EXT = 36682;
    public static final int GL_DOUBLE_MAT3x2_EXT = 36683;
    public static final int GL_DOUBLE_MAT3x4_EXT = 36684;
    public static final int GL_DOUBLE_MAT4x2_EXT = 36685;
    public static final int GL_DOUBLE_MAT4x3_EXT = 36686;

    private EXTVertexAttrib64bit() {
    }

    public static void glVertexAttribL1dEXT(int n, double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL1dEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexAttrib64bit.nglVertexAttribL1dEXT(n, d, l);
    }

    static native void nglVertexAttribL1dEXT(int var0, double var1, long var3);

    public static void glVertexAttribL2dEXT(int n, double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL2dEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexAttrib64bit.nglVertexAttribL2dEXT(n, d, d2, l);
    }

    static native void nglVertexAttribL2dEXT(int var0, double var1, double var3, long var5);

    public static void glVertexAttribL3dEXT(int n, double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL3dEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexAttrib64bit.nglVertexAttribL3dEXT(n, d, d2, d3, l);
    }

    static native void nglVertexAttribL3dEXT(int var0, double var1, double var3, double var5, long var7);

    public static void glVertexAttribL4dEXT(int n, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL4dEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTVertexAttrib64bit.nglVertexAttribL4dEXT(n, d, d2, d3, d4, l);
    }

    static native void nglVertexAttribL4dEXT(int var0, double var1, double var3, double var5, double var7, long var9);

    public static void glVertexAttribL1EXT(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL1dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 1);
        EXTVertexAttrib64bit.nglVertexAttribL1dvEXT(n, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVertexAttribL1dvEXT(int var0, long var1, long var3);

    public static void glVertexAttribL2EXT(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL2dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 2);
        EXTVertexAttrib64bit.nglVertexAttribL2dvEXT(n, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVertexAttribL2dvEXT(int var0, long var1, long var3);

    public static void glVertexAttribL3EXT(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL3dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 3);
        EXTVertexAttrib64bit.nglVertexAttribL3dvEXT(n, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVertexAttribL3dvEXT(int var0, long var1, long var3);

    public static void glVertexAttribL4EXT(int n, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL4dvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        EXTVertexAttrib64bit.nglVertexAttribL4dvEXT(n, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVertexAttribL4dvEXT(int var0, long var1, long var3);

    public static void glVertexAttribLPointerEXT(int n, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribLPointerEXT;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = doubleBuffer;
        }
        EXTVertexAttrib64bit.nglVertexAttribLPointerEXT(n, n2, 5130, n3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglVertexAttribLPointerEXT(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glVertexAttribLPointerEXT(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glVertexAttribLPointerEXT;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        EXTVertexAttrib64bit.nglVertexAttribLPointerEXTBO(n, n2, 5130, n3, l, l2);
    }

    static native void nglVertexAttribLPointerEXTBO(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glGetVertexAttribLEXT(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribLdvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        EXTVertexAttrib64bit.nglGetVertexAttribLdvEXT(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetVertexAttribLdvEXT(int var0, int var1, long var2, long var4);

    public static void glVertexArrayVertexAttribLOffsetEXT(int n, int n2, int n3, int n4, int n5, int n6, long l) {
        ARBVertexAttrib64bit.glVertexArrayVertexAttribLOffsetEXT(n, n2, n3, n4, n5, n6, l);
    }
}
