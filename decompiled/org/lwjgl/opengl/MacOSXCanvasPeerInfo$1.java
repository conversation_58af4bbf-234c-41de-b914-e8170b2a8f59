/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Canvas;
import java.awt.event.ComponentEvent;
import java.awt.event.ComponentListener;
import org.lwjgl.opengl.MacOSXCanvasPeerInfo;

class MacOSXCanvasPeerInfo.1
implements ComponentListener {
    final /* synthetic */ Canvas val$component;

    MacOSXCanvasPeerInfo.1(Canvas canvas) {
        this.val$component = canvas;
    }

    public void componentHidden(ComponentEvent componentEvent) {
    }

    public void componentMoved(ComponentEvent componentEvent) {
        MacOSXCanvasPeerInfo.reSetLayerBounds(this.val$component, MacOSXCanvasPeerInfo.this.getHandle());
    }

    public void componentResized(ComponentEvent componentEvent) {
        MacOSXCanvasPeerInfo.reSetLayerBounds(this.val$component, MacOSXCanvasPeerInfo.this.getHandle());
    }

    public void componentShown(ComponentEvent componentEvent) {
    }

    public String toString() {
        return "CanvasPeerInfoListener";
    }
}
