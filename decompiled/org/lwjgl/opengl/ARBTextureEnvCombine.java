/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

public final class ARBTextureEnvCombine {
    public static final int GL_COMBINE_ARB = 34160;
    public static final int GL_COMBINE_RGB_ARB = 34161;
    public static final int GL_COMBINE_ALPHA_ARB = 34162;
    public static final int GL_SOURCE0_RGB_ARB = 34176;
    public static final int GL_SOURCE1_RGB_ARB = 34177;
    public static final int GL_SOURCE2_RGB_ARB = 34178;
    public static final int GL_SOURCE0_ALPHA_ARB = 34184;
    public static final int GL_SOURCE1_ALPHA_ARB = 34185;
    public static final int GL_SOURCE2_ALPHA_ARB = 34186;
    public static final int GL_OPERAND0_RGB_ARB = 34192;
    public static final int GL_OPERAND1_RGB_ARB = 34193;
    public static final int GL_OPERAND2_RGB_ARB = 34194;
    public static final int GL_OPERAND0_ALPHA_ARB = 34200;
    public static final int GL_OPERAND1_ALPHA_ARB = 34201;
    public static final int GL_OPERAND2_ALPHA_ARB = 34202;
    public static final int GL_RGB_SCALE_ARB = 34163;
    public static final int GL_ADD_SIGNED_ARB = 34164;
    public static final int GL_INTERPOLATE_ARB = 34165;
    public static final int GL_SUBTRACT_ARB = 34023;
    public static final int GL_CONSTANT_ARB = 34166;
    public static final int GL_PRIMARY_COLOR_ARB = 34167;
    public static final int GL_PREVIOUS_ARB = 34168;

    private ARBTextureEnvCombine() {
    }
}
