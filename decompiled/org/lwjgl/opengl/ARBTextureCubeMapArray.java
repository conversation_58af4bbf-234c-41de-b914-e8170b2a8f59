/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

public final class ARBTextureCubeMapArray {
    public static final int GL_TEXTURE_CUBE_MAP_ARRAY_ARB = 36873;
    public static final int GL_TEXTURE_BINDING_CUBE_MAP_ARRAY_ARB = 36874;
    public static final int GL_PROXY_TEXTURE_CUBE_MAP_ARRAY_ARB = 36875;
    public static final int GL_SAMPLER_CUBE_MAP_ARRAY_ARB = 36876;
    public static final int GL_SAMPLER_CUBE_MAP_ARRAY_SHADOW_ARB = 36877;
    public static final int GL_INT_SAMPLER_CUBE_MAP_ARRAY_ARB = 36878;
    public static final int GL_UNSIGNED_INT_SAMPLER_CUBE_MAP_ARRAY_ARB = 36879;

    private ARBTextureCubeMapArray() {
    }
}
