/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class AMDDrawBuffersBlend {
    private AMDDrawBuffersBlend() {
    }

    public static void glBlendFuncIndexedAMD(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendFuncIndexedAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDDrawBuffersBlend.nglBlendFuncIndexedAMD(n, n2, n3, l);
    }

    static native void nglBlendFuncIndexedAMD(int var0, int var1, int var2, long var3);

    public static void glBlendFuncSeparateIndexedAMD(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendFuncSeparateIndexedAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDDrawBuffersBlend.nglBlendFuncSeparateIndexedAMD(n, n2, n3, n4, n5, l);
    }

    static native void nglBlendFuncSeparateIndexedAMD(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glBlendEquationIndexedAMD(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendEquationIndexedAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDDrawBuffersBlend.nglBlendEquationIndexedAMD(n, n2, l);
    }

    static native void nglBlendEquationIndexedAMD(int var0, int var1, long var2);

    public static void glBlendEquationSeparateIndexedAMD(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendEquationSeparateIndexedAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDDrawBuffersBlend.nglBlendEquationSeparateIndexedAMD(n, n2, n3, l);
    }

    static native void nglBlendEquationSeparateIndexedAMD(int var0, int var1, int var2, long var3);
}
