/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;

public final class ARBRobustness {
    public static final int GL_GUILTY_CONTEXT_RESET_ARB = 33363;
    public static final int GL_INNOCENT_CONTEXT_RESET_ARB = 33364;
    public static final int GL_UNKNOWN_CONTEXT_RESET_ARB = 33365;
    public static final int GL_RESET_NOTIFICATION_STRATEGY_ARB = 33366;
    public static final int GL_LOSE_CONTEXT_ON_RESET_ARB = 33362;
    public static final int GL_NO_RESET_NOTIFICATION_ARB = 33377;
    public static final int GL_CONTEXT_FLAG_ROBUST_ACCESS_BIT_ARB = 4;

    private ARBRobustness() {
    }

    public static int glGetGraphicsResetStatusARB() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetGraphicsResetStatusARB;
        BufferChecks.checkFunctionAddress(l);
        int n = ARBRobustness.nglGetGraphicsResetStatusARB(l);
        return n;
    }

    static native int nglGetGraphicsResetStatusARB(long var0);

    public static void glGetnMapdvARB(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnMapdvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnMapdvARB(n, n2, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetnMapdvARB(int var0, int var1, int var2, long var3, long var5);

    public static void glGetnMapfvARB(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnMapfvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnMapfvARB(n, n2, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetnMapfvARB(int var0, int var1, int var2, long var3, long var5);

    public static void glGetnMapivARB(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnMapivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnMapivARB(n, n2, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetnMapivARB(int var0, int var1, int var2, long var3, long var5);

    public static void glGetnPixelMapfvARB(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnPixelMapfvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnPixelMapfvARB(n, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetnPixelMapfvARB(int var0, int var1, long var2, long var4);

    public static void glGetnPixelMapuivARB(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnPixelMapuivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnPixelMapuivARB(n, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetnPixelMapuivARB(int var0, int var1, long var2, long var4);

    public static void glGetnPixelMapusvARB(int n, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnPixelMapusvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnPixelMapusvARB(n, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetnPixelMapusvARB(int var0, int var1, long var2, long var4);

    public static void glGetnPolygonStippleARB(ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnPolygonStippleARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnPolygonStippleARB(byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetnPolygonStippleARB(int var0, long var1, long var3);

    public static void glGetnTexImageARB(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnTexImageARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnTexImageARB(n, n2, n3, n4, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnTexImageARB(int n, int n2, int n3, int n4, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnTexImageARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnTexImageARB(n, n2, n3, n4, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnTexImageARB(int n, int n2, int n3, int n4, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnTexImageARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnTexImageARB(n, n2, n3, n4, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnTexImageARB(int n, int n2, int n3, int n4, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnTexImageARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnTexImageARB(n, n2, n3, n4, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnTexImageARB(int n, int n2, int n3, int n4, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnTexImageARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnTexImageARB(n, n2, n3, n4, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetnTexImageARB(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glGetnTexImageARB(int n, int n2, int n3, int n4, int n5, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetnTexImageARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        ARBRobustness.nglGetnTexImageARBBO(n, n2, n3, n4, n5, l, l2);
    }

    static native void nglGetnTexImageARBBO(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glReadnPixelsARB(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadnPixelsARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglReadnPixelsARB(n, n2, n3, n4, n5, n6, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glReadnPixelsARB(int n, int n2, int n3, int n4, int n5, int n6, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadnPixelsARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglReadnPixelsARB(n, n2, n3, n4, n5, n6, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glReadnPixelsARB(int n, int n2, int n3, int n4, int n5, int n6, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadnPixelsARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglReadnPixelsARB(n, n2, n3, n4, n5, n6, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glReadnPixelsARB(int n, int n2, int n3, int n4, int n5, int n6, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadnPixelsARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglReadnPixelsARB(n, n2, n3, n4, n5, n6, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glReadnPixelsARB(int n, int n2, int n3, int n4, int n5, int n6, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glReadnPixelsARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglReadnPixelsARB(n, n2, n3, n4, n5, n6, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglReadnPixelsARB(int var0, int var1, int var2, int var3, int var4, int var5, int var6, long var7, long var9);

    public static void glReadnPixelsARB(int n, int n2, int n3, int n4, int n5, int n6, int n7, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glReadnPixelsARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        ARBRobustness.nglReadnPixelsARBBO(n, n2, n3, n4, n5, n6, n7, l, l2);
    }

    static native void nglReadnPixelsARBBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, long var7, long var9);

    public static void glGetnColorTableARB(int n, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnColorTableARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnColorTableARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnColorTableARB(int n, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnColorTableARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnColorTableARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnColorTableARB(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnColorTableARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnColorTableARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetnColorTableARB(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glGetnConvolutionFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnConvolutionFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnConvolutionFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnConvolutionFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnConvolutionFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnConvolutionFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnConvolutionFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnConvolutionFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnConvolutionFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnConvolutionFilterARB(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnConvolutionFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnConvolutionFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnConvolutionFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnConvolutionFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnConvolutionFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetnConvolutionFilterARB(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glGetnConvolutionFilterARB(int n, int n2, int n3, int n4, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetnConvolutionFilterARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        ARBRobustness.nglGetnConvolutionFilterARBBO(n, n2, n3, n4, l, l2);
    }

    static native void nglGetnConvolutionFilterARBBO(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, ByteBuffer byteBuffer2, ByteBuffer byteBuffer3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        BufferChecks.checkDirect(byteBuffer3);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), byteBuffer2.remaining(), MemoryUtil.getAddress(byteBuffer2), MemoryUtil.getAddress(byteBuffer3), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, ByteBuffer byteBuffer2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), byteBuffer2.remaining(), MemoryUtil.getAddress(byteBuffer2), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, ByteBuffer byteBuffer2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), byteBuffer2.remaining(), MemoryUtil.getAddress(byteBuffer2), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, ByteBuffer byteBuffer2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), byteBuffer2.remaining(), MemoryUtil.getAddress(byteBuffer2), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, ByteBuffer byteBuffer2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), byteBuffer2.remaining(), MemoryUtil.getAddress(byteBuffer2), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, FloatBuffer floatBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, FloatBuffer floatBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, FloatBuffer floatBuffer, FloatBuffer floatBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(floatBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(floatBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, FloatBuffer floatBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, FloatBuffer floatBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, IntBuffer intBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, IntBuffer intBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, IntBuffer intBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, IntBuffer intBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, ShortBuffer shortBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, ShortBuffer shortBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, ShortBuffer shortBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ByteBuffer byteBuffer, ShortBuffer shortBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), doubleBuffer2.remaining() << 3, MemoryUtil.getAddress(doubleBuffer2), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2, DoubleBuffer doubleBuffer3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        BufferChecks.checkDirect(doubleBuffer3);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), doubleBuffer2.remaining() << 3, MemoryUtil.getAddress(doubleBuffer2), MemoryUtil.getAddress(doubleBuffer3), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), doubleBuffer2.remaining() << 3, MemoryUtil.getAddress(doubleBuffer2), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), doubleBuffer2.remaining() << 3, MemoryUtil.getAddress(doubleBuffer2), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), doubleBuffer2.remaining() << 3, MemoryUtil.getAddress(doubleBuffer2), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, FloatBuffer floatBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, FloatBuffer floatBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, FloatBuffer floatBuffer, FloatBuffer floatBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(floatBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(floatBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, FloatBuffer floatBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, FloatBuffer floatBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, IntBuffer intBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, IntBuffer intBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, IntBuffer intBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, ByteBuffer byteBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, ByteBuffer byteBuffer, FloatBuffer floatBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(floatBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(floatBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, ByteBuffer byteBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, ByteBuffer byteBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, DoubleBuffer doubleBuffer, FloatBuffer floatBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(floatBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(floatBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, DoubleBuffer doubleBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, FloatBuffer floatBuffer2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(floatBuffer2);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), floatBuffer2.remaining() << 2, MemoryUtil.getAddress(floatBuffer2), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, FloatBuffer floatBuffer2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(floatBuffer2);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), floatBuffer2.remaining() << 2, MemoryUtil.getAddress(floatBuffer2), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, FloatBuffer floatBuffer2, FloatBuffer floatBuffer3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(floatBuffer2);
        BufferChecks.checkDirect(floatBuffer3);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), floatBuffer2.remaining() << 2, MemoryUtil.getAddress(floatBuffer2), MemoryUtil.getAddress(floatBuffer3), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, FloatBuffer floatBuffer2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(floatBuffer2);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), floatBuffer2.remaining() << 2, MemoryUtil.getAddress(floatBuffer2), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, FloatBuffer floatBuffer2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(floatBuffer2);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), floatBuffer2.remaining() << 2, MemoryUtil.getAddress(floatBuffer2), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, IntBuffer intBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, IntBuffer intBuffer, FloatBuffer floatBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(floatBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(floatBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, IntBuffer intBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, ShortBuffer shortBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, ShortBuffer shortBuffer, FloatBuffer floatBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(floatBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(floatBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, ShortBuffer shortBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, FloatBuffer floatBuffer, ShortBuffer shortBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, ByteBuffer byteBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, ByteBuffer byteBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, ByteBuffer byteBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, ByteBuffer byteBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, DoubleBuffer doubleBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, DoubleBuffer doubleBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, FloatBuffer floatBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, FloatBuffer floatBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, FloatBuffer floatBuffer, FloatBuffer floatBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(floatBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(floatBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, FloatBuffer floatBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, FloatBuffer floatBuffer, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, IntBuffer intBuffer2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), intBuffer2.remaining() << 2, MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, IntBuffer intBuffer2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), intBuffer2.remaining() << 2, MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, IntBuffer intBuffer2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), intBuffer2.remaining() << 2, MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, IntBuffer intBuffer2, IntBuffer intBuffer3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        BufferChecks.checkDirect(intBuffer3);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), intBuffer2.remaining() << 2, MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(intBuffer3), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, IntBuffer intBuffer2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), intBuffer2.remaining() << 2, MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(shortBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, ShortBuffer shortBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, ShortBuffer shortBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, ShortBuffer shortBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, IntBuffer intBuffer, ShortBuffer shortBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, ByteBuffer byteBuffer, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(byteBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, ByteBuffer byteBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, ByteBuffer byteBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, ByteBuffer byteBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, ByteBuffer byteBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer, DoubleBuffer doubleBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(doubleBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(doubleBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, DoubleBuffer doubleBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, FloatBuffer floatBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, FloatBuffer floatBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, FloatBuffer floatBuffer, FloatBuffer floatBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(floatBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(floatBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, FloatBuffer floatBuffer, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, FloatBuffer floatBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(floatBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, IntBuffer intBuffer, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, IntBuffer intBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(intBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, IntBuffer intBuffer, ShortBuffer shortBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(shortBuffer2), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, ShortBuffer shortBuffer2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), shortBuffer2.remaining() << 1, MemoryUtil.getAddress(shortBuffer2), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, ShortBuffer shortBuffer2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), shortBuffer2.remaining() << 1, MemoryUtil.getAddress(shortBuffer2), MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, ShortBuffer shortBuffer2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), shortBuffer2.remaining() << 1, MemoryUtil.getAddress(shortBuffer2), MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, ShortBuffer shortBuffer2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), shortBuffer2.remaining() << 1, MemoryUtil.getAddress(shortBuffer2), MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, ShortBuffer shortBuffer, ShortBuffer shortBuffer2, ShortBuffer shortBuffer3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        BufferChecks.checkDirect(shortBuffer2);
        BufferChecks.checkDirect(shortBuffer3);
        ARBRobustness.nglGetnSeparableFilterARB(n, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), shortBuffer2.remaining() << 1, MemoryUtil.getAddress(shortBuffer2), MemoryUtil.getAddress(shortBuffer3), l);
    }

    static native void nglGetnSeparableFilterARB(int var0, int var1, int var2, int var3, long var4, int var6, long var7, long var9, long var11);

    public static void glGetnSeparableFilterARB(int n, int n2, int n3, int n4, long l, int n5, long l2, long l3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l4 = contextCapabilities.glGetnSeparableFilterARB;
        BufferChecks.checkFunctionAddress(l4);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        ARBRobustness.nglGetnSeparableFilterARBBO(n, n2, n3, n4, l, n5, l2, l3, l4);
    }

    static native void nglGetnSeparableFilterARBBO(int var0, int var1, int var2, int var3, long var4, int var6, long var7, long var9, long var11);

    public static void glGetnHistogramARB(int n, boolean bl, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnHistogramARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnHistogramARB(n, bl, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnHistogramARB(int n, boolean bl, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnHistogramARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnHistogramARB(n, bl, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnHistogramARB(int n, boolean bl, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnHistogramARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnHistogramARB(n, bl, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnHistogramARB(int n, boolean bl, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnHistogramARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnHistogramARB(n, bl, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnHistogramARB(int n, boolean bl, int n2, int n3, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnHistogramARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnHistogramARB(n, bl, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetnHistogramARB(int var0, boolean var1, int var2, int var3, int var4, long var5, long var7);

    public static void glGetnHistogramARB(int n, boolean bl, int n2, int n3, int n4, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetnHistogramARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        ARBRobustness.nglGetnHistogramARBBO(n, bl, n2, n3, n4, l, l2);
    }

    static native void nglGetnHistogramARBBO(int var0, boolean var1, int var2, int var3, int var4, long var5, long var7);

    public static void glGetnMinmaxARB(int n, boolean bl, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnMinmaxARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnMinmaxARB(n, bl, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnMinmaxARB(int n, boolean bl, int n2, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnMinmaxARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnMinmaxARB(n, bl, n2, n3, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glGetnMinmaxARB(int n, boolean bl, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnMinmaxARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnMinmaxARB(n, bl, n2, n3, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glGetnMinmaxARB(int n, boolean bl, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnMinmaxARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnMinmaxARB(n, bl, n2, n3, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnMinmaxARB(int n, boolean bl, int n2, int n3, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnMinmaxARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnMinmaxARB(n, bl, n2, n3, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetnMinmaxARB(int var0, boolean var1, int var2, int var3, int var4, long var5, long var7);

    public static void glGetnMinmaxARB(int n, boolean bl, int n2, int n3, int n4, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetnMinmaxARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        ARBRobustness.nglGetnMinmaxARBBO(n, bl, n2, n3, n4, l, l2);
    }

    static native void nglGetnMinmaxARBBO(int var0, boolean var1, int var2, int var3, int var4, long var5, long var7);

    public static void glGetnCompressedTexImageARB(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnCompressedTexImageARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBRobustness.nglGetnCompressedTexImageARB(n, n2, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glGetnCompressedTexImageARB(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnCompressedTexImageARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnCompressedTexImageARB(n, n2, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glGetnCompressedTexImageARB(int n, int n2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnCompressedTexImageARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        ARBRobustness.nglGetnCompressedTexImageARB(n, n2, shortBuffer.remaining() << 1, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglGetnCompressedTexImageARB(int var0, int var1, int var2, long var3, long var5);

    public static void glGetnCompressedTexImageARB(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetnCompressedTexImageARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        ARBRobustness.nglGetnCompressedTexImageARBBO(n, n2, n3, l, l2);
    }

    static native void nglGetnCompressedTexImageARBBO(int var0, int var1, int var2, long var3, long var5);

    public static void glGetnUniformfvARB(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnUniformfvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        ARBRobustness.nglGetnUniformfvARB(n, n2, floatBuffer.remaining() << 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetnUniformfvARB(int var0, int var1, int var2, long var3, long var5);

    public static void glGetnUniformivARB(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnUniformivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnUniformivARB(n, n2, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetnUniformivARB(int var0, int var1, int var2, long var3, long var5);

    public static void glGetnUniformuivARB(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnUniformuivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBRobustness.nglGetnUniformuivARB(n, n2, intBuffer.remaining() << 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetnUniformuivARB(int var0, int var1, int var2, long var3, long var5);

    public static void glGetnUniformdvARB(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetnUniformdvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(doubleBuffer);
        ARBRobustness.nglGetnUniformdvARB(n, n2, doubleBuffer.remaining() << 3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetnUniformdvARB(int var0, int var1, int var2, long var3, long var5);
}
