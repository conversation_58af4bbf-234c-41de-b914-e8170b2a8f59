/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.opengl.GL42;

public final class ARBInternalformatQuery {
    public static final int GL_NUM_SAMPLE_COUNTS = 37760;

    private ARBInternalformatQuery() {
    }

    public static void glGetInternalformat(int n, int n2, int n3, IntBuffer intBuffer) {
        GL42.glGetInternalformat(n, n2, n3, intBuffer);
    }

    public static int glGetInternalformat(int n, int n2, int n3) {
        return GL42.glGetInternalformat(n, n2, n3);
    }
}
