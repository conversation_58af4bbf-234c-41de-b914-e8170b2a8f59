/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.opengl.Util;

public class OpenGLException
extends RuntimeException {
    private static final long serialVersionUID = 1L;

    public OpenGLException(int n) {
        this(OpenGLException.createErrorMessage(n));
    }

    private static String createErrorMessage(int n) {
        String string = Util.translateGLErrorString(n);
        return string + " (" + n + ")";
    }

    public OpenGLException() {
    }

    public OpenGLException(String string) {
        super(string);
    }

    public OpenGLException(String string, Throwable throwable) {
        super(string, throwable);
    }

    public OpenGLException(Throwable throwable) {
        super(throwable);
    }
}
