/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Component;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import java.nio.ByteBuffer;
import org.lwjgl.opengl.EventQueue;

final class KeyboardEventQueue
extends EventQueue
implements KeyListener {
    private static final int[] KEY_MAP;
    private final byte[] key_states = new byte[256];
    private final ByteBuffer event = ByteBuffer.allocate(18);
    private final Component component;
    private boolean has_deferred_event;
    private long deferred_nanos;
    private int deferred_key_code;
    private int deferred_key_location;
    private byte deferred_key_state;
    private int deferred_character;

    KeyboardEventQueue(Component component) {
        super(18);
        this.component = component;
    }

    public final void register() {
        this.component.addKeyListener(this);
    }

    public final void unregister() {
    }

    private void putKeyboardEvent(int n, byte by, int n2, long l, boolean bl) {
        this.event.clear();
        this.event.putInt(n).put(by).putInt(n2).putLong(l).put(bl ? (byte)1 : 0);
        this.event.flip();
        KeyboardEventQueue keyboardEventQueue = this;
        keyboardEventQueue.putEvent(keyboardEventQueue.event);
    }

    public final synchronized void poll(ByteBuffer byteBuffer) {
        this.flushDeferredEvent();
        int n = byteBuffer.position();
        byteBuffer.put(this.key_states);
        byteBuffer.position(n);
    }

    public final synchronized void copyEvents(ByteBuffer byteBuffer) {
        this.flushDeferredEvent();
        super.copyEvents(byteBuffer);
    }

    private synchronized void handleKey(int n, int n2, byte by, int n3, long l) {
        if (n3 == 65535) {
            n3 = 0;
        }
        if (by == 1) {
            boolean bl = false;
            if (this.has_deferred_event) {
                if (l == this.deferred_nanos && this.deferred_key_code == n && this.deferred_key_location == n2) {
                    this.has_deferred_event = false;
                    bl = true;
                } else {
                    this.flushDeferredEvent();
                }
            }
            this.putKeyEvent(n, n2, by, n3, l, bl);
            return;
        }
        this.flushDeferredEvent();
        this.has_deferred_event = true;
        this.deferred_nanos = l;
        this.deferred_key_code = n;
        this.deferred_key_location = n2;
        this.deferred_key_state = by;
        this.deferred_character = n3;
    }

    private void flushDeferredEvent() {
        if (this.has_deferred_event) {
            KeyboardEventQueue keyboardEventQueue = this;
            keyboardEventQueue.putKeyEvent(keyboardEventQueue.deferred_key_code, this.deferred_key_location, this.deferred_key_state, this.deferred_character, this.deferred_nanos, false);
            this.has_deferred_event = false;
        }
    }

    private void putKeyEvent(int n, int n2, byte by, int n3, long l, boolean bl) {
        if (this.key_states[n = this.getMappedKeyCode(n, n2)] == by) {
            bl = true;
        }
        this.key_states[n] = by;
        n2 = n3 & 0xFFFF;
        this.putKeyboardEvent(n, by, n2, l, bl);
    }

    private int getMappedKeyCode(int n, int n2) {
        switch (n) {
            case 18: {
                if (n2 == 3) {
                    return 184;
                }
                return 56;
            }
            case 157: {
                if (n2 == 3) {
                    return 220;
                }
                return 219;
            }
            case 16: {
                if (n2 == 3) {
                    return 54;
                }
                return 42;
            }
            case 17: {
                if (n2 == 3) {
                    return 157;
                }
                return 29;
            }
        }
        return KEY_MAP[n];
    }

    public final void keyPressed(KeyEvent keyEvent) {
        this.handleKey(keyEvent.getKeyCode(), keyEvent.getKeyLocation(), (byte)1, keyEvent.getKeyChar(), keyEvent.getWhen() * 1000000L);
    }

    public final void keyReleased(KeyEvent keyEvent) {
        this.handleKey(keyEvent.getKeyCode(), keyEvent.getKeyLocation(), (byte)0, 0, keyEvent.getWhen() * 1000000L);
    }

    public final void keyTyped(KeyEvent keyEvent) {
    }

    static {
        int[] nArray = new int[65535];
        KEY_MAP = nArray;
        nArray[48] = 11;
        KeyboardEventQueue.KEY_MAP[49] = 2;
        KeyboardEventQueue.KEY_MAP[50] = 3;
        KeyboardEventQueue.KEY_MAP[51] = 4;
        KeyboardEventQueue.KEY_MAP[52] = 5;
        KeyboardEventQueue.KEY_MAP[53] = 6;
        KeyboardEventQueue.KEY_MAP[54] = 7;
        KeyboardEventQueue.KEY_MAP[55] = 8;
        KeyboardEventQueue.KEY_MAP[56] = 9;
        KeyboardEventQueue.KEY_MAP[57] = 10;
        KeyboardEventQueue.KEY_MAP[65] = 30;
        KeyboardEventQueue.KEY_MAP[107] = 78;
        KeyboardEventQueue.KEY_MAP[65406] = 184;
        KeyboardEventQueue.KEY_MAP[512] = 145;
        KeyboardEventQueue.KEY_MAP[66] = 48;
        KeyboardEventQueue.KEY_MAP[92] = 43;
        KeyboardEventQueue.KEY_MAP[8] = 14;
        KeyboardEventQueue.KEY_MAP[67] = 46;
        KeyboardEventQueue.KEY_MAP[20] = 58;
        KeyboardEventQueue.KEY_MAP[514] = 144;
        KeyboardEventQueue.KEY_MAP[93] = 27;
        KeyboardEventQueue.KEY_MAP[513] = 146;
        KeyboardEventQueue.KEY_MAP[44] = 51;
        KeyboardEventQueue.KEY_MAP[28] = 121;
        KeyboardEventQueue.KEY_MAP[68] = 32;
        KeyboardEventQueue.KEY_MAP[110] = 83;
        KeyboardEventQueue.KEY_MAP[127] = 211;
        KeyboardEventQueue.KEY_MAP[111] = 181;
        KeyboardEventQueue.KEY_MAP[40] = 208;
        KeyboardEventQueue.KEY_MAP[69] = 18;
        KeyboardEventQueue.KEY_MAP[35] = 207;
        KeyboardEventQueue.KEY_MAP[10] = 28;
        KeyboardEventQueue.KEY_MAP[61] = 13;
        KeyboardEventQueue.KEY_MAP[27] = 1;
        KeyboardEventQueue.KEY_MAP[70] = 33;
        KeyboardEventQueue.KEY_MAP[112] = 59;
        KeyboardEventQueue.KEY_MAP[121] = 68;
        KeyboardEventQueue.KEY_MAP[122] = 87;
        KeyboardEventQueue.KEY_MAP[123] = 88;
        KeyboardEventQueue.KEY_MAP[61440] = 100;
        KeyboardEventQueue.KEY_MAP[61441] = 101;
        KeyboardEventQueue.KEY_MAP[61442] = 102;
        KeyboardEventQueue.KEY_MAP[113] = 60;
        KeyboardEventQueue.KEY_MAP[114] = 61;
        KeyboardEventQueue.KEY_MAP[115] = 62;
        KeyboardEventQueue.KEY_MAP[116] = 63;
        KeyboardEventQueue.KEY_MAP[117] = 64;
        KeyboardEventQueue.KEY_MAP[118] = 65;
        KeyboardEventQueue.KEY_MAP[119] = 66;
        KeyboardEventQueue.KEY_MAP[120] = 67;
        KeyboardEventQueue.KEY_MAP[71] = 34;
        KeyboardEventQueue.KEY_MAP[72] = 35;
        KeyboardEventQueue.KEY_MAP[36] = 199;
        KeyboardEventQueue.KEY_MAP[73] = 23;
        KeyboardEventQueue.KEY_MAP[155] = 210;
        KeyboardEventQueue.KEY_MAP[74] = 36;
        KeyboardEventQueue.KEY_MAP[75] = 37;
        KeyboardEventQueue.KEY_MAP[21] = 112;
        KeyboardEventQueue.KEY_MAP[25] = 148;
        KeyboardEventQueue.KEY_MAP[76] = 38;
        KeyboardEventQueue.KEY_MAP[37] = 203;
        KeyboardEventQueue.KEY_MAP[77] = 50;
        KeyboardEventQueue.KEY_MAP[45] = 12;
        KeyboardEventQueue.KEY_MAP[106] = 55;
        KeyboardEventQueue.KEY_MAP[78] = 49;
        KeyboardEventQueue.KEY_MAP[144] = 69;
        KeyboardEventQueue.KEY_MAP[96] = 82;
        KeyboardEventQueue.KEY_MAP[97] = 79;
        KeyboardEventQueue.KEY_MAP[98] = 80;
        KeyboardEventQueue.KEY_MAP[99] = 81;
        KeyboardEventQueue.KEY_MAP[100] = 75;
        KeyboardEventQueue.KEY_MAP[101] = 76;
        KeyboardEventQueue.KEY_MAP[102] = 77;
        KeyboardEventQueue.KEY_MAP[103] = 71;
        KeyboardEventQueue.KEY_MAP[104] = 72;
        KeyboardEventQueue.KEY_MAP[105] = 73;
        KeyboardEventQueue.KEY_MAP[79] = 24;
        KeyboardEventQueue.KEY_MAP[91] = 26;
        KeyboardEventQueue.KEY_MAP[80] = 25;
        KeyboardEventQueue.KEY_MAP[34] = 209;
        KeyboardEventQueue.KEY_MAP[33] = 201;
        KeyboardEventQueue.KEY_MAP[19] = 197;
        KeyboardEventQueue.KEY_MAP[46] = 52;
        KeyboardEventQueue.KEY_MAP[81] = 16;
        KeyboardEventQueue.KEY_MAP[82] = 19;
        KeyboardEventQueue.KEY_MAP[39] = 205;
        KeyboardEventQueue.KEY_MAP[83] = 31;
        KeyboardEventQueue.KEY_MAP[145] = 70;
        KeyboardEventQueue.KEY_MAP[59] = 39;
        KeyboardEventQueue.KEY_MAP[108] = 83;
        KeyboardEventQueue.KEY_MAP[47] = 53;
        KeyboardEventQueue.KEY_MAP[32] = 57;
        KeyboardEventQueue.KEY_MAP[65480] = 149;
        KeyboardEventQueue.KEY_MAP[109] = 74;
        KeyboardEventQueue.KEY_MAP[84] = 20;
        KeyboardEventQueue.KEY_MAP[9] = 15;
        KeyboardEventQueue.KEY_MAP[85] = 22;
        KeyboardEventQueue.KEY_MAP[38] = 200;
        KeyboardEventQueue.KEY_MAP[86] = 47;
        KeyboardEventQueue.KEY_MAP[87] = 17;
        KeyboardEventQueue.KEY_MAP[88] = 45;
        KeyboardEventQueue.KEY_MAP[89] = 21;
        KeyboardEventQueue.KEY_MAP[90] = 44;
    }
}
