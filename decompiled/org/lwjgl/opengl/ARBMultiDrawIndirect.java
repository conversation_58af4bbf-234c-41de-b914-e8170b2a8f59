/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.opengl.GL43;

public final class ARBMultiDrawIndirect {
    private ARBMultiDrawIndirect() {
    }

    public static void glMultiDrawArraysIndirect(int n, ByteBuffer byteBuffer, int n2, int n3) {
        GL43.glMultiDrawArraysIndirect(n, byteBuffer, n2, n3);
    }

    public static void glMultiDrawArraysIndirect(int n, long l, int n2, int n3) {
        GL43.glMultiDrawArraysIndirect(n, l, n2, n3);
    }

    public static void glMultiDrawArraysIndirect(int n, IntBuffer intBuffer, int n2, int n3) {
        GL43.glMultiDrawArraysIndirect(n, intBuffer, n2, n3);
    }

    public static void glMultiDrawElementsIndirect(int n, int n2, ByteBuffer byteBuffer, int n3, int n4) {
        GL43.glMultiDrawElementsIndirect(n, n2, byteBuffer, n3, n4);
    }

    public static void glMultiDrawElementsIndirect(int n, int n2, long l, int n3, int n4) {
        GL43.glMultiDrawElementsIndirect(n, n2, l, n3, n4);
    }

    public static void glMultiDrawElementsIndirect(int n, int n2, IntBuffer intBuffer, int n3, int n4) {
        GL43.glMultiDrawElementsIndirect(n, n2, intBuffer, n3, n4);
    }
}
