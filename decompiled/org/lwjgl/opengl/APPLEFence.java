/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class APPLEFence {
    public static final int GL_DRAW_PIXELS_APPLE = 35338;
    public static final int GL_FENCE_APPLE = 35339;

    private APPLEFence() {
    }

    public static void glGenFencesAPPLE(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenFencesAPPLE;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        APPLEFence.nglGenFencesAPPLE(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenFencesAPPLE(int var0, long var1, long var3);

    public static int glGenFencesAPPLE() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenFencesAPPLE;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        APPLEFence.nglGenFencesAPPLE(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glDeleteFencesAPPLE(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteFencesAPPLE;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        APPLEFence.nglDeleteFencesAPPLE(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeleteFencesAPPLE(int var0, long var1, long var3);

    public static void glDeleteFencesAPPLE(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteFencesAPPLE;
        BufferChecks.checkFunctionAddress(l);
        APPLEFence.nglDeleteFencesAPPLE(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static void glSetFenceAPPLE(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSetFenceAPPLE;
        BufferChecks.checkFunctionAddress(l);
        APPLEFence.nglSetFenceAPPLE(n, l);
    }

    static native void nglSetFenceAPPLE(int var0, long var1);

    public static boolean glIsFenceAPPLE(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsFenceAPPLE;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = APPLEFence.nglIsFenceAPPLE(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsFenceAPPLE(int var0, long var1);

    public static boolean glTestFenceAPPLE(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTestFenceAPPLE;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = APPLEFence.nglTestFenceAPPLE(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglTestFenceAPPLE(int var0, long var1);

    public static void glFinishFenceAPPLE(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFinishFenceAPPLE;
        BufferChecks.checkFunctionAddress(l);
        APPLEFence.nglFinishFenceAPPLE(n, l);
    }

    static native void nglFinishFenceAPPLE(int var0, long var1);

    public static boolean glTestObjectAPPLE(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTestObjectAPPLE;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = APPLEFence.nglTestObjectAPPLE(n, n2, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglTestObjectAPPLE(int var0, int var1, long var2);

    public static void glFinishObjectAPPLE(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFinishObjectAPPLE;
        BufferChecks.checkFunctionAddress(l);
        APPLEFence.nglFinishObjectAPPLE(n, n2, l);
    }

    static native void nglFinishObjectAPPLE(int var0, int var1, long var2);
}
