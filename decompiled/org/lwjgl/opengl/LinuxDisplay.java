/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.lwjgl.opengles.GLContext
 *  org.lwjgl.opengles.PixelFormat
 */
package org.lwjgl.opengl;

import java.awt.Canvas;
import java.awt.event.FocusEvent;
import java.awt.event.FocusListener;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.Buffer;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.security.AccessController;
import java.security.PrivilegedAction;
import java.util.ArrayList;
import java.util.List;
import org.lwjgl.BufferUtils;
import org.lwjgl.LWJGLException;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.AWTCanvasImplementation;
import org.lwjgl.opengl.AWTGLCanvas;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.Display;
import org.lwjgl.opengl.DisplayImplementation;
import org.lwjgl.opengl.DisplayMode;
import org.lwjgl.opengl.DrawableGLES;
import org.lwjgl.opengl.DrawableLWJGL;
import org.lwjgl.opengl.GlobalLock;
import org.lwjgl.opengl.LinuxDisplayPeerInfo;
import org.lwjgl.opengl.LinuxEvent;
import org.lwjgl.opengl.LinuxKeyboard;
import org.lwjgl.opengl.LinuxMouse;
import org.lwjgl.opengl.LinuxPbufferPeerInfo;
import org.lwjgl.opengl.LinuxPeerInfo;
import org.lwjgl.opengl.PeerInfo;
import org.lwjgl.opengl.XRandR;
import org.lwjgl.opengles.GLContext;
import org.lwjgl.opengles.PixelFormat;

final class LinuxDisplay
implements DisplayImplementation {
    public static final int CurrentTime = 0;
    public static final int GrabSuccess = 0;
    public static final int AutoRepeatModeOff = 0;
    public static final int AutoRepeatModeOn = 1;
    public static final int AutoRepeatModeDefault = 2;
    public static final int None = 0;
    private static final int KeyPressMask = 1;
    private static final int KeyReleaseMask = 2;
    private static final int ButtonPressMask = 4;
    private static final int ButtonReleaseMask = 8;
    private static final int NotifyAncestor = 0;
    private static final int NotifyNonlinear = 3;
    private static final int NotifyPointer = 5;
    private static final int NotifyPointerRoot = 6;
    private static final int NotifyDetailNone = 7;
    private static final int SetModeInsert = 0;
    private static final int SaveSetRoot = 1;
    private static final int SaveSetUnmap = 1;
    private static final int X_SetInputFocus = 42;
    private static final int FULLSCREEN_LEGACY = 1;
    private static final int FULLSCREEN_NETWM = 2;
    private static final int WINDOWED = 3;
    private static int current_window_mode = 3;
    private static final int XRANDR = 10;
    private static final int XF86VIDMODE = 11;
    private static final int NONE = 12;
    private static long display;
    private static long current_window;
    private static long saved_error_handler;
    private static int display_connection_usage_count;
    private final LinuxEvent event_buffer = new LinuxEvent();
    private final LinuxEvent tmp_event_buffer = new LinuxEvent();
    private int current_displaymode_extension = 12;
    private long delete_atom;
    private PeerInfo peer_info;
    private ByteBuffer saved_gamma;
    private ByteBuffer current_gamma;
    private DisplayMode saved_mode;
    private DisplayMode current_mode;
    private boolean keyboard_grabbed;
    private boolean pointer_grabbed;
    private boolean input_released;
    private boolean grab;
    private boolean focused;
    private boolean minimized;
    private boolean dirty;
    private boolean close_requested;
    private long current_cursor;
    private long blank_cursor;
    private boolean mouseInside = true;
    private boolean resizable;
    private boolean resized;
    private int window_x;
    private int window_y;
    private int window_width;
    private int window_height;
    private Canvas parent;
    private long parent_window;
    private static boolean xembedded;
    private long parent_proxy_focus_window;
    private boolean parent_focused;
    private boolean parent_focus_changed;
    private long last_window_focus = 0L;
    private LinuxKeyboard keyboard;
    private LinuxMouse mouse;
    private String wm_class;
    private final FocusListener focus_listener = new FocusListener(){

        /*
         * WARNING - Removed try catching itself - possible behaviour change.
         */
        public void focusGained(FocusEvent object) {
            object = GlobalLock.lock;
            synchronized (object) {
                LinuxDisplay.this.parent_focused = true;
                LinuxDisplay.this.parent_focus_changed = true;
                return;
            }
        }

        /*
         * WARNING - Removed try catching itself - possible behaviour change.
         */
        public void focusLost(FocusEvent object) {
            object = GlobalLock.lock;
            synchronized (object) {
                LinuxDisplay.this.parent_focused = false;
                LinuxDisplay.this.parent_focus_changed = true;
                return;
            }
        }
    };

    LinuxDisplay() {
    }

    private static ByteBuffer getCurrentGammaRamp() {
        LinuxDisplay.lockAWT();
        try {
            block7: {
                ByteBuffer byteBuffer;
                LinuxDisplay.incDisplay();
                try {
                    if (!LinuxDisplay.isXF86VidModeSupported()) break block7;
                    byteBuffer = LinuxDisplay.nGetCurrentGammaRamp(LinuxDisplay.getDisplay(), LinuxDisplay.getDefaultScreen());
                }
                catch (Throwable throwable) {
                    LinuxDisplay.decDisplay();
                    throw throwable;
                }
                LinuxDisplay.decDisplay();
                return byteBuffer;
            }
            LinuxDisplay.decDisplay();
            return null;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native ByteBuffer nGetCurrentGammaRamp(long var0, int var2);

    private static int getBestDisplayModeExtension() {
        int n;
        if (LinuxDisplay.isXrandrSupported()) {
            LWJGLUtil.log("Using Xrandr for display mode switching");
            n = 10;
        } else if (LinuxDisplay.isXF86VidModeSupported()) {
            LWJGLUtil.log("Using XF86VidMode for display mode switching");
            n = 11;
        } else {
            LWJGLUtil.log("No display mode extensions available");
            n = 12;
        }
        return n;
    }

    private static boolean isXrandrSupported() {
        if (Display.getPrivilegedBoolean("LWJGL_DISABLE_XRANDR")) {
            return false;
        }
        LinuxDisplay.lockAWT();
        try {
            boolean bl;
            LinuxDisplay.incDisplay();
            try {
                bl = LinuxDisplay.nIsXrandrSupported(LinuxDisplay.getDisplay());
            }
            catch (Throwable throwable) {
                try {
                    LinuxDisplay.decDisplay();
                    throw throwable;
                }
                catch (LWJGLException lWJGLException) {
                    LWJGLUtil.log("Got exception while querying Xrandr support: " + lWJGLException);
                    return false;
                }
            }
            LinuxDisplay.decDisplay();
            return bl;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native boolean nIsXrandrSupported(long var0);

    private static boolean isXF86VidModeSupported() {
        LinuxDisplay.lockAWT();
        try {
            boolean bl;
            LinuxDisplay.incDisplay();
            try {
                bl = LinuxDisplay.nIsXF86VidModeSupported(LinuxDisplay.getDisplay());
            }
            catch (Throwable throwable) {
                try {
                    LinuxDisplay.decDisplay();
                    throw throwable;
                }
                catch (LWJGLException lWJGLException) {
                    LWJGLUtil.log("Got exception while querying XF86VM support: " + lWJGLException);
                    return false;
                }
            }
            LinuxDisplay.decDisplay();
            return bl;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native boolean nIsXF86VidModeSupported(long var0);

    private static boolean isNetWMFullscreenSupported() {
        if (Display.getPrivilegedBoolean("LWJGL_DISABLE_NETWM")) {
            return false;
        }
        LinuxDisplay.lockAWT();
        try {
            boolean bl;
            LinuxDisplay.incDisplay();
            try {
                bl = LinuxDisplay.nIsNetWMFullscreenSupported(LinuxDisplay.getDisplay(), LinuxDisplay.getDefaultScreen());
            }
            catch (Throwable throwable) {
                try {
                    LinuxDisplay.decDisplay();
                    throw throwable;
                }
                catch (LWJGLException lWJGLException) {
                    LWJGLUtil.log("Got exception while querying NetWM support: " + lWJGLException);
                    return false;
                }
            }
            LinuxDisplay.decDisplay();
            return bl;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native boolean nIsNetWMFullscreenSupported(long var0, int var2);

    static void lockAWT() {
        try {
            LinuxDisplay.nLockAWT();
            return;
        }
        catch (LWJGLException lWJGLException) {
            LWJGLUtil.log("Caught exception while locking AWT: " + lWJGLException);
            return;
        }
    }

    private static native void nLockAWT();

    static void unlockAWT() {
        try {
            LinuxDisplay.nUnlockAWT();
            return;
        }
        catch (LWJGLException lWJGLException) {
            LWJGLUtil.log("Caught exception while unlocking AWT: " + lWJGLException);
            return;
        }
    }

    private static native void nUnlockAWT();

    static void incDisplay() {
        if (display_connection_usage_count == 0) {
            try {
                org.lwjgl.opengl.GLContext.loadOpenGLLibrary();
                GLContext.loadOpenGLLibrary();
            }
            catch (Throwable throwable) {}
            saved_error_handler = LinuxDisplay.setErrorHandler();
            display = LinuxDisplay.openDisplay();
        }
        ++display_connection_usage_count;
    }

    private static native int callErrorHandler(long var0, long var2, long var4);

    private static native long setErrorHandler();

    private static native long resetErrorHandler(long var0);

    private static native void synchronize(long var0, boolean var2);

    private static int globalErrorHandler(long l, long l2, long l3, long l4, long l5, long l6, long l7) {
        if (xembedded && l6 == 42L) {
            return 0;
        }
        if (l == LinuxDisplay.getDisplay()) {
            String string = LinuxDisplay.getErrorText(l, l5);
            throw new LWJGLException("X Error - disp: 0x" + Long.toHexString(l3) + " serial: " + l4 + " error: " + string + " request_code: " + l6 + " minor_code: " + l7);
        }
        if (saved_error_handler != 0L) {
            return LinuxDisplay.callErrorHandler(saved_error_handler, l, l2);
        }
        return 0;
    }

    private static native String getErrorText(long var0, long var2);

    static void decDisplay() {
    }

    static native long openDisplay();

    static native void closeDisplay(long var0);

    private int getWindowMode(boolean bl) {
        if (bl) {
            if (this.current_displaymode_extension == 10 && LinuxDisplay.isNetWMFullscreenSupported()) {
                LWJGLUtil.log("Using NetWM for fullscreen window");
                return 2;
            }
            LWJGLUtil.log("Using legacy mode for fullscreen window");
            return 1;
        }
        return 3;
    }

    static long getDisplay() {
        if (display_connection_usage_count <= 0) {
            throw new InternalError("display_connection_usage_count = " + display_connection_usage_count);
        }
        return display;
    }

    static int getDefaultScreen() {
        return LinuxDisplay.nGetDefaultScreen(LinuxDisplay.getDisplay());
    }

    static native int nGetDefaultScreen(long var0);

    static long getWindow() {
        return current_window;
    }

    private void ungrabKeyboard() {
        if (this.keyboard_grabbed) {
            LinuxDisplay.nUngrabKeyboard(LinuxDisplay.getDisplay());
            this.keyboard_grabbed = false;
        }
    }

    static native int nUngrabKeyboard(long var0);

    private void grabKeyboard() {
        int n;
        if (!this.keyboard_grabbed && (n = LinuxDisplay.nGrabKeyboard(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow())) == 0) {
            this.keyboard_grabbed = true;
        }
    }

    static native int nGrabKeyboard(long var0, long var2);

    private void grabPointer() {
        int n;
        if (!this.pointer_grabbed && (n = LinuxDisplay.nGrabPointer(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow(), 0L)) == 0) {
            this.pointer_grabbed = true;
            if (LinuxDisplay.isLegacyFullscreen()) {
                LinuxDisplay.nSetViewPort(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow(), LinuxDisplay.getDefaultScreen());
            }
        }
    }

    static native int nGrabPointer(long var0, long var2, long var4);

    private static native void nSetViewPort(long var0, long var2, int var4);

    private void ungrabPointer() {
        if (this.pointer_grabbed) {
            this.pointer_grabbed = false;
            LinuxDisplay.nUngrabPointer(LinuxDisplay.getDisplay());
        }
    }

    static native int nUngrabPointer(long var0);

    private static boolean isFullscreen() {
        return current_window_mode == 1 || current_window_mode == 2;
    }

    private boolean shouldGrab() {
        return !this.input_released && this.grab && this.mouse != null;
    }

    private void updatePointerGrab() {
        if (LinuxDisplay.isFullscreen() || this.shouldGrab()) {
            this.grabPointer();
        } else {
            this.ungrabPointer();
        }
        this.updateCursor();
    }

    private void updateCursor() {
        long l = this.shouldGrab() ? this.blank_cursor : this.current_cursor;
        LinuxDisplay.nDefineCursor(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow(), l);
    }

    private static native void nDefineCursor(long var0, long var2, long var4);

    private static boolean isLegacyFullscreen() {
        return current_window_mode == 1;
    }

    private void updateKeyboardGrab() {
        if (LinuxDisplay.isLegacyFullscreen()) {
            this.grabKeyboard();
            return;
        }
        this.ungrabKeyboard();
    }

    public final void createWindow(DrawableLWJGL drawableLWJGL, DisplayMode displayMode, Canvas canvas, int n, int n2) {
        LinuxDisplay.lockAWT();
        try {
            LinuxDisplay.incDisplay();
            try {
                if (drawableLWJGL instanceof DrawableGLES) {
                    this.peer_info = new LinuxDisplayPeerInfo();
                }
                ByteBuffer byteBuffer = this.peer_info.lockAndGetHandle();
                try {
                    current_window_mode = this.getWindowMode(Display.isFullscreen());
                    if (current_window_mode != 3) {
                        Compiz.setLegacyFullscreenSupport(true);
                    }
                    boolean bl = Display.getPrivilegedBoolean("org.lwjgl.opengl.Window.undecorated") || current_window_mode != 3 && Display.getPrivilegedBoolean("org.lwjgl.opengl.Window.undecorated_fs");
                    this.parent = canvas;
                    this.parent_window = canvas != null ? LinuxDisplay.getHandle(canvas) : LinuxDisplay.getRootWindow(LinuxDisplay.getDisplay(), LinuxDisplay.getDefaultScreen());
                    this.resizable = Display.isResizable();
                    this.resized = false;
                    this.window_x = n;
                    this.window_y = n2;
                    this.window_width = displayMode.getWidth();
                    this.window_height = displayMode.getHeight();
                    if (displayMode.isFullscreenCapable() && this.current_displaymode_extension == 10) {
                        XRandR.Screen screen = XRandR.DisplayModetoScreen(Display.getDisplayMode());
                        n = screen.xPos;
                        n2 = screen.yPos;
                    }
                    current_window = LinuxDisplay.nCreateWindow(LinuxDisplay.getDisplay(), LinuxDisplay.getDefaultScreen(), byteBuffer, displayMode, current_window_mode, n, n2, bl, this.parent_window, this.resizable);
                    this.wm_class = Display.getPrivilegedString("LWJGL_WM_CLASS");
                    if (this.wm_class == null) {
                        this.wm_class = Display.getTitle();
                    }
                    this.setClassHint(Display.getTitle(), this.wm_class);
                    LinuxDisplay.mapRaised(LinuxDisplay.getDisplay(), current_window);
                    xembedded = canvas != null && LinuxDisplay.isAncestorXEmbedded(this.parent_window);
                    this.blank_cursor = LinuxDisplay.createBlankCursor();
                    this.current_cursor = 0L;
                    this.focused = false;
                    this.input_released = false;
                    this.pointer_grabbed = false;
                    this.keyboard_grabbed = false;
                    this.close_requested = false;
                    this.grab = false;
                    this.minimized = false;
                    this.dirty = true;
                    if (drawableLWJGL instanceof DrawableGLES) {
                        ((DrawableGLES)drawableLWJGL).initialize(current_window, LinuxDisplay.getDisplay(), 4, (PixelFormat)drawableLWJGL.getPixelFormat());
                    }
                    if (canvas != null) {
                        canvas.addFocusListener(this.focus_listener);
                        this.parent_focused = canvas.isFocusOwner();
                        this.parent_focus_changed = true;
                    }
                }
                finally {
                    this.peer_info.unlock();
                }
            }
            catch (LWJGLException lWJGLException) {
                LinuxDisplay.decDisplay();
                throw lWJGLException;
            }
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native long nCreateWindow(long var0, int var2, ByteBuffer var3, DisplayMode var4, int var5, int var6, int var7, boolean var8, long var9, boolean var11);

    private static native long getRootWindow(long var0, int var2);

    private static native boolean hasProperty(long var0, long var2, long var4);

    private static native long getParentWindow(long var0, long var2);

    private static native int getChildCount(long var0, long var2);

    private static native void mapRaised(long var0, long var2);

    private static native void reparentWindow(long var0, long var2, long var4, int var6, int var7);

    private static native long nGetInputFocus(long var0);

    private static native void nSetInputFocus(long var0, long var2, long var4);

    private static native void nSetWindowSize(long var0, long var2, int var4, int var5, boolean var6);

    private static native int nGetX(long var0, long var2);

    private static native int nGetY(long var0, long var2);

    private static native int nGetWidth(long var0, long var2);

    private static native int nGetHeight(long var0, long var2);

    private static boolean isAncestorXEmbedded(long l) {
        long l2 = LinuxDisplay.internAtom("_XEMBED_INFO", true);
        if (l2 != 0L) {
            long l3 = l;
            while (l3 != 0L) {
                if (LinuxDisplay.hasProperty(LinuxDisplay.getDisplay(), l3, l2)) {
                    return true;
                }
                l3 = LinuxDisplay.getParentWindow(LinuxDisplay.getDisplay(), l3);
            }
        }
        return false;
    }

    private static long getHandle(Canvas object) {
        AWTCanvasImplementation aWTCanvasImplementation = AWTGLCanvas.createImplementation();
        object = (LinuxPeerInfo)aWTCanvasImplementation.createPeerInfo((Canvas)object, null, null);
        ((PeerInfo)object).lockAndGetHandle();
        try {
            long l = ((LinuxPeerInfo)object).getDrawable();
            return l;
        }
        finally {
            ((PeerInfo)object).unlock();
        }
    }

    private void updateInputGrab() {
        this.updatePointerGrab();
        this.updateKeyboardGrab();
    }

    public final void destroyWindow() {
        LinuxDisplay.lockAWT();
        try {
            if (this.parent != null) {
                this.parent.removeFocusListener(this.focus_listener);
            }
            try {
                this.setNativeCursor(null);
            }
            catch (LWJGLException lWJGLException) {
                LWJGLUtil.log("Failed to reset cursor: " + lWJGLException.getMessage());
            }
            LinuxDisplay.nDestroyCursor(LinuxDisplay.getDisplay(), this.blank_cursor);
            this.blank_cursor = 0L;
            this.ungrabKeyboard();
            LinuxDisplay.nDestroyWindow(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow());
            LinuxDisplay.decDisplay();
            if (current_window_mode != 3) {
                Compiz.setLegacyFullscreenSupport(false);
            }
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    static native void nDestroyWindow(long var0, long var2);

    public final void switchDisplayMode(DisplayMode displayMode) {
        LinuxDisplay.lockAWT();
        try {
            this.switchDisplayModeOnTmpDisplay(displayMode);
            this.current_mode = displayMode;
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private void switchDisplayModeOnTmpDisplay(DisplayMode displayMode) {
        if (this.current_displaymode_extension == 10) {
            XRandR.setConfiguration(false, XRandR.DisplayModetoScreen(displayMode));
            return;
        }
        LinuxDisplay.incDisplay();
        try {
            LinuxDisplay.nSwitchDisplayMode(LinuxDisplay.getDisplay(), LinuxDisplay.getDefaultScreen(), this.current_displaymode_extension, displayMode);
            return;
        }
        finally {
            LinuxDisplay.decDisplay();
        }
    }

    private static native void nSwitchDisplayMode(long var0, int var2, int var3, DisplayMode var4);

    private static long internAtom(String string, boolean bl) {
        LinuxDisplay.incDisplay();
        try {
            long l = LinuxDisplay.nInternAtom(LinuxDisplay.getDisplay(), string, bl);
            return l;
        }
        finally {
            LinuxDisplay.decDisplay();
        }
    }

    static native long nInternAtom(long var0, String var2, boolean var3);

    public final void resetDisplayMode() {
        LinuxDisplay.lockAWT();
        try {
            if (this.current_displaymode_extension == 10) {
                AccessController.doPrivileged(new PrivilegedAction<Object>(){

                    @Override
                    public Object run() {
                        XRandR.restoreConfiguration();
                        return null;
                    }
                });
            } else {
                LinuxDisplay linuxDisplay = this;
                linuxDisplay.switchDisplayMode(linuxDisplay.saved_mode);
            }
            if (LinuxDisplay.isXF86VidModeSupported()) {
                LinuxDisplay linuxDisplay = this;
                linuxDisplay.doSetGamma(linuxDisplay.saved_gamma);
            }
            Compiz.setLegacyFullscreenSupport(false);
            return;
        }
        catch (LWJGLException lWJGLException) {
            LWJGLUtil.log("Caught exception while resetting mode: " + lWJGLException);
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    /*
     * Loose catch block
     */
    public final int getGammaRampLength() {
        if (!LinuxDisplay.isXF86VidModeSupported()) {
            return 0;
        }
        LinuxDisplay.lockAWT();
        try {
            int n;
            LinuxDisplay.incDisplay();
            try {
                n = LinuxDisplay.nGetGammaRampLength(LinuxDisplay.getDisplay(), LinuxDisplay.getDefaultScreen());
            }
            catch (LWJGLException lWJGLException) {
                LWJGLUtil.log("Got exception while querying gamma length: " + lWJGLException);
                LinuxDisplay.decDisplay();
                LinuxDisplay.unlockAWT();
                return 0;
                {
                    catch (Throwable throwable) {
                        try {
                            LinuxDisplay.decDisplay();
                            throw throwable;
                        }
                        catch (LWJGLException lWJGLException2) {
                            LWJGLUtil.log("Failed to get gamma ramp length: " + lWJGLException2);
                            return 0;
                        }
                    }
                }
            }
            LinuxDisplay.decDisplay();
            return n;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native int nGetGammaRampLength(long var0, int var2);

    public final void setGammaRamp(FloatBuffer floatBuffer) {
        if (!LinuxDisplay.isXF86VidModeSupported()) {
            throw new LWJGLException("No gamma ramp support (Missing XF86VM extension)");
        }
        this.doSetGamma(LinuxDisplay.convertToNativeRamp(floatBuffer));
    }

    private void doSetGamma(ByteBuffer byteBuffer) {
        LinuxDisplay.lockAWT();
        try {
            LinuxDisplay.setGammaRampOnTmpDisplay(byteBuffer);
            this.current_gamma = byteBuffer;
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static void setGammaRampOnTmpDisplay(ByteBuffer byteBuffer) {
        LinuxDisplay.incDisplay();
        try {
            LinuxDisplay.nSetGammaRamp(LinuxDisplay.getDisplay(), LinuxDisplay.getDefaultScreen(), byteBuffer);
            return;
        }
        finally {
            LinuxDisplay.decDisplay();
        }
    }

    private static native void nSetGammaRamp(long var0, int var2, ByteBuffer var3);

    private static ByteBuffer convertToNativeRamp(FloatBuffer floatBuffer) {
        FloatBuffer floatBuffer2 = floatBuffer;
        return LinuxDisplay.nConvertToNativeRamp(floatBuffer2, floatBuffer2.position(), floatBuffer.remaining());
    }

    private static native ByteBuffer nConvertToNativeRamp(FloatBuffer var0, int var1, int var2);

    public final String getAdapter() {
        return null;
    }

    public final String getVersion() {
        return null;
    }

    public final DisplayMode init() {
        LinuxDisplay.lockAWT();
        try {
            Compiz.init();
            this.delete_atom = LinuxDisplay.internAtom("WM_DELETE_WINDOW", false);
            this.current_displaymode_extension = LinuxDisplay.getBestDisplayModeExtension();
            if (this.current_displaymode_extension == 12) {
                throw new LWJGLException("No display mode extension is available");
            }
            Object object = this.getAvailableDisplayModes();
            if (object == null || ((DisplayMode[])object).length == 0) {
                throw new LWJGLException("No modes available");
            }
            switch (this.current_displaymode_extension) {
                case 10: {
                    this.saved_mode = AccessController.doPrivileged(new PrivilegedAction<DisplayMode>(){

                        @Override
                        public DisplayMode run() {
                            XRandR.saveConfiguration();
                            return XRandR.ScreentoDisplayMode(XRandR.getConfiguration());
                        }
                    });
                    break;
                }
                case 11: {
                    this.saved_mode = object[0];
                    break;
                }
                default: {
                    throw new LWJGLException("Unknown display mode extension: " + this.current_displaymode_extension);
                }
            }
            this.current_mode = this.saved_mode;
            this.current_gamma = this.saved_gamma = LinuxDisplay.getCurrentGammaRamp();
            object = this.saved_mode;
            return object;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static DisplayMode getCurrentXRandrMode() {
        LinuxDisplay.lockAWT();
        try {
            DisplayMode displayMode;
            LinuxDisplay.incDisplay();
            try {
                displayMode = LinuxDisplay.nGetCurrentXRandrMode(LinuxDisplay.getDisplay(), LinuxDisplay.getDefaultScreen());
            }
            catch (Throwable throwable) {
                LinuxDisplay.decDisplay();
                throw throwable;
            }
            LinuxDisplay.decDisplay();
            return displayMode;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native DisplayMode nGetCurrentXRandrMode(long var0, int var2);

    public final void setTitle(String object) {
        LinuxDisplay.lockAWT();
        try {
            object = MemoryUtil.encodeUTF8((CharSequence)object);
            LinuxDisplay.nSetTitle(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow(), MemoryUtil.getAddress((ByteBuffer)object), ((Buffer)object).remaining() - 1);
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native void nSetTitle(long var0, long var2, long var4, int var6);

    private void setClassHint(String object, String object2) {
        LinuxDisplay.lockAWT();
        try {
            object = MemoryUtil.encodeUTF8((CharSequence)object);
            object2 = MemoryUtil.encodeUTF8((CharSequence)object2);
            LinuxDisplay.nSetClassHint(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow(), MemoryUtil.getAddress((ByteBuffer)object), MemoryUtil.getAddress((ByteBuffer)object2));
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native void nSetClassHint(long var0, long var2, long var4, long var6);

    public final boolean isCloseRequested() {
        boolean bl = this.close_requested;
        this.close_requested = false;
        return bl;
    }

    public final boolean isVisible() {
        return !this.minimized;
    }

    public final boolean isActive() {
        return this.focused || LinuxDisplay.isLegacyFullscreen();
    }

    public final boolean isDirty() {
        boolean bl = this.dirty;
        this.dirty = false;
        return bl;
    }

    public final PeerInfo createPeerInfo(org.lwjgl.opengl.PixelFormat pixelFormat, ContextAttribs contextAttribs) {
        this.peer_info = new LinuxDisplayPeerInfo(pixelFormat);
        return this.peer_info;
    }

    private void relayEventToParent(LinuxEvent linuxEvent, int n) {
        this.tmp_event_buffer.copyFrom(linuxEvent);
        this.tmp_event_buffer.setWindow(this.parent_window);
        this.tmp_event_buffer.sendEvent(LinuxDisplay.getDisplay(), this.parent_window, true, n);
    }

    private void relayEventToParent(LinuxEvent linuxEvent) {
        if (this.parent == null) {
            return;
        }
        switch (linuxEvent.getType()) {
            case 2: {
                this.relayEventToParent(linuxEvent, 1);
                return;
            }
            case 3: {
                this.relayEventToParent(linuxEvent, 1);
                return;
            }
            case 4: {
                if (!xembedded && this.focused) break;
                this.relayEventToParent(linuxEvent, 1);
                return;
            }
            case 5: {
                if (!xembedded && this.focused) break;
                this.relayEventToParent(linuxEvent, 1);
            }
        }
    }

    private void processEvents() {
        while (LinuxEvent.getPending(LinuxDisplay.getDisplay()) > 0) {
            this.event_buffer.nextEvent(LinuxDisplay.getDisplay());
            long l = this.event_buffer.getWindow();
            LinuxDisplay linuxDisplay = this;
            linuxDisplay.relayEventToParent(linuxDisplay.event_buffer);
            if (l != LinuxDisplay.getWindow() || this.event_buffer.filterEvent(l) || this.mouse != null && this.mouse.filterEvent(this.grab, this.shouldWarpPointer(), this.event_buffer) || this.keyboard != null && this.keyboard.filterEvent(this.event_buffer)) continue;
            switch (this.event_buffer.getType()) {
                case 9: {
                    this.setFocused(true, this.event_buffer.getFocusDetail());
                    break;
                }
                case 10: {
                    this.setFocused(false, this.event_buffer.getFocusDetail());
                    break;
                }
                case 33: {
                    if (this.event_buffer.getClientFormat() != 32 || (long)this.event_buffer.getClientData(0) != this.delete_atom) break;
                    this.close_requested = true;
                    break;
                }
                case 19: {
                    this.dirty = true;
                    this.minimized = false;
                    break;
                }
                case 18: {
                    this.dirty = true;
                    this.minimized = true;
                    break;
                }
                case 12: {
                    this.dirty = true;
                    break;
                }
                case 22: {
                    int n = LinuxDisplay.nGetX(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow());
                    int n2 = LinuxDisplay.nGetY(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow());
                    int n3 = LinuxDisplay.nGetWidth(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow());
                    int n4 = LinuxDisplay.nGetHeight(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow());
                    this.window_x = n;
                    this.window_y = n2;
                    if (this.window_width == n3 && this.window_height == n4) break;
                    this.resized = true;
                    this.window_width = n3;
                    this.window_height = n4;
                    break;
                }
                case 7: {
                    this.mouseInside = true;
                    break;
                }
                case 8: {
                    this.mouseInside = false;
                }
            }
        }
    }

    public final void update() {
        LinuxDisplay.lockAWT();
        try {
            this.processEvents();
            this.checkInput();
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    public final void reshape(int n, int n2, int n3, int n4) {
        LinuxDisplay.lockAWT();
        try {
            LinuxDisplay.nReshape(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow(), n, n2, n3, n4);
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native void nReshape(long var0, long var2, int var4, int var5, int var6, int var7);

    public final DisplayMode[] getAvailableDisplayModes() {
        LinuxDisplay.lockAWT();
        try {
            DisplayMode[] displayModeArray;
            LinuxDisplay.incDisplay();
            if (this.current_displaymode_extension == 10) {
                Object[] objectArray = LinuxDisplay.nGetAvailableDisplayModes(LinuxDisplay.getDisplay(), LinuxDisplay.getDefaultScreen(), this.current_displaymode_extension);
                int n = 24;
                if (objectArray.length > 0) {
                    n = objectArray[0].getBitsPerPixel();
                }
                objectArray = XRandR.getResolutions(XRandR.getScreenNames()[0]);
                DisplayMode[] displayModeArray2 = new DisplayMode[objectArray.length];
                for (int i = 0; i < displayModeArray2.length; ++i) {
                    displayModeArray2[i] = new DisplayMode(((XRandR.Screen)objectArray[i]).width, ((XRandR.Screen)objectArray[i]).height, n, ((XRandR.Screen)objectArray[i]).freq);
                }
                DisplayMode[] displayModeArray3 = displayModeArray2;
                return displayModeArray3;
            }
            try {
                DisplayMode[] displayModeArray4;
                displayModeArray = displayModeArray4 = LinuxDisplay.nGetAvailableDisplayModes(LinuxDisplay.getDisplay(), LinuxDisplay.getDefaultScreen(), this.current_displaymode_extension);
            }
            catch (Throwable throwable) {
                LinuxDisplay.decDisplay();
                throw throwable;
            }
            LinuxDisplay.decDisplay();
            return displayModeArray;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native DisplayMode[] nGetAvailableDisplayModes(long var0, int var2, int var3);

    public final boolean hasWheel() {
        return true;
    }

    public final int getButtonCount() {
        return this.mouse.getButtonCount();
    }

    public final void createMouse() {
        LinuxDisplay.lockAWT();
        try {
            this.mouse = new LinuxMouse(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow(), LinuxDisplay.getWindow());
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    public final void destroyMouse() {
        this.mouse = null;
        this.updateInputGrab();
    }

    public final void pollMouse(IntBuffer intBuffer, ByteBuffer byteBuffer) {
        LinuxDisplay.lockAWT();
        try {
            this.mouse.poll(this.grab, intBuffer, byteBuffer);
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    public final void readMouse(ByteBuffer byteBuffer) {
        LinuxDisplay.lockAWT();
        try {
            this.mouse.read(byteBuffer);
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    public final void setCursorPosition(int n, int n2) {
        LinuxDisplay.lockAWT();
        try {
            this.mouse.setCursorPosition(n, n2);
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    /*
     * Enabled aggressive block sorting
     */
    private void checkInput() {
        if (this.parent == null) {
            return;
        }
        if (!xembedded) {
            if (!this.parent_focus_changed) return;
            if (!this.parent_focused) return;
            this.setInputFocusUnsafe(LinuxDisplay.getWindow());
            this.parent_focus_changed = false;
            return;
        }
        if (this.last_window_focus == 0L) {
            if (this.parent_focused == this.focused) return;
        }
        if (!this.isParentWindowActive(0L)) {
            this.last_window_focus = 0L;
            this.focused = false;
            return;
        }
        if (this.parent_focused) {
            LinuxDisplay.nSetInputFocus(LinuxDisplay.getDisplay(), current_window, 0L);
            this.last_window_focus = current_window;
            this.focused = true;
            return;
        }
        LinuxDisplay.nSetInputFocus(LinuxDisplay.getDisplay(), this.parent_proxy_focus_window, 0L);
        this.last_window_focus = this.parent_proxy_focus_window;
        this.focused = false;
    }

    private void setInputFocusUnsafe(long l) {
        try {
            LinuxDisplay.nSetInputFocus(LinuxDisplay.getDisplay(), l, 0L);
            LinuxDisplay.nSync(LinuxDisplay.getDisplay(), false);
            return;
        }
        catch (LWJGLException lWJGLException) {
            LWJGLUtil.log("Got exception while trying to focus: " + lWJGLException);
            return;
        }
    }

    private static native void nSync(long var0, boolean var2);

    private boolean isParentWindowActive(long l) {
        try {
            if (l == current_window) {
                return true;
            }
            if (LinuxDisplay.getChildCount(LinuxDisplay.getDisplay(), l) != 0) {
                return false;
            }
            long l2 = LinuxDisplay.getParentWindow(LinuxDisplay.getDisplay(), l);
            if (l2 == 0L) {
                return false;
            }
            long l3 = current_window;
            while (l3 != 0L) {
                l3 = LinuxDisplay.getParentWindow(LinuxDisplay.getDisplay(), l3);
                if (l3 != l2) continue;
                this.parent_proxy_focus_window = l;
                return true;
            }
        }
        catch (LWJGLException lWJGLException) {
            LWJGLUtil.log("Failed to detect if parent window is active: " + lWJGLException.getMessage());
            return true;
        }
        return false;
    }

    private void setFocused(boolean bl, int n) {
        if (this.focused == bl || n == 7 || n == 5 || n == 6 || xembedded) {
            return;
        }
        this.focused = bl;
        if (this.focused) {
            this.acquireInput();
            return;
        }
        this.releaseInput();
    }

    private void releaseInput() {
        if (LinuxDisplay.isLegacyFullscreen() || this.input_released) {
            return;
        }
        if (this.keyboard != null) {
            this.keyboard.releaseAll();
        }
        this.input_released = true;
        this.updateInputGrab();
        if (current_window_mode == 2) {
            LinuxDisplay.nIconifyWindow(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow(), LinuxDisplay.getDefaultScreen());
            try {
                if (this.current_displaymode_extension == 10) {
                    AccessController.doPrivileged(new PrivilegedAction<Object>(){

                        @Override
                        public Object run() {
                            XRandR.restoreConfiguration();
                            return null;
                        }
                    });
                } else {
                    LinuxDisplay linuxDisplay = this;
                    linuxDisplay.switchDisplayModeOnTmpDisplay(linuxDisplay.saved_mode);
                }
                LinuxDisplay.setGammaRampOnTmpDisplay(this.saved_gamma);
                return;
            }
            catch (LWJGLException lWJGLException) {
                LWJGLUtil.log("Failed to restore saved mode: " + lWJGLException.getMessage());
            }
        }
    }

    private static native void nIconifyWindow(long var0, long var2, int var4);

    private void acquireInput() {
        if (LinuxDisplay.isLegacyFullscreen() || !this.input_released) {
            return;
        }
        this.input_released = false;
        this.updateInputGrab();
        if (current_window_mode == 2) {
            try {
                LinuxDisplay linuxDisplay = this;
                linuxDisplay.switchDisplayModeOnTmpDisplay(linuxDisplay.current_mode);
                LinuxDisplay.setGammaRampOnTmpDisplay(this.current_gamma);
                return;
            }
            catch (LWJGLException lWJGLException) {
                LWJGLUtil.log("Failed to restore mode: " + lWJGLException.getMessage());
            }
        }
    }

    public final void grabMouse(boolean bl) {
        LinuxDisplay.lockAWT();
        try {
            if (bl != this.grab) {
                this.grab = bl;
                this.updateInputGrab();
                this.mouse.changeGrabbed(this.grab, this.shouldWarpPointer());
            }
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private boolean shouldWarpPointer() {
        return this.pointer_grabbed && this.shouldGrab();
    }

    public final int getNativeCursorCapabilities() {
        LinuxDisplay.lockAWT();
        try {
            int n;
            LinuxDisplay.incDisplay();
            try {
                n = LinuxDisplay.nGetNativeCursorCapabilities(LinuxDisplay.getDisplay());
            }
            catch (Throwable throwable) {
                try {
                    LinuxDisplay.decDisplay();
                    throw throwable;
                }
                catch (LWJGLException lWJGLException) {
                    throw new RuntimeException(lWJGLException);
                }
            }
            LinuxDisplay.decDisplay();
            return n;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native int nGetNativeCursorCapabilities(long var0);

    public final void setNativeCursor(Object object) {
        this.current_cursor = LinuxDisplay.getCursorHandle(object);
        LinuxDisplay.lockAWT();
        try {
            this.updateCursor();
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    public final int getMinCursorSize() {
        LinuxDisplay.lockAWT();
        try {
            int n;
            LinuxDisplay.incDisplay();
            try {
                n = LinuxDisplay.nGetMinCursorSize(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow());
            }
            catch (Throwable throwable) {
                try {
                    LinuxDisplay.decDisplay();
                    throw throwable;
                }
                catch (LWJGLException lWJGLException) {
                    LWJGLUtil.log("Exception occurred in getMinCursorSize: " + lWJGLException);
                    return 0;
                }
            }
            LinuxDisplay.decDisplay();
            return n;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native int nGetMinCursorSize(long var0, long var2);

    public final int getMaxCursorSize() {
        LinuxDisplay.lockAWT();
        try {
            int n;
            LinuxDisplay.incDisplay();
            try {
                n = LinuxDisplay.nGetMaxCursorSize(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow());
            }
            catch (Throwable throwable) {
                try {
                    LinuxDisplay.decDisplay();
                    throw throwable;
                }
                catch (LWJGLException lWJGLException) {
                    LWJGLUtil.log("Exception occurred in getMaxCursorSize: " + lWJGLException);
                    return 0;
                }
            }
            LinuxDisplay.decDisplay();
            return n;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native int nGetMaxCursorSize(long var0, long var2);

    public final void createKeyboard() {
        LinuxDisplay.lockAWT();
        try {
            this.keyboard = new LinuxKeyboard(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow());
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    public final void destroyKeyboard() {
        LinuxDisplay.lockAWT();
        try {
            this.keyboard.destroy(LinuxDisplay.getDisplay());
            this.keyboard = null;
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    public final void pollKeyboard(ByteBuffer byteBuffer) {
        LinuxDisplay.lockAWT();
        try {
            this.keyboard.poll(byteBuffer);
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    public final void readKeyboard(ByteBuffer byteBuffer) {
        LinuxDisplay.lockAWT();
        try {
            this.keyboard.read(byteBuffer);
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native long nCreateCursor(long var0, int var2, int var3, int var4, int var5, int var6, IntBuffer var7, int var8, IntBuffer var9, int var10);

    private static long createBlankCursor() {
        return LinuxDisplay.nCreateBlankCursor(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow());
    }

    static native long nCreateBlankCursor(long var0, long var2);

    public final Object createCursor(int n, int n2, int n3, int n4, int n5, IntBuffer intBuffer, IntBuffer intBuffer2) {
        LinuxDisplay.lockAWT();
        try {
            LinuxDisplay.incDisplay();
            try {
                IntBuffer intBuffer3 = intBuffer;
                IntBuffer intBuffer4 = intBuffer2;
                long l = LinuxDisplay.nCreateCursor(LinuxDisplay.getDisplay(), n, n2, n3, n4, n5, intBuffer3, intBuffer3.position(), intBuffer4, intBuffer4 != null ? intBuffer2.position() : -1);
                Long l2 = l;
                return l2;
            }
            catch (LWJGLException lWJGLException) {
                LinuxDisplay.decDisplay();
                throw lWJGLException;
            }
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static long getCursorHandle(Object object) {
        if (object != null) {
            return (Long)object;
        }
        return 0L;
    }

    public final void destroyCursor(Object object) {
        LinuxDisplay.lockAWT();
        try {
            LinuxDisplay.nDestroyCursor(LinuxDisplay.getDisplay(), LinuxDisplay.getCursorHandle(object));
            LinuxDisplay.decDisplay();
            return;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    static native void nDestroyCursor(long var0, long var2);

    public final int getPbufferCapabilities() {
        LinuxDisplay.lockAWT();
        try {
            int n;
            LinuxDisplay.incDisplay();
            try {
                n = LinuxDisplay.nGetPbufferCapabilities(LinuxDisplay.getDisplay(), LinuxDisplay.getDefaultScreen());
            }
            catch (Throwable throwable) {
                try {
                    LinuxDisplay.decDisplay();
                    throw throwable;
                }
                catch (LWJGLException lWJGLException) {
                    LWJGLUtil.log("Exception occurred in getPbufferCapabilities: " + lWJGLException);
                    return 0;
                }
            }
            LinuxDisplay.decDisplay();
            return n;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native int nGetPbufferCapabilities(long var0, int var2);

    public final boolean isBufferLost(PeerInfo peerInfo) {
        return false;
    }

    public final PeerInfo createPbuffer(int n, int n2, org.lwjgl.opengl.PixelFormat pixelFormat, ContextAttribs contextAttribs, IntBuffer intBuffer, IntBuffer intBuffer2) {
        return new LinuxPbufferPeerInfo(n, n2, pixelFormat);
    }

    public final void setPbufferAttrib(PeerInfo peerInfo, int n, int n2) {
        throw new UnsupportedOperationException();
    }

    public final void bindTexImageToPbuffer(PeerInfo peerInfo, int n) {
        throw new UnsupportedOperationException();
    }

    public final void releaseTexImageFromPbuffer(PeerInfo peerInfo, int n) {
        throw new UnsupportedOperationException();
    }

    private static ByteBuffer convertIcons(ByteBuffer[] byteBufferArray) {
        int n;
        int n2;
        int n3 = 0;
        Object object = byteBufferArray;
        int n4 = byteBufferArray.length;
        for (n2 = 0; n2 < n4; ++n2) {
            ByteBuffer byteBuffer = object[n2];
            int n5 = byteBuffer.limit() / 4;
            n = (int)Math.sqrt(n5);
            if (n <= 0) continue;
            n3 += 8;
            int n6 = n;
            n3 += n6 * n6 << 2;
        }
        if (n3 == 0) {
            return null;
        }
        object = BufferUtils.createByteBuffer(n3);
        ((ByteBuffer)object).order(ByteOrder.BIG_ENDIAN);
        ByteBuffer[] byteBufferArray2 = byteBufferArray;
        n2 = byteBufferArray.length;
        for (int i = 0; i < n2; ++i) {
            ByteBuffer byteBuffer = byteBufferArray2[i];
            n = byteBuffer.limit() / 4;
            int n7 = (int)Math.sqrt(n);
            ((ByteBuffer)object).putInt(n7);
            ((ByteBuffer)object).putInt(n7);
            for (n3 = 0; n3 < n7; ++n3) {
                for (n = 0; n < n7; ++n) {
                    byte by = byteBuffer.get((n << 2) + (n3 * n7 << 2));
                    byte by2 = byteBuffer.get((n << 2) + (n3 * n7 << 2) + 1);
                    byte by3 = byteBuffer.get((n << 2) + (n3 * n7 << 2) + 2);
                    byte by4 = byteBuffer.get((n << 2) + (n3 * n7 << 2) + 3);
                    ((ByteBuffer)object).put(by4);
                    ((ByteBuffer)object).put(by);
                    ((ByteBuffer)object).put(by2);
                    ((ByteBuffer)object).put(by3);
                }
            }
        }
        return object;
    }

    public final int setIcon(ByteBuffer[] byteBufferArray) {
        LinuxDisplay.lockAWT();
        try {
            ByteBuffer byteBuffer;
            block10: {
                LinuxDisplay.incDisplay();
                try {
                    byteBuffer = LinuxDisplay.convertIcons(byteBufferArray);
                    if (byteBuffer != null) break block10;
                }
                catch (Throwable throwable) {
                    try {
                        LinuxDisplay.decDisplay();
                        throw throwable;
                    }
                    catch (LWJGLException lWJGLException) {
                        LWJGLUtil.log("Failed to set display icon: " + lWJGLException);
                        return 0;
                    }
                }
                LinuxDisplay.decDisplay();
                return 0;
            }
            ByteBuffer byteBuffer2 = byteBuffer;
            LinuxDisplay.nSetWindowIcon(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow(), byteBuffer2, byteBuffer2.capacity());
            int n = byteBufferArray.length;
            LinuxDisplay.decDisplay();
            return n;
        }
        finally {
            LinuxDisplay.unlockAWT();
        }
    }

    private static native void nSetWindowIcon(long var0, long var2, ByteBuffer var4, int var5);

    public final int getX() {
        return this.window_x;
    }

    public final int getY() {
        return this.window_y;
    }

    public final int getWidth() {
        return this.window_width;
    }

    public final int getHeight() {
        return this.window_height;
    }

    public final boolean isInsideWindow() {
        return this.mouseInside;
    }

    public final void setResizable(boolean bl) {
        if (this.resizable == bl) {
            return;
        }
        this.resizable = bl;
        LinuxDisplay.nSetWindowSize(LinuxDisplay.getDisplay(), LinuxDisplay.getWindow(), this.window_width, this.window_height, bl);
    }

    public final boolean wasResized() {
        if (this.resized) {
            this.resized = false;
            return true;
        }
        return false;
    }

    public final float getPixelScaleFactor() {
        return 1.0f;
    }

    /*
     * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
     */
    private static final class Compiz {
        private static boolean applyFix;
        private static Provider provider;

        private Compiz() {
        }

        static void init() {
            if (Display.getPrivilegedBoolean("org.lwjgl.opengl.Window.nocompiz_lfs")) {
                return;
            }
            AccessController.doPrivileged(new PrivilegedAction<Object>(){

                @Override
                public final Object run() {
                    block9: {
                        if (Compiz.isProcessActive("compiz")) break block9;
                        return null;
                    }
                    try {
                        Compiz.provider = null;
                        String string = null;
                        if (Compiz.isProcessActive("dbus-daemon")) {
                            string = "Dbus";
                            Compiz.provider = new Provider(){
                                private static final String KEY = "/org/freedesktop/compiz/workarounds/allscreens/legacy_fullscreen";

                                public boolean hasLegacyFullscreenSupport() {
                                    List list = Compiz.run(new String[]{"dbus-send", "--print-reply", "--type=method_call", "--dest=org.freedesktop.compiz", KEY, "org.freedesktop.compiz.get"});
                                    if (list == null || list.size() < 2) {
                                        throw new LWJGLException("Invalid Dbus reply.");
                                    }
                                    String string = (String)list.get(0);
                                    if (!string.startsWith("method return")) {
                                        throw new LWJGLException("Invalid Dbus reply.");
                                    }
                                    string = ((String)list.get(1)).trim();
                                    if (!string.startsWith("boolean") || string.length() < 12) {
                                        throw new LWJGLException("Invalid Dbus reply.");
                                    }
                                    return "true".equalsIgnoreCase(string.substring(7 + 1));
                                }

                                public void setLegacyFullscreenSupport(boolean bl) {
                                    if (Compiz.run(new String[]{"dbus-send", "--type=method_call", "--dest=org.freedesktop.compiz", KEY, "org.freedesktop.compiz.set", "boolean:" + Boolean.toString(bl)}) == null) {
                                        throw new LWJGLException("Failed to apply Compiz LFS workaround.");
                                    }
                                }
                            };
                        } else {
                            try {
                                Runtime.getRuntime().exec("gconftool");
                                string = "gconftool";
                                Compiz.provider = new Provider(){
                                    private static final String KEY = "/apps/compiz/plugins/workarounds/allscreens/options/legacy_fullscreen";

                                    public boolean hasLegacyFullscreenSupport() {
                                        List list = Compiz.run(new String[]{"gconftool", "-g", KEY});
                                        if (list == null || list.size() == 0) {
                                            throw new LWJGLException("Invalid gconftool reply.");
                                        }
                                        return Boolean.parseBoolean(((String)list.get(0)).trim());
                                    }

                                    public void setLegacyFullscreenSupport(boolean bl) {
                                        if (Compiz.run(new String[]{"gconftool", "-s", KEY, "-s", Boolean.toString(bl), "-t", "bool"}) == null) {
                                            throw new LWJGLException("Failed to apply Compiz LFS workaround.");
                                        }
                                        if (bl) {
                                            try {
                                                Thread.sleep(200L);
                                                return;
                                            }
                                            catch (InterruptedException interruptedException) {
                                                InterruptedException interruptedException2 = interruptedException;
                                                interruptedException.printStackTrace();
                                            }
                                        }
                                    }
                                };
                            }
                            catch (IOException iOException) {}
                        }
                        if (provider != null && !provider.hasLegacyFullscreenSupport()) {
                            applyFix = true;
                            LWJGLUtil.log("Using " + string + " to apply Compiz LFS workaround.");
                        }
                        return null;
                    }
                    catch (LWJGLException lWJGLException) {
                        return null;
                    }
                    catch (Throwable throwable) {
                        return null;
                    }
                }
            });
        }

        static void setLegacyFullscreenSupport(final boolean bl) {
            if (!applyFix) {
                return;
            }
            AccessController.doPrivileged(new PrivilegedAction<Object>(){

                @Override
                public final Object run() {
                    try {
                        provider.setLegacyFullscreenSupport(bl);
                    }
                    catch (LWJGLException lWJGLException) {
                        LWJGLUtil.log("Failed to change Compiz Legacy Fullscreen Support. Reason: " + lWJGLException.getMessage());
                    }
                    return null;
                }
            });
        }

        private static List<String> run(String ... object) {
            ArrayList<String> arrayList = new ArrayList<String>();
            try {
                object = Runtime.getRuntime().exec((String[])object);
                try {
                    int n = ((Process)object).waitFor();
                    if (n != 0) {
                        return null;
                    }
                }
                catch (InterruptedException interruptedException) {
                    throw new LWJGLException("Process interrupted.", interruptedException);
                }
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(((Process)object).getInputStream()));
                while ((object = bufferedReader.readLine()) != null) {
                    arrayList.add((String)object);
                }
                bufferedReader.close();
            }
            catch (IOException iOException) {
                throw new LWJGLException("Process failed.", iOException);
            }
            return arrayList;
        }

        private static boolean isProcessActive(String string) {
            List<String> list = Compiz.run("ps", "-C", string);
            if (list == null) {
                return false;
            }
            for (String string2 : list) {
                if (!string2.contains(string)) continue;
                return true;
            }
            return false;
        }

        private static interface Provider {
            public boolean hasLegacyFullscreenSupport();

            public void setLegacyFullscreenSupport(boolean var1);
        }
    }
}
