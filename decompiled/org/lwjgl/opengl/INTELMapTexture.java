/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class INTELMapTexture {
    public static final int GL_TEXTURE_MEMORY_LAYOUT_INTEL = 33791;
    public static final int GL_LAYOUT_DEFAULT_INTEL = 0;
    public static final int GL_LAYOUT_LINEAR_INTEL = 1;
    public static final int GL_LAYOUT_LINEAR_CPU_CACHED_INTEL = 2;

    private INTELMapTexture() {
    }

    public static ByteBuffer glMapTexture2DINTEL(int n, int n2, long l, int n3, IntBuffer intBuffer, IntBuffer intBuffer2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMapTexture2DINTEL;
        BufferChecks.checkFunctionAddress(l2);
        BufferChecks.checkBuffer(intBuffer, 1);
        BufferChecks.checkBuffer(intBuffer2, 1);
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        ByteBuffer byteBuffer2 = INTELMapTexture.nglMapTexture2DINTEL(n, n2, l, n3, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer2), byteBuffer, l2);
        if (LWJGLUtil.CHECKS && byteBuffer2 == null) {
            return null;
        }
        return byteBuffer2.order(ByteOrder.nativeOrder());
    }

    static native ByteBuffer nglMapTexture2DINTEL(int var0, int var1, long var2, int var4, long var5, long var7, ByteBuffer var9, long var10);

    public static void glUnmapTexture2DINTEL(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUnmapTexture2DINTEL;
        BufferChecks.checkFunctionAddress(l);
        INTELMapTexture.nglUnmapTexture2DINTEL(n, n2, l);
    }

    static native void nglUnmapTexture2DINTEL(int var0, int var1, long var2);

    public static void glSyncTextureINTEL(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glSyncTextureINTEL;
        BufferChecks.checkFunctionAddress(l);
        INTELMapTexture.nglSyncTextureINTEL(n, l);
    }

    static native void nglSyncTextureINTEL(int var0, long var1);
}
