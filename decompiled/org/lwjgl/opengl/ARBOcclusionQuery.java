/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ARBOcclusionQuery {
    public static final int GL_SAMPLES_PASSED_ARB = 35092;
    public static final int GL_QUERY_COUNTER_BITS_ARB = 34916;
    public static final int GL_CURRENT_QUERY_ARB = 34917;
    public static final int GL_QUERY_RESULT_ARB = 34918;
    public static final int GL_QUERY_RESULT_AVAILABLE_ARB = 34919;

    private ARBOcclusionQuery() {
    }

    public static void glGenQueriesARB(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenQueriesARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBOcclusionQuery.nglGenQueriesARB(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGenQueriesARB(int var0, long var1, long var3);

    public static int glGenQueriesARB() {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGenQueriesARB;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        ARBOcclusionQuery.nglGenQueriesARB(1, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glDeleteQueriesARB(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteQueriesARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBOcclusionQuery.nglDeleteQueriesARB(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDeleteQueriesARB(int var0, long var1, long var3);

    public static void glDeleteQueriesARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteQueriesARB;
        BufferChecks.checkFunctionAddress(l);
        ARBOcclusionQuery.nglDeleteQueriesARB(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static boolean glIsQueryARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsQueryARB;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = ARBOcclusionQuery.nglIsQueryARB(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsQueryARB(int var0, long var1);

    public static void glBeginQueryARB(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBeginQueryARB;
        BufferChecks.checkFunctionAddress(l);
        ARBOcclusionQuery.nglBeginQueryARB(n, n2, l);
    }

    static native void nglBeginQueryARB(int var0, int var1, long var2);

    public static void glEndQueryARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEndQueryARB;
        BufferChecks.checkFunctionAddress(l);
        ARBOcclusionQuery.nglEndQueryARB(n, l);
    }

    static native void nglEndQueryARB(int var0, long var1);

    public static void glGetQueryARB(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetQueryivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        ARBOcclusionQuery.nglGetQueryivARB(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetQueryivARB(int var0, int var1, long var2, long var4);

    public static int glGetQueryARB(int n, int n2) {
        return ARBOcclusionQuery.glGetQueryiARB(n, n2);
    }

    public static int glGetQueryiARB(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetQueryivARB;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        ARBOcclusionQuery.nglGetQueryivARB(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetQueryObjectARB(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetQueryObjectivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        ARBOcclusionQuery.nglGetQueryObjectivARB(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetQueryObjectivARB(int var0, int var1, long var2, long var4);

    public static int glGetQueryObjectiARB(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetQueryObjectivARB;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        ARBOcclusionQuery.nglGetQueryObjectivARB(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetQueryObjectuARB(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetQueryObjectuivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        ARBOcclusionQuery.nglGetQueryObjectuivARB(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetQueryObjectuivARB(int var0, int var1, long var2, long var4);

    public static int glGetQueryObjectuiARB(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetQueryObjectuivARB;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        ARBOcclusionQuery.nglGetQueryObjectuivARB(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }
}
