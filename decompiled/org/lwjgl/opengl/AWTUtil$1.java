/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Component;
import java.awt.Robot;
import java.security.PrivilegedExceptionAction;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
static final class AWTUtil.1
implements PrivilegedExceptionAction<Robot> {
    final /* synthetic */ Component val$component;

    AWTUtil.1(Component component) {
        this.val$component = component;
    }

    @Override
    public final Robot run() {
        return new Robot(this.val$component.getGraphicsConfiguration().getDevice());
    }
}
