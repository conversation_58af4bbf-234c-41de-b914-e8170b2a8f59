/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ATIVertexAttribArrayObject {
    private ATIVertexAttribArrayObject() {
    }

    public static void glVertexAttribArrayObjectATI(int n, int n2, int n3, boolean bl, int n4, int n5, int n6) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribArrayObjectATI;
        BufferChecks.checkFunctionAddress(l);
        ATIVertexAttribArrayObject.nglVertexAttribArrayObjectATI(n, n2, n3, bl, n4, n5, n6, l);
    }

    static native void nglVertexAttribArrayObjectATI(int var0, int var1, int var2, boolean var3, int var4, int var5, int var6, long var7);

    public static void glGetVertexAttribArrayObjectATI(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribArrayObjectfvATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        ATIVertexAttribArrayObject.nglGetVertexAttribArrayObjectfvATI(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetVertexAttribArrayObjectfvATI(int var0, int var1, long var2, long var4);

    public static void glGetVertexAttribArrayObjectATI(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribArrayObjectivATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        ATIVertexAttribArrayObject.nglGetVertexAttribArrayObjectivATI(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetVertexAttribArrayObjectivATI(int var0, int var1, long var2, long var4);
}
