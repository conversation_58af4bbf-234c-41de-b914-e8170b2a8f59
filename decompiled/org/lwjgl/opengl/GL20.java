/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.PointerBuffer;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.StateTracker;

public final class GL20 {
    public static final int GL_SHADING_LANGUAGE_VERSION = 35724;
    public static final int GL_CURRENT_PROGRAM = 35725;
    public static final int GL_SHADER_TYPE = 35663;
    public static final int GL_DELETE_STATUS = 35712;
    public static final int GL_COMPILE_STATUS = 35713;
    public static final int GL_LINK_STATUS = 35714;
    public static final int GL_VALIDATE_STATUS = 35715;
    public static final int GL_INFO_LOG_LENGTH = 35716;
    public static final int GL_ATTACHED_SHADERS = 35717;
    public static final int GL_ACTIVE_UNIFORMS = 35718;
    public static final int GL_ACTIVE_UNIFORM_MAX_LENGTH = 35719;
    public static final int GL_ACTIVE_ATTRIBUTES = 35721;
    public static final int GL_ACTIVE_ATTRIBUTE_MAX_LENGTH = 35722;
    public static final int GL_SHADER_SOURCE_LENGTH = 35720;
    public static final int GL_SHADER_OBJECT = 35656;
    public static final int GL_FLOAT_VEC2 = 35664;
    public static final int GL_FLOAT_VEC3 = 35665;
    public static final int GL_FLOAT_VEC4 = 35666;
    public static final int GL_INT_VEC2 = 35667;
    public static final int GL_INT_VEC3 = 35668;
    public static final int GL_INT_VEC4 = 35669;
    public static final int GL_BOOL = 35670;
    public static final int GL_BOOL_VEC2 = 35671;
    public static final int GL_BOOL_VEC3 = 35672;
    public static final int GL_BOOL_VEC4 = 35673;
    public static final int GL_FLOAT_MAT2 = 35674;
    public static final int GL_FLOAT_MAT3 = 35675;
    public static final int GL_FLOAT_MAT4 = 35676;
    public static final int GL_SAMPLER_1D = 35677;
    public static final int GL_SAMPLER_2D = 35678;
    public static final int GL_SAMPLER_3D = 35679;
    public static final int GL_SAMPLER_CUBE = 35680;
    public static final int GL_SAMPLER_1D_SHADOW = 35681;
    public static final int GL_SAMPLER_2D_SHADOW = 35682;
    public static final int GL_VERTEX_SHADER = 35633;
    public static final int GL_MAX_VERTEX_UNIFORM_COMPONENTS = 35658;
    public static final int GL_MAX_VARYING_FLOATS = 35659;
    public static final int GL_MAX_VERTEX_ATTRIBS = 34921;
    public static final int GL_MAX_TEXTURE_IMAGE_UNITS = 34930;
    public static final int GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS = 35660;
    public static final int GL_MAX_COMBINED_TEXTURE_IMAGE_UNITS = 35661;
    public static final int GL_MAX_TEXTURE_COORDS = 34929;
    public static final int GL_VERTEX_PROGRAM_POINT_SIZE = 34370;
    public static final int GL_VERTEX_PROGRAM_TWO_SIDE = 34371;
    public static final int GL_VERTEX_ATTRIB_ARRAY_ENABLED = 34338;
    public static final int GL_VERTEX_ATTRIB_ARRAY_SIZE = 34339;
    public static final int GL_VERTEX_ATTRIB_ARRAY_STRIDE = 34340;
    public static final int GL_VERTEX_ATTRIB_ARRAY_TYPE = 34341;
    public static final int GL_VERTEX_ATTRIB_ARRAY_NORMALIZED = 34922;
    public static final int GL_CURRENT_VERTEX_ATTRIB = 34342;
    public static final int GL_VERTEX_ATTRIB_ARRAY_POINTER = 34373;
    public static final int GL_FRAGMENT_SHADER = 35632;
    public static final int GL_MAX_FRAGMENT_UNIFORM_COMPONENTS = 35657;
    public static final int GL_FRAGMENT_SHADER_DERIVATIVE_HINT = 35723;
    public static final int GL_MAX_DRAW_BUFFERS = 34852;
    public static final int GL_DRAW_BUFFER0 = 34853;
    public static final int GL_DRAW_BUFFER1 = 34854;
    public static final int GL_DRAW_BUFFER2 = 34855;
    public static final int GL_DRAW_BUFFER3 = 34856;
    public static final int GL_DRAW_BUFFER4 = 34857;
    public static final int GL_DRAW_BUFFER5 = 34858;
    public static final int GL_DRAW_BUFFER6 = 34859;
    public static final int GL_DRAW_BUFFER7 = 34860;
    public static final int GL_DRAW_BUFFER8 = 34861;
    public static final int GL_DRAW_BUFFER9 = 34862;
    public static final int GL_DRAW_BUFFER10 = 34863;
    public static final int GL_DRAW_BUFFER11 = 34864;
    public static final int GL_DRAW_BUFFER12 = 34865;
    public static final int GL_DRAW_BUFFER13 = 34866;
    public static final int GL_DRAW_BUFFER14 = 34867;
    public static final int GL_DRAW_BUFFER15 = 34868;
    public static final int GL_POINT_SPRITE = 34913;
    public static final int GL_COORD_REPLACE = 34914;
    public static final int GL_POINT_SPRITE_COORD_ORIGIN = 36000;
    public static final int GL_LOWER_LEFT = 36001;
    public static final int GL_UPPER_LEFT = 36002;
    public static final int GL_STENCIL_BACK_FUNC = 34816;
    public static final int GL_STENCIL_BACK_FAIL = 34817;
    public static final int GL_STENCIL_BACK_PASS_DEPTH_FAIL = 34818;
    public static final int GL_STENCIL_BACK_PASS_DEPTH_PASS = 34819;
    public static final int GL_STENCIL_BACK_REF = 36003;
    public static final int GL_STENCIL_BACK_VALUE_MASK = 36004;
    public static final int GL_STENCIL_BACK_WRITEMASK = 36005;
    public static final int GL_BLEND_EQUATION_RGB = 32777;
    public static final int GL_BLEND_EQUATION_ALPHA = 34877;

    private GL20() {
    }

    public static void glShaderSource(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glShaderSource;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        GL20.nglShaderSource(n, 1, MemoryUtil.getAddress(byteBuffer), byteBuffer.remaining(), l);
    }

    static native void nglShaderSource(int var0, int var1, long var2, int var4, long var5);

    public static void glShaderSource(int n, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glShaderSource;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglShaderSource(n, 1, APIUtil.getBuffer(contextCapabilities, charSequence), charSequence.length(), l);
    }

    public static void glShaderSource(int n, CharSequence[] charSequenceArray) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glShaderSource;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkArray(charSequenceArray);
        GL20.nglShaderSource3(n, charSequenceArray.length, APIUtil.getBuffer(contextCapabilities, charSequenceArray), APIUtil.getLengths(contextCapabilities, charSequenceArray), l);
    }

    static native void nglShaderSource3(int var0, int var1, long var2, long var4, long var6);

    public static int glCreateShader(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateShader;
        BufferChecks.checkFunctionAddress(l);
        n = GL20.nglCreateShader(n, l);
        return n;
    }

    static native int nglCreateShader(int var0, long var1);

    public static boolean glIsShader(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsShader;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = GL20.nglIsShader(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsShader(int var0, long var1);

    public static void glCompileShader(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompileShader;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglCompileShader(n, l);
    }

    static native void nglCompileShader(int var0, long var1);

    public static void glDeleteShader(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteShader;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglDeleteShader(n, l);
    }

    static native void nglDeleteShader(int var0, long var1);

    public static int glCreateProgram() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCreateProgram;
        BufferChecks.checkFunctionAddress(l);
        int n = GL20.nglCreateProgram(l);
        return n;
    }

    static native int nglCreateProgram(long var0);

    public static boolean glIsProgram(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsProgram;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = GL20.nglIsProgram(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsProgram(int var0, long var1);

    public static void glAttachShader(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glAttachShader;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglAttachShader(n, n2, l);
    }

    static native void nglAttachShader(int var0, int var1, long var2);

    public static void glDetachShader(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDetachShader;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglDetachShader(n, n2, l);
    }

    static native void nglDetachShader(int var0, int var1, long var2);

    public static void glLinkProgram(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLinkProgram;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglLinkProgram(n, l);
    }

    static native void nglLinkProgram(int var0, long var1);

    public static void glUseProgram(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUseProgram;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglUseProgram(n, l);
    }

    static native void nglUseProgram(int var0, long var1);

    public static void glValidateProgram(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glValidateProgram;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglValidateProgram(n, l);
    }

    static native void nglValidateProgram(int var0, long var1);

    public static void glDeleteProgram(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeleteProgram;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglDeleteProgram(n, l);
    }

    static native void nglDeleteProgram(int var0, long var1);

    public static void glUniform1f(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform1f;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglUniform1f(n, f, l);
    }

    static native void nglUniform1f(int var0, float var1, long var2);

    public static void glUniform2f(int n, float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform2f;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglUniform2f(n, f, f2, l);
    }

    static native void nglUniform2f(int var0, float var1, float var2, long var3);

    public static void glUniform3f(int n, float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform3f;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglUniform3f(n, f, f2, f3, l);
    }

    static native void nglUniform3f(int var0, float var1, float var2, float var3, long var4);

    public static void glUniform4f(int n, float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform4f;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglUniform4f(n, f, f2, f3, f4, l);
    }

    static native void nglUniform4f(int var0, float var1, float var2, float var3, float var4, long var5);

    public static void glUniform1i(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform1i;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglUniform1i(n, n2, l);
    }

    static native void nglUniform1i(int var0, int var1, long var2);

    public static void glUniform2i(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform2i;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglUniform2i(n, n2, n3, l);
    }

    static native void nglUniform2i(int var0, int var1, int var2, long var3);

    public static void glUniform3i(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform3i;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglUniform3i(n, n2, n3, n4, l);
    }

    static native void nglUniform3i(int var0, int var1, int var2, int var3, long var4);

    public static void glUniform4i(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform4i;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglUniform4i(n, n2, n3, n4, n5, l);
    }

    static native void nglUniform4i(int var0, int var1, int var2, int var3, int var4, long var5);

    public static void glUniform1(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform1fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL20.nglUniform1fv(n, floatBuffer.remaining(), MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglUniform1fv(int var0, int var1, long var2, long var4);

    public static void glUniform2(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform2fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL20.nglUniform2fv(n, floatBuffer.remaining() >> 1, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglUniform2fv(int var0, int var1, long var2, long var4);

    public static void glUniform3(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform3fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL20.nglUniform3fv(n, floatBuffer.remaining() / 3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglUniform3fv(int var0, int var1, long var2, long var4);

    public static void glUniform4(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform4fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL20.nglUniform4fv(n, floatBuffer.remaining() >> 2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglUniform4fv(int var0, int var1, long var2, long var4);

    public static void glUniform1(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform1iv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL20.nglUniform1iv(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglUniform1iv(int var0, int var1, long var2, long var4);

    public static void glUniform2(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform2iv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL20.nglUniform2iv(n, intBuffer.remaining() >> 1, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglUniform2iv(int var0, int var1, long var2, long var4);

    public static void glUniform3(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform3iv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL20.nglUniform3iv(n, intBuffer.remaining() / 3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglUniform3iv(int var0, int var1, long var2, long var4);

    public static void glUniform4(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniform4iv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL20.nglUniform4iv(n, intBuffer.remaining() >> 2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglUniform4iv(int var0, int var1, long var2, long var4);

    public static void glUniformMatrix2(int n, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix2fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL20.nglUniformMatrix2fv(n, floatBuffer.remaining() >> 2, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglUniformMatrix2fv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glUniformMatrix3(int n, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix3fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL20.nglUniformMatrix3fv(n, floatBuffer.remaining() / 9, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglUniformMatrix3fv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glUniformMatrix4(int n, boolean bl, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glUniformMatrix4fv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL20.nglUniformMatrix4fv(n, floatBuffer.remaining() >> 4, bl, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglUniformMatrix4fv(int var0, int var1, boolean var2, long var3, long var5);

    public static void glGetShader(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetShaderiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL20.nglGetShaderiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetShaderiv(int var0, int var1, long var2, long var4);

    public static int glGetShader(int n, int n2) {
        return GL20.glGetShaderi(n, n2);
    }

    public static int glGetShaderi(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetShaderiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL20.nglGetShaderiv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetProgram(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL20.nglGetProgramiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetProgramiv(int var0, int var1, long var2, long var4);

    public static int glGetProgram(int n, int n2) {
        return GL20.glGetProgrami(n, n2);
    }

    public static int glGetProgrami(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetProgramiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL20.nglGetProgramiv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetShaderInfoLog(int n, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetShaderInfoLog;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(byteBuffer);
        GL20.nglGetShaderInfoLog(n, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetShaderInfoLog(int var0, int var1, long var2, long var4, long var6);

    public static String glGetShaderInfoLog(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetShaderInfoLog;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n2);
        GL20.nglGetShaderInfoLog(n, n2, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static void glGetProgramInfoLog(int n, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramInfoLog;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(byteBuffer);
        GL20.nglGetProgramInfoLog(n, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetProgramInfoLog(int var0, int var1, long var2, long var4, long var6);

    public static String glGetProgramInfoLog(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramInfoLog;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n2);
        GL20.nglGetProgramInfoLog(n, n2, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static void glGetAttachedShaders(int n, IntBuffer intBuffer, IntBuffer intBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetAttachedShaders;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(intBuffer2);
        GL20.nglGetAttachedShaders(n, intBuffer2.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(intBuffer2), l);
    }

    static native void nglGetAttachedShaders(int var0, int var1, long var2, long var4, long var6);

    public static int glGetUniformLocation(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformLocation;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 1);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = GL20.nglGetUniformLocation(n, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglGetUniformLocation(int var0, long var1, long var3);

    public static int glGetUniformLocation(int n, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformLocation;
        BufferChecks.checkFunctionAddress(l);
        n = GL20.nglGetUniformLocation(n, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }

    public static void glGetActiveUniform(int n, int n2, IntBuffer intBuffer, IntBuffer intBuffer2, IntBuffer intBuffer3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniform;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkBuffer(intBuffer2, 1);
        BufferChecks.checkBuffer(intBuffer3, 1);
        BufferChecks.checkDirect(byteBuffer);
        GL20.nglGetActiveUniform(n, n2, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(intBuffer3), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetActiveUniform(int var0, int var1, int var2, long var3, long var5, long var7, long var9, long var11);

    public static String glGetActiveUniform(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniform;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 2);
        IntBuffer intBuffer2 = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        IntBuffer intBuffer3 = intBuffer;
        GL20.nglGetActiveUniform(n, n2, n3, MemoryUtil.getAddress0(intBuffer2), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer3, intBuffer3.position() + 1), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer2.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static String glGetActiveUniform(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniform;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        GL20.nglGetActiveUniform(n, n2, n3, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress0(APIUtil.getBufferInt(contextCapabilities)), MemoryUtil.getAddress(APIUtil.getBufferInt(contextCapabilities), 1), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static int glGetActiveUniformSize(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniform;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getBufferInt(contextCapabilities);
        GL20.nglGetActiveUniform(n, n2, 1, 0L, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer, 1), APIUtil.getBufferByte0(contextCapabilities), l);
        return intBuffer.get(0);
    }

    public static int glGetActiveUniformType(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveUniform;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getBufferInt(contextCapabilities);
        GL20.nglGetActiveUniform(n, n2, 0, 0L, MemoryUtil.getAddress(intBuffer, 1), MemoryUtil.getAddress(intBuffer), APIUtil.getBufferByte0(contextCapabilities), l);
        return intBuffer.get(0);
    }

    public static void glGetUniform(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        GL20.nglGetUniformfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetUniformfv(int var0, int var1, long var2, long var4);

    public static void glGetUniform(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetUniformiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL20.nglGetUniformiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetUniformiv(int var0, int var1, long var2, long var4);

    public static void glGetShaderSource(int n, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetShaderSource;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(byteBuffer);
        GL20.nglGetShaderSource(n, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetShaderSource(int var0, int var1, long var2, long var4, long var6);

    public static String glGetShaderSource(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetShaderSource;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n2);
        GL20.nglGetShaderSource(n, n2, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static void glVertexAttrib1s(int n, short s) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib1s;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglVertexAttrib1s(n, s, l);
    }

    static native void nglVertexAttrib1s(int var0, short var1, long var2);

    public static void glVertexAttrib1f(int n, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib1f;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglVertexAttrib1f(n, f, l);
    }

    static native void nglVertexAttrib1f(int var0, float var1, long var2);

    public static void glVertexAttrib1d(int n, double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib1d;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglVertexAttrib1d(n, d, l);
    }

    static native void nglVertexAttrib1d(int var0, double var1, long var3);

    public static void glVertexAttrib2s(int n, short s, short s2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib2s;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglVertexAttrib2s(n, s, s2, l);
    }

    static native void nglVertexAttrib2s(int var0, short var1, short var2, long var3);

    public static void glVertexAttrib2f(int n, float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib2f;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglVertexAttrib2f(n, f, f2, l);
    }

    static native void nglVertexAttrib2f(int var0, float var1, float var2, long var3);

    public static void glVertexAttrib2d(int n, double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib2d;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglVertexAttrib2d(n, d, d2, l);
    }

    static native void nglVertexAttrib2d(int var0, double var1, double var3, long var5);

    public static void glVertexAttrib3s(int n, short s, short s2, short s3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib3s;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglVertexAttrib3s(n, s, s2, s3, l);
    }

    static native void nglVertexAttrib3s(int var0, short var1, short var2, short var3, long var4);

    public static void glVertexAttrib3f(int n, float f, float f2, float f3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib3f;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglVertexAttrib3f(n, f, f2, f3, l);
    }

    static native void nglVertexAttrib3f(int var0, float var1, float var2, float var3, long var4);

    public static void glVertexAttrib3d(int n, double d, double d2, double d3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib3d;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglVertexAttrib3d(n, d, d2, d3, l);
    }

    static native void nglVertexAttrib3d(int var0, double var1, double var3, double var5, long var7);

    public static void glVertexAttrib4s(int n, short s, short s2, short s3, short s4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib4s;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglVertexAttrib4s(n, s, s2, s3, s4, l);
    }

    static native void nglVertexAttrib4s(int var0, short var1, short var2, short var3, short var4, long var5);

    public static void glVertexAttrib4f(int n, float f, float f2, float f3, float f4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib4f;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglVertexAttrib4f(n, f, f2, f3, f4, l);
    }

    static native void nglVertexAttrib4f(int var0, float var1, float var2, float var3, float var4, long var5);

    public static void glVertexAttrib4d(int n, double d, double d2, double d3, double d4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib4d;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglVertexAttrib4d(n, d, d2, d3, d4, l);
    }

    static native void nglVertexAttrib4d(int var0, double var1, double var3, double var5, double var7, long var9);

    public static void glVertexAttrib4Nub(int n, byte by, byte by2, byte by3, byte by4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttrib4Nub;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglVertexAttrib4Nub(n, by, by2, by3, by4, l);
    }

    static native void nglVertexAttrib4Nub(int var0, byte var1, byte var2, byte var3, byte var4, long var5);

    public static void glVertexAttribPointer(int n, int n2, boolean bl, int n3, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(doubleBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = doubleBuffer;
        }
        GL20.nglVertexAttribPointer(n, n2, 5130, bl, n3, MemoryUtil.getAddress(doubleBuffer), l);
    }

    public static void glVertexAttribPointer(int n, int n2, boolean bl, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(floatBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = floatBuffer;
        }
        GL20.nglVertexAttribPointer(n, n2, 5126, bl, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    public static void glVertexAttribPointer(int n, int n2, boolean bl, boolean bl2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = byteBuffer;
        }
        GL20.nglVertexAttribPointer(n, n2, bl ? 5121 : 5120, bl2, n3, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glVertexAttribPointer(int n, int n2, boolean bl, boolean bl2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = intBuffer;
        }
        GL20.nglVertexAttribPointer(n, n2, bl ? 5125 : 5124, bl2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glVertexAttribPointer(int n, int n2, boolean bl, boolean bl2, int n3, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = shortBuffer;
        }
        GL20.nglVertexAttribPointer(n, n2, bl ? 5123 : 5122, bl2, n3, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglVertexAttribPointer(int var0, int var1, int var2, boolean var3, int var4, long var5, long var7);

    public static void glVertexAttribPointer(int n, int n2, int n3, boolean bl, int n4, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glVertexAttribPointer;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        GL20.nglVertexAttribPointerBO(n, n2, n3, bl, n4, l, l2);
    }

    static native void nglVertexAttribPointerBO(int var0, int var1, int var2, boolean var3, int var4, long var5, long var7);

    public static void glVertexAttribPointer(int n, int n2, int n3, boolean bl, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribPointer;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).glVertexAttribPointer_buffer[n] = byteBuffer;
        }
        GL20.nglVertexAttribPointer(n, n2, n3, bl, n4, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glEnableVertexAttribArray(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glEnableVertexAttribArray;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglEnableVertexAttribArray(n, l);
    }

    static native void nglEnableVertexAttribArray(int var0, long var1);

    public static void glDisableVertexAttribArray(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDisableVertexAttribArray;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglDisableVertexAttribArray(n, l);
    }

    static native void nglDisableVertexAttribArray(int var0, long var1);

    public static void glGetVertexAttrib(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribfv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        GL20.nglGetVertexAttribfv(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetVertexAttribfv(int var0, int var1, long var2, long var4);

    public static void glGetVertexAttrib(int n, int n2, DoubleBuffer doubleBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribdv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(doubleBuffer, 4);
        GL20.nglGetVertexAttribdv(n, n2, MemoryUtil.getAddress(doubleBuffer), l);
    }

    static native void nglGetVertexAttribdv(int var0, int var1, long var2, long var4);

    public static void glGetVertexAttrib(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        GL20.nglGetVertexAttribiv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetVertexAttribiv(int var0, int var1, long var2, long var4);

    public static ByteBuffer glGetVertexAttribPointer(int n, int n2, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetVertexAttribPointerv;
        BufferChecks.checkFunctionAddress(l2);
        ByteBuffer byteBuffer = GL20.nglGetVertexAttribPointerv(n, n2, l, l2);
        if (LWJGLUtil.CHECKS && byteBuffer == null) {
            return null;
        }
        return byteBuffer.order(ByteOrder.nativeOrder());
    }

    static native ByteBuffer nglGetVertexAttribPointerv(int var0, int var1, long var2, long var4);

    public static void glGetVertexAttribPointer(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribPointerv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, PointerBuffer.getPointerSize());
        GL20.nglGetVertexAttribPointerv2(n, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetVertexAttribPointerv2(int var0, int var1, long var2, long var4);

    public static void glBindAttribLocation(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindAttribLocation;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        GL20.nglBindAttribLocation(n, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglBindAttribLocation(int var0, int var1, long var2, long var4);

    public static void glBindAttribLocation(int n, int n2, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBindAttribLocation;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglBindAttribLocation(n, n2, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
    }

    public static void glGetActiveAttrib(int n, int n2, IntBuffer intBuffer, IntBuffer intBuffer2, IntBuffer intBuffer3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveAttrib;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkBuffer(intBuffer2, 1);
        BufferChecks.checkBuffer(intBuffer3, 1);
        BufferChecks.checkDirect(byteBuffer);
        GL20.nglGetActiveAttrib(n, n2, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(intBuffer2), MemoryUtil.getAddress(intBuffer3), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetActiveAttrib(int var0, int var1, int var2, long var3, long var5, long var7, long var9, long var11);

    public static String glGetActiveAttrib(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveAttrib;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 2);
        IntBuffer intBuffer2 = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        IntBuffer intBuffer3 = intBuffer;
        GL20.nglGetActiveAttrib(n, n2, n3, MemoryUtil.getAddress0(intBuffer2), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer3, intBuffer3.position() + 1), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer2.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static String glGetActiveAttrib(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveAttrib;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        GL20.nglGetActiveAttrib(n, n2, n3, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress0(APIUtil.getBufferInt(contextCapabilities)), MemoryUtil.getAddress(APIUtil.getBufferInt(contextCapabilities), 1), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static int glGetActiveAttribSize(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveAttrib;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getBufferInt(contextCapabilities);
        GL20.nglGetActiveAttrib(n, n2, 0, 0L, MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(intBuffer, 1), APIUtil.getBufferByte0(contextCapabilities), l);
        return intBuffer.get(0);
    }

    public static int glGetActiveAttribType(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetActiveAttrib;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getBufferInt(contextCapabilities);
        GL20.nglGetActiveAttrib(n, n2, 0, 0L, MemoryUtil.getAddress(intBuffer, 1), MemoryUtil.getAddress(intBuffer), APIUtil.getBufferByte0(contextCapabilities), l);
        return intBuffer.get(0);
    }

    public static int glGetAttribLocation(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetAttribLocation;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = GL20.nglGetAttribLocation(n, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglGetAttribLocation(int var0, long var1, long var3);

    public static int glGetAttribLocation(int n, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetAttribLocation;
        BufferChecks.checkFunctionAddress(l);
        n = GL20.nglGetAttribLocation(n, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }

    public static void glDrawBuffers(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawBuffers;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL20.nglDrawBuffers(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglDrawBuffers(int var0, long var1, long var3);

    public static void glDrawBuffers(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawBuffers;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglDrawBuffers(1, APIUtil.getInt(contextCapabilities, n), l);
    }

    public static void glStencilOpSeparate(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glStencilOpSeparate;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglStencilOpSeparate(n, n2, n3, n4, l);
    }

    static native void nglStencilOpSeparate(int var0, int var1, int var2, int var3, long var4);

    public static void glStencilFuncSeparate(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glStencilFuncSeparate;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglStencilFuncSeparate(n, n2, n3, n4, l);
    }

    static native void nglStencilFuncSeparate(int var0, int var1, int var2, int var3, long var4);

    public static void glStencilMaskSeparate(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glStencilMaskSeparate;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglStencilMaskSeparate(n, n2, l);
    }

    static native void nglStencilMaskSeparate(int var0, int var1, long var2);

    public static void glBlendEquationSeparate(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glBlendEquationSeparate;
        BufferChecks.checkFunctionAddress(l);
        GL20.nglBlendEquationSeparate(n, n2, l);
    }

    static native void nglBlendEquationSeparate(int var0, int var1, long var2);
}
