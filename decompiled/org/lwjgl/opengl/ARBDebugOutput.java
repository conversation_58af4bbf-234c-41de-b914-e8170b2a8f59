/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ARBDebugOutputCallback;
import org.lwjgl.opengl.CallbackUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ARBDebugOutput {
    public static final int GL_DEBUG_OUTPUT_SYNCHRONOUS_ARB = 33346;
    public static final int GL_MAX_DEBUG_MESSAGE_LENGTH_ARB = 37187;
    public static final int GL_MAX_DEBUG_LOGGED_MESSAGES_ARB = 37188;
    public static final int GL_DEBUG_LOGGED_MESSAGES_ARB = 37189;
    public static final int GL_DEBUG_NEXT_LOGGED_MESSAGE_LENGTH_ARB = 33347;
    public static final int GL_DEBUG_CALLBACK_FUNCTION_ARB = 33348;
    public static final int GL_DEBUG_CALLBACK_USER_PARAM_ARB = 33349;
    public static final int GL_DEBUG_SOURCE_API_ARB = 33350;
    public static final int GL_DEBUG_SOURCE_WINDOW_SYSTEM_ARB = 33351;
    public static final int GL_DEBUG_SOURCE_SHADER_COMPILER_ARB = 33352;
    public static final int GL_DEBUG_SOURCE_THIRD_PARTY_ARB = 33353;
    public static final int GL_DEBUG_SOURCE_APPLICATION_ARB = 33354;
    public static final int GL_DEBUG_SOURCE_OTHER_ARB = 33355;
    public static final int GL_DEBUG_TYPE_ERROR_ARB = 33356;
    public static final int GL_DEBUG_TYPE_DEPRECATED_BEHAVIOR_ARB = 33357;
    public static final int GL_DEBUG_TYPE_UNDEFINED_BEHAVIOR_ARB = 33358;
    public static final int GL_DEBUG_TYPE_PORTABILITY_ARB = 33359;
    public static final int GL_DEBUG_TYPE_PERFORMANCE_ARB = 33360;
    public static final int GL_DEBUG_TYPE_OTHER_ARB = 33361;
    public static final int GL_DEBUG_SEVERITY_HIGH_ARB = 37190;
    public static final int GL_DEBUG_SEVERITY_MEDIUM_ARB = 37191;
    public static final int GL_DEBUG_SEVERITY_LOW_ARB = 37192;

    private ARBDebugOutput() {
    }

    public static void glDebugMessageControlARB(int n, int n2, int n3, IntBuffer intBuffer, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDebugMessageControlARB;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkDirect(intBuffer);
        }
        ARBDebugOutput.nglDebugMessageControlARB(n, n2, n3, intBuffer == null ? 0 : intBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), bl, l);
    }

    static native void nglDebugMessageControlARB(int var0, int var1, int var2, int var3, long var4, boolean var6, long var7);

    public static void glDebugMessageInsertARB(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDebugMessageInsertARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        ARBDebugOutput.nglDebugMessageInsertARB(n, n2, n3, n4, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglDebugMessageInsertARB(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glDebugMessageInsertARB(int n, int n2, int n3, int n4, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDebugMessageInsertARB;
        BufferChecks.checkFunctionAddress(l);
        ARBDebugOutput.nglDebugMessageInsertARB(n, n2, n3, n4, charSequence.length(), APIUtil.getBuffer(contextCapabilities, charSequence), l);
    }

    public static void glDebugMessageCallbackARB(ARBDebugOutputCallback aRBDebugOutputCallback) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDebugMessageCallbackARB;
        BufferChecks.checkFunctionAddress(l);
        long l2 = aRBDebugOutputCallback == null ? 0L : CallbackUtil.createGlobalRef(aRBDebugOutputCallback.getHandler());
        CallbackUtil.registerContextCallbackARB(l2);
        ARBDebugOutput.nglDebugMessageCallbackARB(aRBDebugOutputCallback == null ? 0L : aRBDebugOutputCallback.getPointer(), l2, l);
    }

    static native void nglDebugMessageCallbackARB(long var0, long var2, long var4);

    public static int glGetDebugMessageLogARB(int n, IntBuffer intBuffer, IntBuffer intBuffer2, IntBuffer intBuffer3, IntBuffer intBuffer4, IntBuffer intBuffer5, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetDebugMessageLogARB;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, n);
        }
        if (intBuffer2 != null) {
            BufferChecks.checkBuffer(intBuffer2, n);
        }
        if (intBuffer3 != null) {
            BufferChecks.checkBuffer(intBuffer3, n);
        }
        if (intBuffer4 != null) {
            BufferChecks.checkBuffer(intBuffer4, n);
        }
        if (intBuffer5 != null) {
            BufferChecks.checkBuffer(intBuffer5, n);
        }
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        n = ARBDebugOutput.nglGetDebugMessageLogARB(n, byteBuffer == null ? 0 : byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddressSafe(intBuffer2), MemoryUtil.getAddressSafe(intBuffer3), MemoryUtil.getAddressSafe(intBuffer4), MemoryUtil.getAddressSafe(intBuffer5), MemoryUtil.getAddressSafe(byteBuffer), l);
        return n;
    }

    static native int nglGetDebugMessageLogARB(int var0, int var1, long var2, long var4, long var6, long var8, long var10, long var12, long var14);
}
