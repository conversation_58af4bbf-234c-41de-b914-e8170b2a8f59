/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.opengl.PeerInfo;
import org.lwjgl.opengl.PixelFormat;

abstract class WindowsPeerInfo
extends PeerInfo {
    protected WindowsPeerInfo() {
        super(WindowsPeerInfo.createHandle());
    }

    private static native ByteBuffer createHandle();

    protected static int choosePixelFormat(long l, int n, int n2, PixelFormat pixelFormat, IntBuffer intBuffer, boolean bl, boolean bl2, boolean bl3, boolean bl4) {
        return WindowsPeerInfo.nChoosePixelFormat(l, n, n2, pixelFormat, intBuffer, bl, bl2, bl3, bl4);
    }

    private static native int nChoosePixelFormat(long var0, int var2, int var3, PixelFormat var4, IntBuffer var5, boolean var6, boolean var7, boolean var8, boolean var9);

    protected static native void setPixelFormat(long var0, int var2);

    public final long getHdc() {
        return WindowsPeerInfo.nGetHdc(this.getHandle());
    }

    private static native long nGetHdc(ByteBuffer var0);

    public final long getHwnd() {
        return WindowsPeerInfo.nGetHwnd(this.getHandle());
    }

    private static native long nGetHwnd(ByteBuffer var0);
}
