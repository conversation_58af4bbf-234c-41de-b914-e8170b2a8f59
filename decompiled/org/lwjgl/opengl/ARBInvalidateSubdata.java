/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.IntBuffer;
import org.lwjgl.opengl.GL43;

public final class ARBInvalidateSubdata {
    private ARBInvalidateSubdata() {
    }

    public static void glInvalidateTexSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8) {
        GL43.glInvalidateTexSubImage(n, n2, n3, n4, n5, n6, n7, n8);
    }

    public static void glInvalidateTexImage(int n, int n2) {
        GL43.glInvalidateTexImage(n, n2);
    }

    public static void glInvalidateBufferSubData(int n, long l, long l2) {
        GL43.glInvalidateBufferSubData(n, l, l2);
    }

    public static void glInvalidateBufferData(int n) {
        GL43.glInvalidateBufferData(n);
    }

    public static void glInvalidateFramebuffer(int n, IntBuffer intBuffer) {
        GL43.glInvalidateFramebuffer(n, intBuffer);
    }

    public static void glInvalidateSubFramebuffer(int n, IntBuffer intBuffer, int n2, int n3, int n4, int n5) {
        GL43.glInvalidateSubFramebuffer(n, intBuffer, n2, n3, n4, n5);
    }
}
