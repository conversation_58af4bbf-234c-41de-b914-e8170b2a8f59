/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

public final class APPLEFloatPixels {
    public static final int GL_HALF_APPLE = 5131;
    public static final int GL_COLOR_FLOAT_APPLE = 35343;
    public static final int GL_RGBA_FLOAT32_APPLE = 34836;
    public static final int GL_RGB_FLOAT32_APPLE = 34837;
    public static final int GL_ALPHA_FLOAT32_APPLE = 34838;
    public static final int GL_INTENSITY_FLOAT32_APPLE = 34839;
    public static final int GL_LUMINANCE_FLOAT32_APPLE = 34840;
    public static final int GL_LUMINANCE_ALPHA_FLOAT32_APPLE = 34841;
    public static final int GL_RGBA_FLOAT16_APPLE = 34842;
    public static final int GL_RGB_FLOAT16_APPLE = 34843;
    public static final int GL_ALPHA_FLOAT16_APPLE = 34844;
    public static final int GL_INTENSITY_FLOAT16_APPLE = 34845;
    public static final int GL_LUMINANCE_FLOAT16_APPLE = 34846;
    public static final int GL_LUMINANCE_ALPHA_FLOAT16_APPLE = 34847;

    private APPLEFloatPixels() {
    }
}
