/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ARBSparseTexture {
    public static final int GL_TEXTURE_SPARSE_ARB = 37286;
    public static final int GL_VIRTUAL_PAGE_SIZE_INDEX_ARB = 37287;
    public static final int GL_NUM_SPARSE_LEVELS_ARB = 37290;
    public static final int GL_NUM_VIRTUAL_PAGE_SIZES_ARB = 37288;
    public static final int GL_VIRTUAL_PAGE_SIZE_X_ARB = 37269;
    public static final int GL_VIRTUAL_PAGE_SIZE_Y_ARB = 37270;
    public static final int GL_VIRTUAL_PAGE_SIZE_Z_ARB = 37271;
    public static final int GL_MAX_SPARSE_TEXTURE_SIZE_ARB = 37272;
    public static final int GL_MAX_SPARSE_3D_TEXTURE_SIZE_ARB = 37273;
    public static final int GL_MAX_SPARSE_ARRAY_TEXTURE_LAYERS_ARB = 37274;
    public static final int GL_SPARSE_TEXTURE_FULL_ARRAY_CUBE_MIPMAPS_ARB = 37289;

    private ARBSparseTexture() {
    }

    public static void glTexPageCommitmentARB(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexPageCommitmentARB;
        BufferChecks.checkFunctionAddress(l);
        ARBSparseTexture.nglTexPageCommitmentARB(n, n2, n3, n4, n5, n6, n7, n8, bl, l);
    }

    static native void nglTexPageCommitmentARB(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, boolean var8, long var9);

    public static void glTexturePageCommitmentEXT(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexturePageCommitmentEXT;
        BufferChecks.checkFunctionAddress(l);
        ARBSparseTexture.nglTexturePageCommitmentEXT(n, n2, n3, n4, n5, n6, n7, n8, n9, bl, l);
    }

    static native void nglTexturePageCommitmentEXT(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, boolean var9, long var10);
}
