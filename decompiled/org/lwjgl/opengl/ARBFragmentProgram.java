/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.opengl.ARBProgram;

public final class ARBFragmentProgram
extends ARBProgram {
    public static final int GL_FRAGMENT_PROGRAM_ARB = 34820;
    public static final int GL_PROGRAM_ALU_INSTRUCTIONS_ARB = 34821;
    public static final int GL_PROGRAM_TEX_INSTRUCTIONS_ARB = 34822;
    public static final int GL_PROGRAM_TEX_INDIRECTIONS_ARB = 34823;
    public static final int GL_PROGRAM_NATIVE_ALU_INSTRUCTIONS_ARB = 34824;
    public static final int GL_PROGRAM_NATIVE_TEX_INSTRUCTIONS_ARB = 34825;
    public static final int GL_PROGRAM_NATIVE_TEX_INDIRECTIONS_ARB = 34826;
    public static final int GL_MAX_PROGRAM_ALU_INSTRUCTIONS_ARB = 34827;
    public static final int GL_MAX_PROGRAM_TEX_INSTRUCTIONS_ARB = 34828;
    public static final int GL_MAX_PROGRAM_TEX_INDIRECTIONS_ARB = 34829;
    public static final int GL_MAX_PROGRAM_NATIVE_ALU_INSTRUCTIONS_ARB = 34830;
    public static final int GL_MAX_PROGRAM_NATIVE_TEX_INSTRUCTIONS_ARB = 34831;
    public static final int GL_MAX_PROGRAM_NATIVE_TEX_INDIRECTIONS_ARB = 34832;
    public static final int GL_MAX_TEXTURE_COORDS_ARB = 34929;
    public static final int GL_MAX_TEXTURE_IMAGE_UNITS_ARB = 34930;

    private ARBFragmentProgram() {
    }
}
