/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;

public final class ARBIndirectParameters {
    public static final int GL_PARAMETER_BUFFER_ARB = 33006;
    public static final int GL_PARAMETER_BUFFER_BINDING_ARB = 33007;

    private ARBIndirectParameters() {
    }

    public static void glMultiDrawArraysIndirectCountARB(int n, ByteBuffer byteBuffer, long l, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMultiDrawArraysIndirectCountARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureIndirectBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, (n3 == 0 ? 16 : n3) * n2);
        ARBIndirectParameters.nglMultiDrawArraysIndirectCountARB(n, MemoryUtil.getAddress(byteBuffer), l, n2, n3, l2);
    }

    static native void nglMultiDrawArraysIndirectCountARB(int var0, long var1, long var3, int var5, int var6, long var7);

    public static void glMultiDrawArraysIndirectCountARB(int n, long l, long l2, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glMultiDrawArraysIndirectCountARB;
        BufferChecks.checkFunctionAddress(l3);
        GLChecks.ensureIndirectBOenabled(contextCapabilities);
        ARBIndirectParameters.nglMultiDrawArraysIndirectCountARBBO(n, l, l2, n2, n3, l3);
    }

    static native void nglMultiDrawArraysIndirectCountARBBO(int var0, long var1, long var3, int var5, int var6, long var7);

    public static void glMultiDrawArraysIndirectCountARB(int n, IntBuffer intBuffer, long l, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMultiDrawArraysIndirectCountARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureIndirectBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(intBuffer, (n3 == 0 ? 4 : n3 >> 2) * n2);
        ARBIndirectParameters.nglMultiDrawArraysIndirectCountARB(n, MemoryUtil.getAddress(intBuffer), l, n2, n3, l2);
    }

    public static void glMultiDrawElementsIndirectCountARB(int n, int n2, ByteBuffer byteBuffer, long l, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMultiDrawElementsIndirectCountARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureIndirectBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, (n4 == 0 ? 20 : n4) * n3);
        ARBIndirectParameters.nglMultiDrawElementsIndirectCountARB(n, n2, MemoryUtil.getAddress(byteBuffer), l, n3, n4, l2);
    }

    static native void nglMultiDrawElementsIndirectCountARB(int var0, int var1, long var2, long var4, int var6, int var7, long var8);

    public static void glMultiDrawElementsIndirectCountARB(int n, int n2, long l, long l2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glMultiDrawElementsIndirectCountARB;
        BufferChecks.checkFunctionAddress(l3);
        GLChecks.ensureIndirectBOenabled(contextCapabilities);
        ARBIndirectParameters.nglMultiDrawElementsIndirectCountARBBO(n, n2, l, l2, n3, n4, l3);
    }

    static native void nglMultiDrawElementsIndirectCountARBBO(int var0, int var1, long var2, long var4, int var6, int var7, long var8);

    public static void glMultiDrawElementsIndirectCountARB(int n, int n2, IntBuffer intBuffer, long l, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMultiDrawElementsIndirectCountARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureIndirectBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(intBuffer, (n4 == 0 ? 5 : n4 >> 2) * n3);
        ARBIndirectParameters.nglMultiDrawElementsIndirectCountARB(n, n2, MemoryUtil.getAddress(intBuffer), l, n3, n4, l2);
    }
}
