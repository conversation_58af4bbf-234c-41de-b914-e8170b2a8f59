/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.LongBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVVertexAttribInteger64bit {
    public static final int GL_INT64_NV = 5134;
    public static final int GL_UNSIGNED_INT64_NV = 5135;

    private NVVertexAttribInteger64bit() {
    }

    public static void glVertexAttribL1i64NV(int n, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glVertexAttribL1i64NV;
        BufferChecks.checkFunctionAddress(l2);
        NVVertexAttribInteger64bit.nglVertexAttribL1i64NV(n, l, l2);
    }

    static native void nglVertexAttribL1i64NV(int var0, long var1, long var3);

    public static void glVertexAttribL2i64NV(int n, long l, long l2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glVertexAttribL2i64NV;
        BufferChecks.checkFunctionAddress(l3);
        NVVertexAttribInteger64bit.nglVertexAttribL2i64NV(n, l, l2, l3);
    }

    static native void nglVertexAttribL2i64NV(int var0, long var1, long var3, long var5);

    public static void glVertexAttribL3i64NV(int n, long l, long l2, long l3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l4 = contextCapabilities.glVertexAttribL3i64NV;
        BufferChecks.checkFunctionAddress(l4);
        NVVertexAttribInteger64bit.nglVertexAttribL3i64NV(n, l, l2, l3, l4);
    }

    static native void nglVertexAttribL3i64NV(int var0, long var1, long var3, long var5, long var7);

    public static void glVertexAttribL4i64NV(int n, long l, long l2, long l3, long l4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l5 = contextCapabilities.glVertexAttribL4i64NV;
        BufferChecks.checkFunctionAddress(l5);
        NVVertexAttribInteger64bit.nglVertexAttribL4i64NV(n, l, l2, l3, l4, l5);
    }

    static native void nglVertexAttribL4i64NV(int var0, long var1, long var3, long var5, long var7, long var9);

    public static void glVertexAttribL1NV(int n, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL1i64vNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 1);
        NVVertexAttribInteger64bit.nglVertexAttribL1i64vNV(n, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglVertexAttribL1i64vNV(int var0, long var1, long var3);

    public static void glVertexAttribL2NV(int n, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL2i64vNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 2);
        NVVertexAttribInteger64bit.nglVertexAttribL2i64vNV(n, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglVertexAttribL2i64vNV(int var0, long var1, long var3);

    public static void glVertexAttribL3NV(int n, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL3i64vNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 3);
        NVVertexAttribInteger64bit.nglVertexAttribL3i64vNV(n, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglVertexAttribL3i64vNV(int var0, long var1, long var3);

    public static void glVertexAttribL4NV(int n, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL4i64vNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 4);
        NVVertexAttribInteger64bit.nglVertexAttribL4i64vNV(n, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglVertexAttribL4i64vNV(int var0, long var1, long var3);

    public static void glVertexAttribL1ui64NV(int n, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glVertexAttribL1ui64NV;
        BufferChecks.checkFunctionAddress(l2);
        NVVertexAttribInteger64bit.nglVertexAttribL1ui64NV(n, l, l2);
    }

    static native void nglVertexAttribL1ui64NV(int var0, long var1, long var3);

    public static void glVertexAttribL2ui64NV(int n, long l, long l2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glVertexAttribL2ui64NV;
        BufferChecks.checkFunctionAddress(l3);
        NVVertexAttribInteger64bit.nglVertexAttribL2ui64NV(n, l, l2, l3);
    }

    static native void nglVertexAttribL2ui64NV(int var0, long var1, long var3, long var5);

    public static void glVertexAttribL3ui64NV(int n, long l, long l2, long l3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l4 = contextCapabilities.glVertexAttribL3ui64NV;
        BufferChecks.checkFunctionAddress(l4);
        NVVertexAttribInteger64bit.nglVertexAttribL3ui64NV(n, l, l2, l3, l4);
    }

    static native void nglVertexAttribL3ui64NV(int var0, long var1, long var3, long var5, long var7);

    public static void glVertexAttribL4ui64NV(int n, long l, long l2, long l3, long l4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l5 = contextCapabilities.glVertexAttribL4ui64NV;
        BufferChecks.checkFunctionAddress(l5);
        NVVertexAttribInteger64bit.nglVertexAttribL4ui64NV(n, l, l2, l3, l4, l5);
    }

    static native void nglVertexAttribL4ui64NV(int var0, long var1, long var3, long var5, long var7, long var9);

    public static void glVertexAttribL1uNV(int n, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL1ui64vNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 1);
        NVVertexAttribInteger64bit.nglVertexAttribL1ui64vNV(n, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglVertexAttribL1ui64vNV(int var0, long var1, long var3);

    public static void glVertexAttribL2uNV(int n, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL2ui64vNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 2);
        NVVertexAttribInteger64bit.nglVertexAttribL2ui64vNV(n, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglVertexAttribL2ui64vNV(int var0, long var1, long var3);

    public static void glVertexAttribL3uNV(int n, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL3ui64vNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 3);
        NVVertexAttribInteger64bit.nglVertexAttribL3ui64vNV(n, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglVertexAttribL3ui64vNV(int var0, long var1, long var3);

    public static void glVertexAttribL4uNV(int n, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribL4ui64vNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 4);
        NVVertexAttribInteger64bit.nglVertexAttribL4ui64vNV(n, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglVertexAttribL4ui64vNV(int var0, long var1, long var3);

    public static void glGetVertexAttribLNV(int n, int n2, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribLi64vNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 4);
        NVVertexAttribInteger64bit.nglGetVertexAttribLi64vNV(n, n2, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetVertexAttribLi64vNV(int var0, int var1, long var2, long var4);

    public static void glGetVertexAttribLuNV(int n, int n2, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetVertexAttribLui64vNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(longBuffer, 4);
        NVVertexAttribInteger64bit.nglGetVertexAttribLui64vNV(n, n2, MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetVertexAttribLui64vNV(int var0, int var1, long var2, long var4);

    public static void glVertexAttribLFormatNV(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribLFormatNV;
        BufferChecks.checkFunctionAddress(l);
        NVVertexAttribInteger64bit.nglVertexAttribLFormatNV(n, n2, n3, n4, l);
    }

    static native void nglVertexAttribLFormatNV(int var0, int var1, int var2, int var3, long var4);
}
