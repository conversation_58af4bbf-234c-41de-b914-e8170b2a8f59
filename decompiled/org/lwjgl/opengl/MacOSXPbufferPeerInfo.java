/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.MacOSXPeerInfo;
import org.lwjgl.opengl.PixelFormat;

final class MacOSXPbufferPeerInfo
extends MacOSXPeerInfo {
    MacOSXPbufferPeerInfo(int n, int n2, PixelFormat pixelFormat, ContextAttribs contextAttribs) {
        super(pixelFormat, contextAttribs, false, false, true, false);
        MacOSXPbufferPeerInfo.nCreate(this.getHandle(), n, n2);
    }

    private static native void nCreate(ByteBuffer var0, int var1, int var2);

    public final void destroy() {
        MacOSXPbufferPeerInfo.nDestroy(this.getHandle());
    }

    private static native void nDestroy(ByteBuffer var0);

    protected final void doLockAndInitHandle() {
    }

    protected final void doUnlock() {
    }
}
