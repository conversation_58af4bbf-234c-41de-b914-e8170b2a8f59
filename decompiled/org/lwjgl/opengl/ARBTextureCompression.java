/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;

public final class ARBTextureCompression {
    public static final int GL_COMPRESSED_ALPHA_ARB = 34025;
    public static final int GL_COMPRESSED_LUMINANCE_ARB = 34026;
    public static final int GL_COMPRESSED_LUMINANCE_ALPHA_ARB = 34027;
    public static final int GL_COMPRESSED_INTENSITY_ARB = 34028;
    public static final int GL_COMPRESSED_RGB_ARB = 34029;
    public static final int GL_COMPRESSED_RGBA_ARB = 34030;
    public static final int GL_TEXTURE_COMPRESSION_HINT_ARB = 34031;
    public static final int GL_TEXTURE_COMPRESSED_IMAGE_SIZE_ARB = 34464;
    public static final int GL_TEXTURE_COMPRESSED_ARB = 34465;
    public static final int GL_NUM_COMPRESSED_TEXTURE_FORMATS_ARB = 34466;
    public static final int GL_COMPRESSED_TEXTURE_FORMATS_ARB = 34467;

    private ARBTextureCompression() {
    }

    public static void glCompressedTexImage1DARB(int n, int n2, int n3, int n4, int n5, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexImage1DARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBTextureCompression.nglCompressedTexImage1DARB(n, n2, n3, n4, n5, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTexImage1DARB(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glCompressedTexImage1DARB(int n, int n2, int n3, int n4, int n5, int n6, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTexImage1DARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        ARBTextureCompression.nglCompressedTexImage1DARBBO(n, n2, n3, n4, n5, n6, l, l2);
    }

    static native void nglCompressedTexImage1DARBBO(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glCompressedTexImage2DARB(int n, int n2, int n3, int n4, int n5, int n6, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexImage2DARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBTextureCompression.nglCompressedTexImage2DARB(n, n2, n3, n4, n5, n6, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTexImage2DARB(int var0, int var1, int var2, int var3, int var4, int var5, int var6, long var7, long var9);

    public static void glCompressedTexImage2DARB(int n, int n2, int n3, int n4, int n5, int n6, int n7, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTexImage2DARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        ARBTextureCompression.nglCompressedTexImage2DARBBO(n, n2, n3, n4, n5, n6, n7, l, l2);
    }

    static native void nglCompressedTexImage2DARBBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, long var7, long var9);

    public static void glCompressedTexImage3DARB(int n, int n2, int n3, int n4, int n5, int n6, int n7, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexImage3DARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBTextureCompression.nglCompressedTexImage3DARB(n, n2, n3, n4, n5, n6, n7, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTexImage3DARB(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glCompressedTexImage3DARB(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTexImage3DARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        ARBTextureCompression.nglCompressedTexImage3DARBBO(n, n2, n3, n4, n5, n6, n7, n8, l, l2);
    }

    static native void nglCompressedTexImage3DARBBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glCompressedTexSubImage1DARB(int n, int n2, int n3, int n4, int n5, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexSubImage1DARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBTextureCompression.nglCompressedTexSubImage1DARB(n, n2, n3, n4, n5, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTexSubImage1DARB(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glCompressedTexSubImage1DARB(int n, int n2, int n3, int n4, int n5, int n6, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTexSubImage1DARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        ARBTextureCompression.nglCompressedTexSubImage1DARBBO(n, n2, n3, n4, n5, n6, l, l2);
    }

    static native void nglCompressedTexSubImage1DARBBO(int var0, int var1, int var2, int var3, int var4, int var5, long var6, long var8);

    public static void glCompressedTexSubImage2DARB(int n, int n2, int n3, int n4, int n5, int n6, int n7, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexSubImage2DARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBTextureCompression.nglCompressedTexSubImage2DARB(n, n2, n3, n4, n5, n6, n7, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTexSubImage2DARB(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glCompressedTexSubImage2DARB(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTexSubImage2DARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        ARBTextureCompression.nglCompressedTexSubImage2DARBBO(n, n2, n3, n4, n5, n6, n7, n8, l, l2);
    }

    static native void nglCompressedTexSubImage2DARBBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glCompressedTexSubImage3DARB(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCompressedTexSubImage3DARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureUnpackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBTextureCompression.nglCompressedTexSubImage3DARB(n, n2, n3, n4, n5, n6, n7, n8, n9, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglCompressedTexSubImage3DARB(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9, long var10, long var12);

    public static void glCompressedTexSubImage3DARB(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glCompressedTexSubImage3DARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureUnpackPBOenabled(contextCapabilities);
        ARBTextureCompression.nglCompressedTexSubImage3DARBBO(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, l, l2);
    }

    static native void nglCompressedTexSubImage3DARBBO(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9, long var10, long var12);

    public static void glGetCompressedTexImageARB(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetCompressedTexImageARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensurePackPBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        ARBTextureCompression.nglGetCompressedTexImageARB(n, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetCompressedTexImageARB(int var0, int var1, long var2, long var4);

    public static void glGetCompressedTexImageARB(int n, int n2, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glGetCompressedTexImageARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensurePackPBOenabled(contextCapabilities);
        ARBTextureCompression.nglGetCompressedTexImageARBBO(n, n2, l, l2);
    }

    static native void nglGetCompressedTexImageARBBO(int var0, int var1, long var2, long var4);
}
