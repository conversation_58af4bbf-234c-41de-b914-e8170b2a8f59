/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class AMDSparseTexture {
    public static final int GL_TEXTURE_STORAGE_SPARSE_BIT_AMD = 1;
    public static final int GL_VIRTUAL_PAGE_SIZE_X_AMD = 37269;
    public static final int GL_VIRTUAL_PAGE_SIZE_Y_AMD = 37270;
    public static final int GL_VIRTUAL_PAGE_SIZE_Z_AMD = 37271;
    public static final int GL_MAX_SPARSE_TEXTURE_SIZE_AMD = 37272;
    public static final int GL_MAX_SPARSE_3D_TEXTURE_SIZE_AMD = 37273;
    public static final int GL_MAX_SPARSE_ARRAY_TEXTURE_LAYERS = 37274;
    public static final int GL_MIN_SPARSE_LEVEL_AMD = 37275;
    public static final int GL_MIN_LOD_WARNING_AMD = 37276;

    private AMDSparseTexture() {
    }

    public static void glTexStorageSparseAMD(int n, int n2, int n3, int n4, int n5, int n6, int n7) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexStorageSparseAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDSparseTexture.nglTexStorageSparseAMD(n, n2, n3, n4, n5, n6, n7, l);
    }

    static native void nglTexStorageSparseAMD(int var0, int var1, int var2, int var3, int var4, int var5, int var6, long var7);

    public static void glTextureStorageSparseAMD(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureStorageSparseAMD;
        BufferChecks.checkFunctionAddress(l);
        AMDSparseTexture.nglTextureStorageSparseAMD(n, n2, n3, n4, n5, n6, n7, n8, l);
    }

    static native void nglTextureStorageSparseAMD(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8);
}
