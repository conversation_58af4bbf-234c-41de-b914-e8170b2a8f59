/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ATIElementArray {
    public static final int GL_ELEMENT_ARRAY_ATI = 34664;
    public static final int GL_ELEMENT_ARRAY_TYPE_ATI = 34665;
    public static final int GL_ELEMENT_ARRAY_POINTER_ATI = 34666;

    private ATIElementArray() {
    }

    public static void glElementPointerATI(ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glElementPointerATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        ATIElementArray.nglElementPointerATI(5121, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glElementPointerATI(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glElementPointerATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ATIElementArray.nglElementPointerATI(5125, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glElementPointerATI(ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glElementPointerATI;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        ATIElementArray.nglElementPointerATI(5123, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglElementPointerATI(int var0, long var1, long var3);

    public static void glDrawElementArrayATI(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawElementArrayATI;
        BufferChecks.checkFunctionAddress(l);
        ATIElementArray.nglDrawElementArrayATI(n, n2, l);
    }

    static native void nglDrawElementArrayATI(int var0, int var1, long var2);

    public static void glDrawRangeElementArrayATI(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDrawRangeElementArrayATI;
        BufferChecks.checkFunctionAddress(l);
        ATIElementArray.nglDrawRangeElementArrayATI(n, n2, n3, n4, l);
    }

    static native void nglDrawRangeElementArrayATI(int var0, int var1, int var2, int var3, long var4);
}
