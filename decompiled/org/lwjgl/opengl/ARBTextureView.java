/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.opengl.GL43;

public final class ARBTextureView {
    public static final int GL_TEXTURE_VIEW_MIN_LEVEL = 33499;
    public static final int GL_TEXTURE_VIEW_NUM_LEVELS = 33500;
    public static final int GL_TEXTURE_VIEW_MIN_LAYER = 33501;
    public static final int GL_TEXTURE_VIEW_NUM_LAYERS = 33502;
    public static final int GL_TEXTURE_IMMUTABLE_LEVELS = 33503;

    private ARBTextureView() {
    }

    public static void glTextureView(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8) {
        GL43.glTextureView(n, n2, n3, n4, n5, n6, n7, n8);
    }
}
