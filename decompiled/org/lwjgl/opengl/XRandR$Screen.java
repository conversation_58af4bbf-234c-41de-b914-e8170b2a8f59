/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.util.List;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
public static class XRandR.Screen
implements Cloneable {
    public final String name;
    public final int width;
    public final int height;
    public final int freq;
    public int xPos;
    public int yPos;

    XRandR.Screen(String string, int n, int n2, int n3, int n4, int n5) {
        this.name = string;
        this.width = n;
        this.height = n2;
        this.freq = n3;
        this.xPos = n4;
        this.yPos = n5;
    }

    private void getArgs(List<String> list) {
        list.add("--output");
        list.add(this.name);
        list.add("--mode");
        list.add(this.width + "x" + this.height);
        list.add("--rate");
        list.add(Integer.toString(this.freq));
        list.add("--pos");
        list.add(this.xPos + "x" + this.yPos);
    }

    public String toString() {
        return this.name + " " + this.width + "x" + this.height + " @ " + this.xPos + "x" + this.yPos + " with " + this.freq + "Hz";
    }

    static /* synthetic */ void access$000(XRandR.Screen screen, List list) {
        screen.getArgs(list);
    }
}
