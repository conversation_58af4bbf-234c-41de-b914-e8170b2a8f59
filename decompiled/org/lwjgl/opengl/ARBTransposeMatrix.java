/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.FloatBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class ARBTransposeMatrix {
    public static final int GL_TRANSPOSE_MODELVIEW_MATRIX_ARB = 34019;
    public static final int GL_TRANSPOSE_PROJECTION_MATRIX_ARB = 34020;
    public static final int GL_TRANSPOSE_TEXTURE_MATRIX_ARB = 34021;
    public static final int GL_TRANSPOSE_COLOR_MATRIX_ARB = 34022;

    private ARBTransposeMatrix() {
    }

    public static void glLoadTransposeMatrixARB(FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glLoadTransposeMatrixfARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 16);
        ARBTransposeMatrix.nglLoadTransposeMatrixfARB(MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglLoadTransposeMatrixfARB(long var0, long var2);

    public static void glMultTransposeMatrixARB(FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultTransposeMatrixfARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 16);
        ARBTransposeMatrix.nglMultTransposeMatrixfARB(MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglMultTransposeMatrixfARB(long var0, long var2);
}
