/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;

public final class NVPathRendering {
    public static final int GL_CLOSE_PATH_NV = 0;
    public static final int GL_MOVE_TO_NV = 2;
    public static final int GL_RELATIVE_MOVE_TO_NV = 3;
    public static final int GL_LINE_TO_NV = 4;
    public static final int GL_RELATIVE_LINE_TO_NV = 5;
    public static final int GL_HORIZONTAL_LINE_TO_NV = 6;
    public static final int GL_RELATIVE_HORIZONTAL_LINE_TO_NV = 7;
    public static final int GL_VERTICAL_LINE_TO_NV = 8;
    public static final int GL_RELATIVE_VERTICAL_LINE_TO_NV = 9;
    public static final int GL_QUADRATIC_CURVE_TO_NV = 10;
    public static final int GL_RELATIVE_QUADRATIC_CURVE_TO_NV = 11;
    public static final int GL_CUBIC_CURVE_TO_NV = 12;
    public static final int GL_RELATIVE_CUBIC_CURVE_TO_NV = 13;
    public static final int GL_SMOOTH_QUADRATIC_CURVE_TO_NV = 14;
    public static final int GL_RELATIVE_SMOOTH_QUADRATIC_CURVE_TO_NV = 15;
    public static final int GL_SMOOTH_CUBIC_CURVE_TO_NV = 16;
    public static final int GL_RELATIVE_SMOOTH_CUBIC_CURVE_TO_NV = 17;
    public static final int GL_SMALL_CCW_ARC_TO_NV = 18;
    public static final int GL_RELATIVE_SMALL_CCW_ARC_TO_NV = 19;
    public static final int GL_SMALL_CW_ARC_TO_NV = 20;
    public static final int GL_RELATIVE_SMALL_CW_ARC_TO_NV = 21;
    public static final int GL_LARGE_CCW_ARC_TO_NV = 22;
    public static final int GL_RELATIVE_LARGE_CCW_ARC_TO_NV = 23;
    public static final int GL_LARGE_CW_ARC_TO_NV = 24;
    public static final int GL_RELATIVE_LARGE_CW_ARC_TO_NV = 25;
    public static final int GL_CIRCULAR_CCW_ARC_TO_NV = 248;
    public static final int GL_CIRCULAR_CW_ARC_TO_NV = 250;
    public static final int GL_CIRCULAR_TANGENT_ARC_TO_NV = 252;
    public static final int GL_ARC_TO_NV = 254;
    public static final int GL_RELATIVE_ARC_TO_NV = 255;
    public static final int GL_PATH_FORMAT_SVG_NV = 36976;
    public static final int GL_PATH_FORMAT_PS_NV = 36977;
    public static final int GL_STANDARD_FONT_NAME_NV = 36978;
    public static final int GL_SYSTEM_FONT_NAME_NV = 36979;
    public static final int GL_FILE_NAME_NV = 36980;
    public static final int GL_SKIP_MISSING_GLYPH_NV = 37033;
    public static final int GL_USE_MISSING_GLYPH_NV = 37034;
    public static final int GL_PATH_STROKE_WIDTH_NV = 36981;
    public static final int GL_PATH_INITIAL_END_CAP_NV = 36983;
    public static final int GL_PATH_TERMINAL_END_CAP_NV = 36984;
    public static final int GL_PATH_JOIN_STYLE_NV = 36985;
    public static final int GL_PATH_MITER_LIMIT_NV = 36986;
    public static final int GL_PATH_INITIAL_DASH_CAP_NV = 36988;
    public static final int GL_PATH_TERMINAL_DASH_CAP_NV = 36989;
    public static final int GL_PATH_DASH_OFFSET_NV = 36990;
    public static final int GL_PATH_CLIENT_LENGTH_NV = 36991;
    public static final int GL_PATH_DASH_OFFSET_RESET_NV = 37044;
    public static final int GL_PATH_FILL_MODE_NV = 36992;
    public static final int GL_PATH_FILL_MASK_NV = 36993;
    public static final int GL_PATH_FILL_COVER_MODE_NV = 36994;
    public static final int GL_PATH_STROKE_COVER_MODE_NV = 36995;
    public static final int GL_PATH_STROKE_MASK_NV = 36996;
    public static final int GL_PATH_END_CAPS_NV = 36982;
    public static final int GL_PATH_DASH_CAPS_NV = 36987;
    public static final int GL_COUNT_UP_NV = 37000;
    public static final int GL_COUNT_DOWN_NV = 37001;
    public static final int GL_PRIMARY_COLOR = 34167;
    public static final int GL_PRIMARY_COLOR_NV = 34092;
    public static final int GL_SECONDARY_COLOR_NV = 34093;
    public static final int GL_PATH_OBJECT_BOUNDING_BOX_NV = 37002;
    public static final int GL_CONVEX_HULL_NV = 37003;
    public static final int GL_BOUNDING_BOX_NV = 37005;
    public static final int GL_TRANSLATE_X_NV = 37006;
    public static final int GL_TRANSLATE_Y_NV = 37007;
    public static final int GL_TRANSLATE_2D_NV = 37008;
    public static final int GL_TRANSLATE_3D_NV = 37009;
    public static final int GL_AFFINE_2D_NV = 37010;
    public static final int GL_AFFINE_3D_NV = 37012;
    public static final int GL_TRANSPOSE_AFFINE_2D_NV = 37014;
    public static final int GL_TRANSPOSE_AFFINE_3D_NV = 37016;
    public static final int GL_UTF8_NV = 37018;
    public static final int GL_UTF16_NV = 37019;
    public static final int GL_BOUNDING_BOX_OF_BOUNDING_BOXES_NV = 37020;
    public static final int GL_PATH_COMMAND_COUNT_NV = 37021;
    public static final int GL_PATH_COORD_COUNT_NV = 37022;
    public static final int GL_PATH_DASH_ARRAY_COUNT_NV = 37023;
    public static final int GL_PATH_COMPUTED_LENGTH_NV = 37024;
    public static final int GL_PATH_FILL_BOUNDING_BOX_NV = 37025;
    public static final int GL_PATH_STROKE_BOUNDING_BOX_NV = 37026;
    public static final int GL_SQUARE_NV = 37027;
    public static final int GL_ROUND_NV = 37028;
    public static final int GL_TRIANGULAR_NV = 37029;
    public static final int GL_BEVEL_NV = 37030;
    public static final int GL_MITER_REVERT_NV = 37031;
    public static final int GL_MITER_TRUNCATE_NV = 37032;
    public static final int GL_MOVE_TO_RESETS_NV = 37045;
    public static final int GL_MOVE_TO_CONTINUES_NV = 37046;
    public static final int GL_BOLD_BIT_NV = 1;
    public static final int GL_ITALIC_BIT_NV = 2;
    public static final int GL_PATH_ERROR_POSITION_NV = 37035;
    public static final int GL_PATH_FOG_GEN_MODE_NV = 37036;
    public static final int GL_PATH_STENCIL_FUNC_NV = 37047;
    public static final int GL_PATH_STENCIL_REF_NV = 37048;
    public static final int GL_PATH_STENCIL_VALUE_MASK_NV = 37049;
    public static final int GL_PATH_STENCIL_DEPTH_OFFSET_FACTOR_NV = 37053;
    public static final int GL_PATH_STENCIL_DEPTH_OFFSET_UNITS_NV = 37054;
    public static final int GL_PATH_COVER_DEPTH_FUNC_NV = 37055;
    public static final int GL_GLYPH_WIDTH_BIT_NV = 1;
    public static final int GL_GLYPH_HEIGHT_BIT_NV = 2;
    public static final int GL_GLYPH_HORIZONTAL_BEARING_X_BIT_NV = 4;
    public static final int GL_GLYPH_HORIZONTAL_BEARING_Y_BIT_NV = 8;
    public static final int GL_GLYPH_HORIZONTAL_BEARING_ADVANCE_BIT_NV = 16;
    public static final int GL_GLYPH_VERTICAL_BEARING_X_BIT_NV = 32;
    public static final int GL_GLYPH_VERTICAL_BEARING_Y_BIT_NV = 64;
    public static final int GL_GLYPH_VERTICAL_BEARING_ADVANCE_BIT_NV = 128;
    public static final int GL_GLYPH_HAS_KERNING_NV = 256;
    public static final int GL_FONT_X_MIN_BOUNDS_NV = 65536;
    public static final int GL_FONT_Y_MIN_BOUNDS_NV = 131072;
    public static final int GL_FONT_X_MAX_BOUNDS_NV = 262144;
    public static final int GL_FONT_Y_MAX_BOUNDS_NV = 524288;
    public static final int GL_FONT_UNITS_PER_EM_NV = 0x100000;
    public static final int GL_FONT_ASCENDER_NV = 0x200000;
    public static final int GL_FONT_DESCENDER_NV = 0x400000;
    public static final int GL_FONT_HEIGHT_NV = 0x800000;
    public static final int GL_FONT_MAX_ADVANCE_WIDTH_NV = 0x1000000;
    public static final int GL_FONT_MAX_ADVANCE_HEIGHT_NV = 0x2000000;
    public static final int GL_FONT_UNDERLINE_POSITION_NV = 0x4000000;
    public static final int GL_FONT_UNDERLINE_THICKNESS_NV = 0x8000000;
    public static final int GL_FONT_HAS_KERNING_NV = 0x10000000;
    public static final int GL_ACCUM_ADJACENT_PAIRS_NV = 37037;
    public static final int GL_ADJACENT_PAIRS_NV = 37038;
    public static final int GL_FIRST_TO_REST_NV = 37039;
    public static final int GL_PATH_GEN_MODE_NV = 37040;
    public static final int GL_PATH_GEN_COEFF_NV = 37041;
    public static final int GL_PATH_GEN_COLOR_FORMAT_NV = 37042;
    public static final int GL_PATH_GEN_COMPONENTS_NV = 37043;

    private NVPathRendering() {
    }

    public static void glPathCommandsNV(int n, ByteBuffer byteBuffer, int n2, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathCommandsNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        NVPathRendering.nglPathCommandsNV(n, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), byteBuffer2.remaining(), n2, MemoryUtil.getAddress(byteBuffer2), l);
    }

    static native void nglPathCommandsNV(int var0, int var1, long var2, int var4, int var5, long var6, long var8);

    public static void glPathCoordsNV(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathCoordsNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        NVPathRendering.nglPathCoordsNV(n, byteBuffer.remaining(), n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglPathCoordsNV(int var0, int var1, int var2, long var3, long var5);

    public static void glPathSubCommandsNV(int n, int n2, int n3, ByteBuffer byteBuffer, int n4, ByteBuffer byteBuffer2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathSubCommandsNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        NVPathRendering.nglPathSubCommandsNV(n, n2, n3, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), byteBuffer2.remaining(), n4, MemoryUtil.getAddress(byteBuffer2), l);
    }

    static native void nglPathSubCommandsNV(int var0, int var1, int var2, int var3, long var4, int var6, int var7, long var8, long var10);

    public static void glPathSubCoordsNV(int n, int n2, int n3, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathSubCoordsNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        NVPathRendering.nglPathSubCoordsNV(n, n2, byteBuffer.remaining(), n3, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglPathSubCoordsNV(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glPathStringNV(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathStringNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        NVPathRendering.nglPathStringNV(n, n2, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglPathStringNV(int var0, int var1, int var2, long var3, long var5);

    public static void glPathGlyphsNV(int n, int n2, ByteBuffer byteBuffer, int n3, int n4, ByteBuffer byteBuffer2, int n5, int n6, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathGlyphsNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        BufferChecks.checkDirect(byteBuffer2);
        NVPathRendering.nglPathGlyphsNV(n, n2, MemoryUtil.getAddress(byteBuffer), n3, byteBuffer2.remaining() / GLChecks.calculateBytesPerCharCode(n4), n4, MemoryUtil.getAddress(byteBuffer2), n5, n6, f, l);
    }

    static native void nglPathGlyphsNV(int var0, int var1, long var2, int var4, int var5, int var6, long var7, int var9, int var10, float var11, long var12);

    public static void glPathGlyphRangeNV(int n, int n2, ByteBuffer byteBuffer, int n3, int n4, int n5, int n6, int n7, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathGlyphRangeNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        NVPathRendering.nglPathGlyphRangeNV(n, n2, MemoryUtil.getAddress(byteBuffer), n3, n4, n5, n6, n7, f, l);
    }

    static native void nglPathGlyphRangeNV(int var0, int var1, long var2, int var4, int var5, int var6, int var7, int var8, float var9, long var10);

    public static void glWeightPathsNV(int n, IntBuffer intBuffer, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glWeightPathsNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        BufferChecks.checkBuffer(floatBuffer, intBuffer.remaining());
        NVPathRendering.nglWeightPathsNV(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglWeightPathsNV(int var0, int var1, long var2, long var4, long var6);

    public static void glCopyPathNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCopyPathNV;
        BufferChecks.checkFunctionAddress(l);
        NVPathRendering.nglCopyPathNV(n, n2, l);
    }

    static native void nglCopyPathNV(int var0, int var1, long var2);

    public static void glInterpolatePathsNV(int n, int n2, int n3, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInterpolatePathsNV;
        BufferChecks.checkFunctionAddress(l);
        NVPathRendering.nglInterpolatePathsNV(n, n2, n3, f, l);
    }

    static native void nglInterpolatePathsNV(int var0, int var1, int var2, float var3, long var4);

    public static void glTransformPathNV(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTransformPathNV;
        BufferChecks.checkFunctionAddress(l);
        if (floatBuffer != null) {
            BufferChecks.checkBuffer(floatBuffer, GLChecks.calculateTransformPathValues(n3));
        }
        NVPathRendering.nglTransformPathNV(n, n2, n3, MemoryUtil.getAddressSafe(floatBuffer), l);
    }

    static native void nglTransformPathNV(int var0, int var1, int var2, long var3, long var5);

    public static void glPathParameterNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVPathRendering.nglPathParameterivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglPathParameterivNV(int var0, int var1, long var2, long var4);

    public static void glPathParameteriNV(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathParameteriNV;
        BufferChecks.checkFunctionAddress(l);
        NVPathRendering.nglPathParameteriNV(n, n2, n3, l);
    }

    static native void nglPathParameteriNV(int var0, int var1, int var2, long var3);

    public static void glPathParameterNV(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVPathRendering.nglPathParameterfvNV(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglPathParameterfvNV(int var0, int var1, long var2, long var4);

    public static void glPathParameterfNV(int n, int n2, float f) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathParameterfNV;
        BufferChecks.checkFunctionAddress(l);
        NVPathRendering.nglPathParameterfNV(n, n2, f, l);
    }

    static native void nglPathParameterfNV(int var0, int var1, float var2, long var3);

    public static void glPathDashArrayNV(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathDashArrayNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        NVPathRendering.nglPathDashArrayNV(n, floatBuffer.remaining(), MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglPathDashArrayNV(int var0, int var1, long var2, long var4);

    public static int glGenPathsNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGenPathsNV;
        BufferChecks.checkFunctionAddress(l);
        n = NVPathRendering.nglGenPathsNV(n, l);
        return n;
    }

    static native int nglGenPathsNV(int var0, long var1);

    public static void glDeletePathsNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDeletePathsNV;
        BufferChecks.checkFunctionAddress(l);
        NVPathRendering.nglDeletePathsNV(n, n2, l);
    }

    static native void nglDeletePathsNV(int var0, int var1, long var2);

    public static boolean glIsPathNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsPathNV;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = NVPathRendering.nglIsPathNV(n, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsPathNV(int var0, long var1);

    public static void glPathStencilFuncNV(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathStencilFuncNV;
        BufferChecks.checkFunctionAddress(l);
        NVPathRendering.nglPathStencilFuncNV(n, n2, n3, l);
    }

    static native void nglPathStencilFuncNV(int var0, int var1, int var2, long var3);

    public static void glPathStencilDepthOffsetNV(float f, int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathStencilDepthOffsetNV;
        BufferChecks.checkFunctionAddress(l);
        NVPathRendering.nglPathStencilDepthOffsetNV(f, n, l);
    }

    static native void nglPathStencilDepthOffsetNV(float var0, int var1, long var2);

    public static void glStencilFillPathNV(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glStencilFillPathNV;
        BufferChecks.checkFunctionAddress(l);
        NVPathRendering.nglStencilFillPathNV(n, n2, n3, l);
    }

    static native void nglStencilFillPathNV(int var0, int var1, int var2, long var3);

    public static void glStencilStrokePathNV(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glStencilStrokePathNV;
        BufferChecks.checkFunctionAddress(l);
        NVPathRendering.nglStencilStrokePathNV(n, n2, n3, l);
    }

    static native void nglStencilStrokePathNV(int var0, int var1, int var2, long var3);

    public static void glStencilFillPathInstancedNV(int n, ByteBuffer byteBuffer, int n2, int n3, int n4, int n5, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glStencilFillPathInstancedNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        if (floatBuffer != null) {
            BufferChecks.checkBuffer(floatBuffer, GLChecks.calculateTransformPathValues(n5));
        }
        NVPathRendering.nglStencilFillPathInstancedNV(byteBuffer.remaining() / GLChecks.calculateBytesPerPathName(n), n, MemoryUtil.getAddress(byteBuffer), n2, n3, n4, n5, MemoryUtil.getAddressSafe(floatBuffer), l);
    }

    static native void nglStencilFillPathInstancedNV(int var0, int var1, long var2, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glStencilStrokePathInstancedNV(int n, ByteBuffer byteBuffer, int n2, int n3, int n4, int n5, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glStencilStrokePathInstancedNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        if (floatBuffer != null) {
            BufferChecks.checkBuffer(floatBuffer, GLChecks.calculateTransformPathValues(n5));
        }
        NVPathRendering.nglStencilStrokePathInstancedNV(byteBuffer.remaining() / GLChecks.calculateBytesPerPathName(n), n, MemoryUtil.getAddress(byteBuffer), n2, n3, n4, n5, MemoryUtil.getAddressSafe(floatBuffer), l);
    }

    static native void nglStencilStrokePathInstancedNV(int var0, int var1, long var2, int var4, int var5, int var6, int var7, long var8, long var10);

    public static void glPathCoverDepthFuncNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathCoverDepthFuncNV;
        BufferChecks.checkFunctionAddress(l);
        NVPathRendering.nglPathCoverDepthFuncNV(n, l);
    }

    static native void nglPathCoverDepthFuncNV(int var0, long var1);

    public static void glPathColorGenNV(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathColorGenNV;
        BufferChecks.checkFunctionAddress(l);
        if (floatBuffer != null) {
            BufferChecks.checkBuffer(floatBuffer, GLChecks.calculatePathColorGenCoeffsCount(n2, n3));
        }
        NVPathRendering.nglPathColorGenNV(n, n2, n3, MemoryUtil.getAddressSafe(floatBuffer), l);
    }

    static native void nglPathColorGenNV(int var0, int var1, int var2, long var3, long var5);

    public static void glPathTexGenNV(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathTexGenNV;
        BufferChecks.checkFunctionAddress(l);
        if (floatBuffer != null) {
            BufferChecks.checkDirect(floatBuffer);
        }
        NVPathRendering.nglPathTexGenNV(n, n2, GLChecks.calculatePathTextGenCoeffsPerComponent(floatBuffer, n2), MemoryUtil.getAddressSafe(floatBuffer), l);
    }

    static native void nglPathTexGenNV(int var0, int var1, int var2, long var3, long var5);

    public static void glPathFogGenNV(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPathFogGenNV;
        BufferChecks.checkFunctionAddress(l);
        NVPathRendering.nglPathFogGenNV(n, l);
    }

    static native void nglPathFogGenNV(int var0, long var1);

    public static void glCoverFillPathNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCoverFillPathNV;
        BufferChecks.checkFunctionAddress(l);
        NVPathRendering.nglCoverFillPathNV(n, n2, l);
    }

    static native void nglCoverFillPathNV(int var0, int var1, long var2);

    public static void glCoverStrokePathNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCoverStrokePathNV;
        BufferChecks.checkFunctionAddress(l);
        NVPathRendering.nglCoverStrokePathNV(n, n2, l);
    }

    static native void nglCoverStrokePathNV(int var0, int var1, long var2);

    public static void glCoverFillPathInstancedNV(int n, ByteBuffer byteBuffer, int n2, int n3, int n4, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCoverFillPathInstancedNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        if (floatBuffer != null) {
            BufferChecks.checkBuffer(floatBuffer, GLChecks.calculateTransformPathValues(n4));
        }
        NVPathRendering.nglCoverFillPathInstancedNV(byteBuffer.remaining() / GLChecks.calculateBytesPerPathName(n), n, MemoryUtil.getAddress(byteBuffer), n2, n3, n4, MemoryUtil.getAddressSafe(floatBuffer), l);
    }

    static native void nglCoverFillPathInstancedNV(int var0, int var1, long var2, int var4, int var5, int var6, long var7, long var9);

    public static void glCoverStrokePathInstancedNV(int n, ByteBuffer byteBuffer, int n2, int n3, int n4, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCoverStrokePathInstancedNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        if (floatBuffer != null) {
            BufferChecks.checkBuffer(floatBuffer, GLChecks.calculateTransformPathValues(n4));
        }
        NVPathRendering.nglCoverStrokePathInstancedNV(byteBuffer.remaining() / GLChecks.calculateBytesPerPathName(n), n, MemoryUtil.getAddress(byteBuffer), n2, n3, n4, MemoryUtil.getAddressSafe(floatBuffer), l);
    }

    static native void nglCoverStrokePathInstancedNV(int var0, int var1, long var2, int var4, int var5, int var6, long var7, long var9);

    public static void glGetPathParameterNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPathParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 4);
        NVPathRendering.nglGetPathParameterivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetPathParameterivNV(int var0, int var1, long var2, long var4);

    public static int glGetPathParameteriNV(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetPathParameterivNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        NVPathRendering.nglGetPathParameterivNV(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetPathParameterfvNV(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPathParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 4);
        NVPathRendering.nglGetPathParameterfvNV(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetPathParameterfvNV(int var0, int var1, long var2, long var4);

    public static float glGetPathParameterfNV(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetPathParameterfvNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        NVPathRendering.nglGetPathParameterfvNV(n, n2, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetPathCommandsNV(int n, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPathCommandsNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        NVPathRendering.nglGetPathCommandsNV(n, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetPathCommandsNV(int var0, long var1, long var3);

    public static void glGetPathCoordsNV(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPathCoordsNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        NVPathRendering.nglGetPathCoordsNV(n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetPathCoordsNV(int var0, long var1, long var3);

    public static void glGetPathDashArrayNV(int n, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPathDashArrayNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(floatBuffer);
        NVPathRendering.nglGetPathDashArrayNV(n, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetPathDashArrayNV(int var0, long var1, long var3);

    public static void glGetPathMetricsNV(int n, int n2, ByteBuffer byteBuffer, int n3, int n4, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPathMetricsNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkBuffer(floatBuffer, GLChecks.calculateMetricsSize(n, n4));
        NVPathRendering.nglGetPathMetricsNV(n, byteBuffer.remaining() / GLChecks.calculateBytesPerPathName(n2), n2, MemoryUtil.getAddress(byteBuffer), n3, n4, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetPathMetricsNV(int var0, int var1, int var2, long var3, int var5, int var6, long var7, long var9);

    public static void glGetPathMetricRangeNV(int n, int n2, int n3, int n4, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPathMetricRangeNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, GLChecks.calculateMetricsSize(n, n4));
        NVPathRendering.nglGetPathMetricRangeNV(n, n2, n3, n4, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetPathMetricRangeNV(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glGetPathSpacingNV(int n, int n2, ByteBuffer byteBuffer, int n3, float f, float f2, int n4, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPathSpacingNV;
        BufferChecks.checkFunctionAddress(l);
        int n5 = byteBuffer.remaining() / GLChecks.calculateBytesPerPathName(n2);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkBuffer(floatBuffer, n5 - 1);
        NVPathRendering.nglGetPathSpacingNV(n, n5, n2, MemoryUtil.getAddress(byteBuffer), n3, f, f2, n4, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetPathSpacingNV(int var0, int var1, int var2, long var3, int var5, float var6, float var7, int var8, long var9, long var11);

    public static void glGetPathColorGenNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPathColorGenivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 16);
        NVPathRendering.nglGetPathColorGenivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetPathColorGenivNV(int var0, int var1, long var2, long var4);

    public static int glGetPathColorGeniNV(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetPathColorGenivNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        NVPathRendering.nglGetPathColorGenivNV(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetPathColorGenNV(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPathColorGenfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 16);
        NVPathRendering.nglGetPathColorGenfvNV(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetPathColorGenfvNV(int var0, int var1, long var2, long var4);

    public static float glGetPathColorGenfNV(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetPathColorGenfvNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        NVPathRendering.nglGetPathColorGenfvNV(n, n2, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static void glGetPathTexGenNV(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPathTexGenivNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 16);
        NVPathRendering.nglGetPathTexGenivNV(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetPathTexGenivNV(int var0, int var1, long var2, long var4);

    public static int glGetPathTexGeniNV(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetPathTexGenivNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        NVPathRendering.nglGetPathTexGenivNV(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetPathTexGenNV(int n, int n2, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPathTexGenfvNV;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, 16);
        NVPathRendering.nglGetPathTexGenfvNV(n, n2, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglGetPathTexGenfvNV(int var0, int var1, long var2, long var4);

    public static float glGetPathTexGenfNV(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetPathTexGenfvNV;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferFloat((ContextCapabilities)object);
        NVPathRendering.nglGetPathTexGenfvNV(n, n2, MemoryUtil.getAddress((FloatBuffer)object), l);
        return ((FloatBuffer)object).get(0);
    }

    public static boolean glIsPointInFillPathNV(int n, int n2, float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsPointInFillPathNV;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = NVPathRendering.nglIsPointInFillPathNV(n, n2, f, f2, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsPointInFillPathNV(int var0, int var1, float var2, float var3, long var4);

    public static boolean glIsPointInStrokePathNV(int n, float f, float f2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glIsPointInStrokePathNV;
        BufferChecks.checkFunctionAddress(l);
        boolean bl = NVPathRendering.nglIsPointInStrokePathNV(n, f, f2, l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglIsPointInStrokePathNV(int var0, float var1, float var2, long var3);

    public static float glGetPathLengthNV(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetPathLengthNV;
        BufferChecks.checkFunctionAddress(l);
        float f = NVPathRendering.nglGetPathLengthNV(n, n2, n3, l);
        return f;
    }

    static native float nglGetPathLengthNV(int var0, int var1, int var2, long var3);

    public static boolean glPointAlongPathNV(int n, int n2, int n3, float f, FloatBuffer floatBuffer, FloatBuffer floatBuffer2, FloatBuffer floatBuffer3, FloatBuffer floatBuffer4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPointAlongPathNV;
        BufferChecks.checkFunctionAddress(l);
        if (floatBuffer != null) {
            BufferChecks.checkBuffer(floatBuffer, 1);
        }
        if (floatBuffer2 != null) {
            BufferChecks.checkBuffer(floatBuffer2, 1);
        }
        if (floatBuffer3 != null) {
            BufferChecks.checkBuffer(floatBuffer3, 1);
        }
        if (floatBuffer4 != null) {
            BufferChecks.checkBuffer(floatBuffer4, 1);
        }
        boolean bl = NVPathRendering.nglPointAlongPathNV(n, n2, n3, f, MemoryUtil.getAddressSafe(floatBuffer), MemoryUtil.getAddressSafe(floatBuffer2), MemoryUtil.getAddressSafe(floatBuffer3), MemoryUtil.getAddressSafe(floatBuffer4), l);
        n = bl ? 1 : 0;
        return bl;
    }

    static native boolean nglPointAlongPathNV(int var0, int var1, int var2, float var3, long var4, long var6, long var8, long var10, long var12);
}
