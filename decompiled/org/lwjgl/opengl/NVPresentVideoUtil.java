/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import java.nio.LongBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.opengl.ContextGL;
import org.lwjgl.opengl.GLContext;

public final class NVPresentVideoUtil {
    private NVPresentVideoUtil() {
    }

    private static void checkExtension() {
        if (LWJGLUtil.CHECKS && !GLContext.getCapabilities().GL_NV_present_video) {
            throw new IllegalStateException("NV_present_video is not supported");
        }
    }

    private static ByteBuffer getPeerInfo() {
        return ContextGL.getCurrentContext().getPeerInfo().getHandle();
    }

    public static int glEnumerateVideoDevicesNV(LongBuffer longBuffer) {
        NVPresentVideoUtil.checkExtension();
        if (longBuffer != null) {
            BufferChecks.checkBuffer(longBuffer, 1);
        }
        LongBuffer longBuffer2 = longBuffer;
        return NVPresentVideoUtil.nglEnumerateVideoDevicesNV(NVPresentVideoUtil.getPeerInfo(), longBuffer2, longBuffer2 == null ? 0 : longBuffer.position());
    }

    private static native int nglEnumerateVideoDevicesNV(ByteBuffer var0, LongBuffer var1, int var2);

    public static boolean glBindVideoDeviceNV(int n, long l, IntBuffer intBuffer) {
        NVPresentVideoUtil.checkExtension();
        if (intBuffer != null) {
            BufferChecks.checkNullTerminated(intBuffer);
        }
        IntBuffer intBuffer2 = intBuffer;
        return NVPresentVideoUtil.nglBindVideoDeviceNV(NVPresentVideoUtil.getPeerInfo(), n, l, intBuffer2, intBuffer2 == null ? 0 : intBuffer.position());
    }

    private static native boolean nglBindVideoDeviceNV(ByteBuffer var0, int var1, long var2, IntBuffer var4, int var5);

    public static boolean glQueryContextNV(int n, IntBuffer intBuffer) {
        NVPresentVideoUtil.checkExtension();
        BufferChecks.checkBuffer(intBuffer, 1);
        ContextGL contextGL = ContextGL.getCurrentContext();
        IntBuffer intBuffer2 = intBuffer;
        return NVPresentVideoUtil.nglQueryContextNV(contextGL.getPeerInfo().getHandle(), contextGL.getHandle(), n, intBuffer2, intBuffer2.position());
    }

    private static native boolean nglQueryContextNV(ByteBuffer var0, ByteBuffer var1, int var2, IntBuffer var3, int var4);
}
