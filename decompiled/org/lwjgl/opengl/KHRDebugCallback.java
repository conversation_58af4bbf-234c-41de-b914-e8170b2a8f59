/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.PointerWrapperAbstract;

public final class KHRDebugCallback
extends PointerWrapperAbstract {
    private static final int GL_DEBUG_SEVERITY_HIGH = 37190;
    private static final int GL_DEBUG_SEVERITY_MEDIUM = 37191;
    private static final int GL_DEBUG_SEVERITY_LOW = 37192;
    private static final int GL_DEBUG_SEVERITY_NOTIFICATION = 33387;
    private static final int GL_DEBUG_SOURCE_API = 33350;
    private static final int GL_DEBUG_SOURCE_WINDOW_SYSTEM = 33351;
    private static final int GL_DEBUG_SOURCE_SHADER_COMPILER = 33352;
    private static final int GL_DEBUG_SOURCE_THIRD_PARTY = 33353;
    private static final int GL_DEBUG_SOURCE_APPLICATION = 33354;
    private static final int GL_DEBUG_SOURCE_OTHER = 33355;
    private static final int GL_DEBUG_TYPE_ERROR = 33356;
    private static final int GL_DEBUG_TYPE_DEPRECATED_BEHAVIOR = 33357;
    private static final int GL_DEBUG_TYPE_UNDEFINED_BEHAVIOR = 33358;
    private static final int GL_DEBUG_TYPE_PORTABILITY = 33359;
    private static final int GL_DEBUG_TYPE_PERFORMANCE = 33360;
    private static final int GL_DEBUG_TYPE_OTHER = 33361;
    private static final int GL_DEBUG_TYPE_MARKER = 33384;
    private static final long CALLBACK_POINTER;
    private final Handler handler;

    public KHRDebugCallback() {
        this(new Handler(){

            public void handleMessage(int n, int n2, int n3, int n4, String string) {
                String string2;
                System.err.println("[LWJGL] KHR_debug message");
                System.err.println("\tID: " + n3);
                switch (n) {
                    case 33350: {
                        string2 = "API";
                        break;
                    }
                    case 33351: {
                        string2 = "WINDOW SYSTEM";
                        break;
                    }
                    case 33352: {
                        string2 = "SHADER COMPILER";
                        break;
                    }
                    case 33353: {
                        string2 = "THIRD PARTY";
                        break;
                    }
                    case 33354: {
                        string2 = "APPLICATION";
                        break;
                    }
                    case 33355: {
                        string2 = "OTHER";
                        break;
                    }
                    default: {
                        string2 = this.printUnknownToken(n);
                    }
                }
                System.err.println("\tSource: " + string2);
                switch (n2) {
                    case 33356: {
                        string2 = "ERROR";
                        break;
                    }
                    case 33357: {
                        string2 = "DEPRECATED BEHAVIOR";
                        break;
                    }
                    case 33358: {
                        string2 = "UNDEFINED BEHAVIOR";
                        break;
                    }
                    case 33359: {
                        string2 = "PORTABILITY";
                        break;
                    }
                    case 33360: {
                        string2 = "PERFORMANCE";
                        break;
                    }
                    case 33361: {
                        string2 = "OTHER";
                        break;
                    }
                    case 33384: {
                        string2 = "MARKER";
                        break;
                    }
                    default: {
                        string2 = this.printUnknownToken(n2);
                    }
                }
                System.err.println("\tType: " + string2);
                switch (n4) {
                    case 37190: {
                        string2 = "HIGH";
                        break;
                    }
                    case 37191: {
                        string2 = "MEDIUM";
                        break;
                    }
                    case 37192: {
                        string2 = "LOW";
                        break;
                    }
                    case 33387: {
                        string2 = "NOTIFICATION";
                        break;
                    }
                    default: {
                        string2 = this.printUnknownToken(n4);
                    }
                }
                System.err.println("\tSeverity: " + string2);
                System.err.println("\tMessage: " + string);
            }

            private String printUnknownToken(int n) {
                return "Unknown (0x" + Integer.toHexString(n).toUpperCase() + ")";
            }
        });
    }

    public KHRDebugCallback(Handler handler) {
        super(CALLBACK_POINTER);
        this.handler = handler;
    }

    final Handler getHandler() {
        return this.handler;
    }

    static {
        long l = 0L;
        try {
            l = (Long)Class.forName("org.lwjgl.opengl.CallbackUtil").getDeclaredMethod("getDebugCallbackKHR", new Class[0]).invoke(null, new Object[0]);
        }
        catch (Exception exception) {}
        CALLBACK_POINTER = l;
    }

    public static interface Handler {
        public void handleMessage(int var1, int var2, int var3, int var4, String var5);
    }
}
