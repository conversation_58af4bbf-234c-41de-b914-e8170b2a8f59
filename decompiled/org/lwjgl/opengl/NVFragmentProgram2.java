/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

public final class NVFragmentProgram2 {
    public static final int GL_MAX_PROGRAM_EXEC_INSTRUCTIONS_NV = 35060;
    public static final int GL_MAX_PROGRAM_CALL_DEPTH_NV = 35061;
    public static final int GL_MAX_PROGRAM_IF_DEPTH_NV = 35062;
    public static final int GL_MAX_PROGRAM_LOOP_DEPTH_NV = 35063;
    public static final int GL_MAX_PROGRAM_LOOP_COUNT_NV = 35064;

    private NVFragmentProgram2() {
    }
}
