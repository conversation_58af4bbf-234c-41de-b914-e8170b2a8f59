/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.FloatBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class EXTGpuProgramParameters {
    private EXTGpuProgramParameters() {
    }

    public static void glProgramEnvParameters4EXT(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramEnvParameters4fvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, n3 << 2);
        EXTGpuProgramParameters.nglProgramEnvParameters4fvEXT(n, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramEnvParameters4fvEXT(int var0, int var1, int var2, long var3, long var5);

    public static void glProgramLocalParameters4EXT(int n, int n2, int n3, FloatBuffer floatBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramLocalParameters4fvEXT;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(floatBuffer, n3 << 2);
        EXTGpuProgramParameters.nglProgramLocalParameters4fvEXT(n, n2, n3, MemoryUtil.getAddress(floatBuffer), l);
    }

    static native void nglProgramLocalParameters4fvEXT(int var0, int var1, int var2, long var3, long var5);
}
