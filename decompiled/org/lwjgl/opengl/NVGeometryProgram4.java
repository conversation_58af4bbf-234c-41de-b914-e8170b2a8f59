/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.EXTGeometryShader4;
import org.lwjgl.opengl.GLContext;

public final class NVGeometryProgram4 {
    public static final int GL_GEOMETRY_PROGRAM_NV = 35878;
    public static final int GL_MAX_PROGRAM_OUTPUT_VERTICES_NV = 35879;
    public static final int GL_MAX_PROGRAM_TOTAL_OUTPUT_COMPONENTS_NV = 35880;

    private NVGeometryProgram4() {
    }

    public static void glProgramVertexLimitNV(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glProgramVertexLimitNV;
        BufferChecks.checkFunctionAddress(l);
        NVGeometryProgram4.nglProgramVertexLimitNV(n, n2, l);
    }

    static native void nglProgramVertexLimitNV(int var0, int var1, long var2);

    public static void glFramebufferTextureEXT(int n, int n2, int n3, int n4) {
        EXTGeometryShader4.glFramebufferTextureEXT(n, n2, n3, n4);
    }

    public static void glFramebufferTextureLayerEXT(int n, int n2, int n3, int n4, int n5) {
        EXTGeometryShader4.glFramebufferTextureLayerEXT(n, n2, n3, n4, n5);
    }

    public static void glFramebufferTextureFaceEXT(int n, int n2, int n3, int n4, int n5) {
        EXTGeometryShader4.glFramebufferTextureFaceEXT(n, n2, n3, n4, n5);
    }
}
