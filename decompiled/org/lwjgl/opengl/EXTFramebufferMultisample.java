/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class EXTFramebufferMultisample {
    public static final int GL_RENDERBUFFER_SAMPLES_EXT = 36011;
    public static final int GL_FRAMEBUFFER_INCOMPLETE_MULTISAMPLE_EXT = 36182;
    public static final int GL_MAX_SAMPLES_EXT = 36183;

    private EXTFramebufferMultisample() {
    }

    public static void glRenderbufferStorageMultisampleEXT(int n, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glRenderbufferStorageMultisampleEXT;
        BufferChecks.checkFunctionAddress(l);
        EXTFramebufferMultisample.nglRenderbufferStorageMultisampleEXT(n, n2, n3, n4, n5, l);
    }

    static native void nglRenderbufferStorageMultisampleEXT(int var0, int var1, int var2, int var3, int var4, long var5);
}
