/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferChecks;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLContext;

public final class NVDepthBufferFloat {
    public static final int GL_DEPTH_COMPONENT32F_NV = 36267;
    public static final int GL_DEPTH32F_STENCIL8_NV = 36268;
    public static final int GL_FLOAT_32_UNSIGNED_INT_24_8_REV_NV = 36269;
    public static final int GL_DEPTH_BUFFER_FLOAT_MODE_NV = 36271;

    private NVDepthBufferFloat() {
    }

    public static void glDepthRangedNV(double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDepthRangedNV;
        BufferChecks.checkFunctionAddress(l);
        NVDepthBufferFloat.nglDepthRangedNV(d, d2, l);
    }

    static native void nglDepthRangedNV(double var0, double var2, long var4);

    public static void glClearDepthdNV(double d) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearDepthdNV;
        BufferChecks.checkFunctionAddress(l);
        NVDepthBufferFloat.nglClearDepthdNV(d, l);
    }

    static native void nglClearDepthdNV(double var0, long var2);

    public static void glDepthBoundsdNV(double d, double d2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDepthBoundsdNV;
        BufferChecks.checkFunctionAddress(l);
        NVDepthBufferFloat.nglDepthBoundsdNV(d, d2, l);
    }

    static native void nglDepthBoundsdNV(double var0, double var2, long var4);
}
