/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.security.PrivilegedAction;

/*
 * This class specifies class file version 49.0 but uses Java 6 signatures.  Assumed Java 6.
 */
static final class Display.8
implements PrivilegedAction<String> {
    final /* synthetic */ String val$property_name;

    Display.8(String string) {
        this.val$property_name = string;
    }

    @Override
    public final String run() {
        return System.getProperty(this.val$property_name);
    }
}
