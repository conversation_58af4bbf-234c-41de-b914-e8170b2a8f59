/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.StateTracker;

public final class ARBMatrixPalette {
    public static final int GL_MATRIX_PALETTE_ARB = 34880;
    public static final int GL_MAX_MATRIX_PALETTE_STACK_DEPTH_ARB = 34881;
    public static final int GL_MAX_PALETTE_MATRICES_ARB = 34882;
    public static final int GL_CURRENT_PALETTE_MATRIX_ARB = 34883;
    public static final int GL_MATRIX_INDEX_ARRAY_ARB = 34884;
    public static final int GL_CURRENT_MATRIX_INDEX_ARB = 34885;
    public static final int GL_MATRIX_INDEX_ARRAY_SIZE_ARB = 34886;
    public static final int GL_MATRIX_INDEX_ARRAY_TYPE_ARB = 34887;
    public static final int GL_MATRIX_INDEX_ARRAY_STRIDE_ARB = 34888;
    public static final int GL_MATRIX_INDEX_ARRAY_POINTER_ARB = 34889;

    private ARBMatrixPalette() {
    }

    public static void glCurrentPaletteMatrixARB(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCurrentPaletteMatrixARB;
        BufferChecks.checkFunctionAddress(l);
        ARBMatrixPalette.nglCurrentPaletteMatrixARB(n, l);
    }

    static native void nglCurrentPaletteMatrixARB(int var0, long var1);

    public static void glMatrixIndexPointerARB(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMatrixIndexPointerARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(byteBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).ARB_matrix_palette_glMatrixIndexPointerARB_pPointer = byteBuffer;
        }
        ARBMatrixPalette.nglMatrixIndexPointerARB(n, 5121, n2, MemoryUtil.getAddress(byteBuffer), l);
    }

    public static void glMatrixIndexPointerARB(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMatrixIndexPointerARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(intBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).ARB_matrix_palette_glMatrixIndexPointerARB_pPointer = intBuffer;
        }
        ARBMatrixPalette.nglMatrixIndexPointerARB(n, 5125, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    public static void glMatrixIndexPointerARB(int n, int n2, ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMatrixIndexPointerARB;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureArrayVBOdisabled(contextCapabilities);
        BufferChecks.checkDirect(shortBuffer);
        if (LWJGLUtil.CHECKS) {
            StateTracker.getReferences((ContextCapabilities)contextCapabilities).ARB_matrix_palette_glMatrixIndexPointerARB_pPointer = shortBuffer;
        }
        ARBMatrixPalette.nglMatrixIndexPointerARB(n, 5123, n2, MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglMatrixIndexPointerARB(int var0, int var1, int var2, long var3, long var5);

    public static void glMatrixIndexPointerARB(int n, int n2, int n3, long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMatrixIndexPointerARB;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureArrayVBOenabled(contextCapabilities);
        ARBMatrixPalette.nglMatrixIndexPointerARBBO(n, n2, n3, l, l2);
    }

    static native void nglMatrixIndexPointerARBBO(int var0, int var1, int var2, long var3, long var5);

    public static void glMatrixIndexuARB(ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMatrixIndexubvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        ARBMatrixPalette.nglMatrixIndexubvARB(byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglMatrixIndexubvARB(int var0, long var1, long var3);

    public static void glMatrixIndexuARB(ShortBuffer shortBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMatrixIndexusvARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(shortBuffer);
        ARBMatrixPalette.nglMatrixIndexusvARB(shortBuffer.remaining(), MemoryUtil.getAddress(shortBuffer), l);
    }

    static native void nglMatrixIndexusvARB(int var0, long var1, long var3);

    public static void glMatrixIndexuARB(IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMatrixIndexuivARB;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        ARBMatrixPalette.nglMatrixIndexuivARB(intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglMatrixIndexuivARB(int var0, long var1, long var3);
}
