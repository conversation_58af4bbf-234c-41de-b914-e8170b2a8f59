/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;

class EventQueue {
    private static final int QUEUE_SIZE = 200;
    private final int event_size;
    private final ByteBuffer queue;

    protected EventQueue(int n) {
        this.event_size = n;
        this.queue = ByteBuffer.allocate(n * 200);
    }

    protected synchronized void clearEvents() {
        this.queue.clear();
    }

    public synchronized void copyEvents(ByteBuffer byteBuffer) {
        this.queue.flip();
        int n = this.queue.limit();
        if (byteBuffer.remaining() < this.queue.remaining()) {
            this.queue.limit(byteBuffer.remaining() + this.queue.position());
        }
        byteBuffer.put(this.queue);
        this.queue.limit(n);
        this.queue.compact();
    }

    public synchronized boolean putEvent(ByteBuffer byteBuffer) {
        if (byteBuffer.remaining() != this.event_size) {
            throw new IllegalArgumentException("Internal error: event size " + this.event_size + " does not equal the given event size " + byteBuffer.remaining());
        }
        if (this.queue.remaining() >= byteBuffer.remaining()) {
            this.queue.put(byteBuffer);
            return true;
        }
        return false;
    }
}
