/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.awt.Canvas;
import java.awt.GraphicsConfiguration;
import java.awt.GraphicsDevice;
import org.lwjgl.LWJGLException;
import org.lwjgl.opengl.AWTCanvasImplementation;
import org.lwjgl.opengl.ContextAttribs;
import org.lwjgl.opengl.MacOSXAWTGLCanvasPeerInfo;
import org.lwjgl.opengl.PeerInfo;
import org.lwjgl.opengl.PixelFormat;

final class MacOSXCanvasImplementation
implements AWTCanvasImplementation {
    MacOSXCanvasImplementation() {
    }

    public final PeerInfo createPeerInfo(Canvas canvas, PixelFormat pixelFormat, ContextAttribs contextAttribs) {
        try {
            return new MacOSXAWTGLCanvasPeerInfo(canvas, pixelFormat, contextAttribs, true);
        }
        catch (LWJGLException lWJGLException) {
            return new MacOSXAWTGLCanvasPeerInfo(canvas, pixelFormat, contextAttribs, false);
        }
    }

    public final GraphicsConfiguration findConfiguration(GraphicsDevice graphicsDevice, PixelFormat pixelFormat) {
        return null;
    }
}
