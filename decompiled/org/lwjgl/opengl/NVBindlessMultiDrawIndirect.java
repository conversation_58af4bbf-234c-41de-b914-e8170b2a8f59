/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;

public final class NVBindlessMultiDrawIndirect {
    private NVBindlessMultiDrawIndirect() {
    }

    public static void glMultiDrawArraysIndirectBindlessNV(int n, ByteBuffer byteBuffer, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiDrawArraysIndirectBindlessNV;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureIndirectBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, (n3 == 0 ? 20 + n4 * 24 : n3) * n2);
        NVBindlessMultiDrawIndirect.nglMultiDrawArraysIndirectBindlessNV(n, MemoryUtil.getAddress(byteBuffer), n2, n3, n4, l);
    }

    static native void nglMultiDrawArraysIndirectBindlessNV(int var0, long var1, int var3, int var4, int var5, long var6);

    public static void glMultiDrawArraysIndirectBindlessNV(int n, long l, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMultiDrawArraysIndirectBindlessNV;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureIndirectBOenabled(contextCapabilities);
        NVBindlessMultiDrawIndirect.nglMultiDrawArraysIndirectBindlessNVBO(n, l, n2, n3, n4, l2);
    }

    static native void nglMultiDrawArraysIndirectBindlessNVBO(int var0, long var1, int var3, int var4, int var5, long var6);

    public static void glMultiDrawElementsIndirectBindlessNV(int n, int n2, ByteBuffer byteBuffer, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiDrawElementsIndirectBindlessNV;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureIndirectBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, (n4 == 0 ? 48 + n5 * 24 : n4) * n3);
        NVBindlessMultiDrawIndirect.nglMultiDrawElementsIndirectBindlessNV(n, n2, MemoryUtil.getAddress(byteBuffer), n3, n4, n5, l);
    }

    static native void nglMultiDrawElementsIndirectBindlessNV(int var0, int var1, long var2, int var4, int var5, int var6, long var7);

    public static void glMultiDrawElementsIndirectBindlessNV(int n, int n2, long l, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMultiDrawElementsIndirectBindlessNV;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureIndirectBOenabled(contextCapabilities);
        NVBindlessMultiDrawIndirect.nglMultiDrawElementsIndirectBindlessNVBO(n, n2, l, n3, n4, n5, l2);
    }

    static native void nglMultiDrawElementsIndirectBindlessNVBO(int var0, int var1, long var2, int var4, int var5, int var6, long var7);
}
