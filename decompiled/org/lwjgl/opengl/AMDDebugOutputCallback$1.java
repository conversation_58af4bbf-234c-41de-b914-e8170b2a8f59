/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.opengl.AMDDebugOutputCallback;

class AMDDebugOutputCallback.1
implements AMDDebugOutputCallback.Handler {
    AMDDebugOutputCallback.1() {
    }

    public void handleMessage(int n, int n2, int n3, String string) {
        String string2;
        System.err.println("[LWJGL] AMD_debug_output message");
        System.err.println("\tID: " + n);
        switch (n2) {
            case 37193: {
                string2 = "API ERROR";
                break;
            }
            case 37194: {
                string2 = "WINDOW SYSTEM";
                break;
            }
            case 37195: {
                string2 = "DEPRECATION";
                break;
            }
            case 37196: {
                string2 = "UNDEFINED BEHAVIOR";
                break;
            }
            case 37197: {
                string2 = "PERFORMANCE";
                break;
            }
            case 37198: {
                string2 = "SHADER COMPILER";
                break;
            }
            case 37199: {
                string2 = "APPLICATION";
                break;
            }
            case 37200: {
                string2 = "OTHER";
                break;
            }
            default: {
                string2 = this.printUnknownToken(n2);
            }
        }
        System.err.println("\tCategory: " + string2);
        switch (n3) {
            case 37190: {
                string2 = "HIGH";
                break;
            }
            case 37191: {
                string2 = "MEDIUM";
                break;
            }
            case 37192: {
                string2 = "LOW";
                break;
            }
            default: {
                string2 = this.printUnknownToken(n3);
            }
        }
        System.err.println("\tSeverity: " + string2);
        System.err.println("\tMessage: " + string);
    }

    private String printUnknownToken(int n) {
        return "Unknown (0x" + Integer.toHexString(n).toUpperCase() + ")";
    }
}
