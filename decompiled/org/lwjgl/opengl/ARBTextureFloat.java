/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

public final class ARBTextureFloat {
    public static final int GL_TEXTURE_RED_TYPE_ARB = 35856;
    public static final int GL_TEXTURE_GREEN_TYPE_ARB = 35857;
    public static final int GL_TEXTURE_BLUE_TYPE_ARB = 35858;
    public static final int GL_TEXTURE_ALPHA_TYPE_ARB = 35859;
    public static final int GL_TEXTURE_LUMINANCE_TYPE_ARB = 35860;
    public static final int GL_TEXTURE_INTENSITY_TYPE_ARB = 35861;
    public static final int GL_TEXTURE_DEPTH_TYPE_ARB = 35862;
    public static final int GL_UNSIGNED_NORMALIZED_ARB = 35863;
    public static final int GL_RGBA32F_ARB = 34836;
    public static final int GL_RGB32F_ARB = 34837;
    public static final int GL_ALPHA32F_ARB = 34838;
    public static final int GL_INTENSITY32F_ARB = 34839;
    public static final int GL_LUMINANCE32F_ARB = 34840;
    public static final int GL_LUMINANCE_ALPHA32F_ARB = 34841;
    public static final int GL_RGBA16F_ARB = 34842;
    public static final int GL_RGB16F_ARB = 34843;
    public static final int GL_ALPHA16F_ARB = 34844;
    public static final int GL_INTENSITY16F_ARB = 34845;
    public static final int GL_LUMINANCE16F_ARB = 34846;
    public static final int GL_LUMINANCE_ALPHA16F_ARB = 34847;

    private ARBTextureFloat() {
    }
}
