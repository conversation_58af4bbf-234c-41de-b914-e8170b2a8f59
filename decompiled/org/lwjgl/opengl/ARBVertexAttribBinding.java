/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.opengl.GL43;

public final class ARBVertexAttribBinding {
    public static final int GL_VERTEX_ATTRIB_BINDING = 33492;
    public static final int GL_VERTEX_ATTRIB_RELATIVE_OFFSET = 33493;
    public static final int GL_VERTEX_BINDING_DIVISOR = 33494;
    public static final int GL_VERTEX_BINDING_OFFSET = 33495;
    public static final int GL_VERTEX_BINDING_STRIDE = 33496;
    public static final int GL_MAX_VERTEX_ATTRIB_RELATIVE_OFFSET = 33497;
    public static final int GL_MAX_VERTEX_ATTRIB_BINDINGS = 33498;

    private ARBVertexAttribBinding() {
    }

    public static void glBindVertexBuffer(int n, int n2, long l, int n3) {
        GL43.glBindVertexBuffer(n, n2, l, n3);
    }

    public static void glVertexAttribFormat(int n, int n2, int n3, boolean bl, int n4) {
        GL43.glVertexAttribFormat(n, n2, n3, bl, n4);
    }

    public static void glVertexAttribIFormat(int n, int n2, int n3, int n4) {
        GL43.glVertexAttribIFormat(n, n2, n3, n4);
    }

    public static void glVertexAttribLFormat(int n, int n2, int n3, int n4) {
        GL43.glVertexAttribLFormat(n, n2, n3, n4);
    }

    public static void glVertexAttribBinding(int n, int n2) {
        GL43.glVertexAttribBinding(n, n2);
    }

    public static void glVertexBindingDivisor(int n, int n2) {
        GL43.glVertexBindingDivisor(n, n2);
    }
}
