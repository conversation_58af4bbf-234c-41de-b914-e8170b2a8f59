/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.lwjgl.opengles.EGL
 *  org.lwjgl.opengles.EGLConfig
 *  org.lwjgl.opengles.EGLDisplay
 *  org.lwjgl.opengles.EGLSurface
 *  org.lwjgl.opengles.GLES20
 *  org.lwjgl.opengles.PixelFormat
 *  org.lwjgl.opengles.PowerManagementEventException
 *  org.lwjgl.opengles.Util
 */
package org.lwjgl.opengl;

import org.lwjgl.BufferUtils;
import org.lwjgl.LWJGLException;
import org.lwjgl.LWJGLUtil;
import org.lwjgl.PointerBuffer;
import org.lwjgl.opengl.Context;
import org.lwjgl.opengl.ContextGLES;
import org.lwjgl.opengl.Drawable;
import org.lwjgl.opengl.DrawableLWJGL;
import org.lwjgl.opengl.GlobalLock;
import org.lwjgl.opengl.PixelFormatLWJGL;
import org.lwjgl.opengles.ContextAttribs;
import org.lwjgl.opengles.EGL;
import org.lwjgl.opengles.EGLConfig;
import org.lwjgl.opengles.EGLDisplay;
import org.lwjgl.opengles.EGLSurface;
import org.lwjgl.opengles.GLES20;
import org.lwjgl.opengles.PixelFormat;
import org.lwjgl.opengles.PowerManagementEventException;
import org.lwjgl.opengles.Util;

abstract class DrawableGLES
implements DrawableLWJGL {
    protected PixelFormat pixel_format;
    protected EGLDisplay eglDisplay;
    protected EGLConfig eglConfig;
    protected EGLSurface eglSurface;
    protected ContextGLES context;
    protected Drawable shared_drawable;

    protected DrawableGLES() {
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void setPixelFormat(PixelFormatLWJGL pixelFormatLWJGL) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            this.pixel_format = (PixelFormat)pixelFormatLWJGL;
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public PixelFormatLWJGL getPixelFormat() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            return this.pixel_format;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void initialize(long l, long l2, int n, PixelFormat pixelFormat) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            if (this.eglSurface != null) {
                this.eglSurface.destroy();
                this.eglSurface = null;
            }
            if (this.eglDisplay != null) {
                this.eglDisplay.terminate();
                this.eglDisplay = null;
            }
            EGLDisplay eGLDisplay = EGL.eglGetDisplay((long)((int)l2));
            Object object2 = new int[]{12329, 0, 12352, 4, 12333, 0};
            EGLConfig[] eGLConfigArray = eGLDisplay.chooseConfig(pixelFormat.getAttribBuffer(eGLDisplay, n, object2), null, BufferUtils.createIntBuffer(1));
            object2 = eGLConfigArray;
            if (eGLConfigArray.length == 0) {
                throw new LWJGLException("No EGLConfigs found for the specified PixelFormat.");
            }
            object2 = pixelFormat.getBestMatch((EGLConfig[])object2);
            EGLSurface eGLSurface = eGLDisplay.createWindowSurface((EGLConfig)object2, l, null);
            pixelFormat.setSurfaceAttribs(eGLSurface);
            this.eglDisplay = eGLDisplay;
            this.eglConfig = (EGLConfig)object2;
            this.eglSurface = eGLSurface;
            if (this.context != null) {
                this.context.getEGLContext().setDisplay(eGLDisplay);
            }
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void createContext(ContextAttribs contextAttribs, Drawable drawable) {
        Object object = GlobalLock.lock;
        synchronized (object) {
            this.context = new ContextGLES(this, contextAttribs, drawable != null ? ((DrawableGLES)drawable).getContext() : null);
            this.shared_drawable = drawable;
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    Drawable getSharedDrawable() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            return this.shared_drawable;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public EGLDisplay getEGLDisplay() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            return this.eglDisplay;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public EGLConfig getEGLConfig() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            return this.eglConfig;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public EGLSurface getEGLSurface() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            return this.eglSurface;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public ContextGLES getContext() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            return this.context;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public Context createSharedContext() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            this.checkDestroyed();
            DrawableGLES drawableGLES = this;
            return new ContextGLES(drawableGLES, drawableGLES.context.getContextAttribs(), this.context);
        }
    }

    public void checkGLError() {
        Util.checkGLError();
    }

    public void setSwapInterval(int n) {
        ContextGLES.setSwapInterval(n);
    }

    public void swapBuffers() {
        ContextGLES.swapBuffers();
    }

    public void initContext(float f, float f2, float f3) {
        GLES20.glClearColor((float)f, (float)f2, (float)f3, (float)0.0f);
        GLES20.glClear((int)16384);
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public boolean isCurrent() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            this.checkDestroyed();
            return this.context.isCurrent();
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void makeCurrent() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            this.checkDestroyed();
            this.context.makeCurrent();
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void releaseContext() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            this.checkDestroyed();
            if (this.context.isCurrent()) {
                this.context.releaseCurrent();
            }
            return;
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public void destroy() {
        Object object = GlobalLock.lock;
        synchronized (object) {
            try {
                if (this.context != null) {
                    try {
                        this.releaseContext();
                    }
                    catch (PowerManagementEventException powerManagementEventException) {}
                    this.context.forceDestroy();
                    this.context = null;
                }
                if (this.eglSurface != null) {
                    this.eglSurface.destroy();
                    this.eglSurface = null;
                }
                if (this.eglDisplay != null) {
                    this.eglDisplay.terminate();
                    this.eglDisplay = null;
                }
                this.pixel_format = null;
                this.shared_drawable = null;
            }
            catch (LWJGLException lWJGLException) {
                LWJGLUtil.log("Exception occurred while destroying Drawable: " + lWJGLException);
            }
            return;
        }
    }

    protected void checkDestroyed() {
        if (this.context == null) {
            throw new IllegalStateException("The Drawable has no context available.");
        }
    }

    public void setCLSharingProperties(PointerBuffer pointerBuffer) {
        throw new UnsupportedOperationException();
    }
}
