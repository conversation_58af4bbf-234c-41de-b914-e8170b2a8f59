/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.opengl.GL41;

public final class ARBGetProgramBinary {
    public static final int GL_PROGRAM_BINARY_RETRIEVABLE_HINT = 33367;
    public static final int GL_PROGRAM_BINARY_LENGTH = 34625;
    public static final int GL_NUM_PROGRAM_BINARY_FORMATS = 34814;
    public static final int GL_PROGRAM_BINARY_FORMATS = 34815;

    private ARBGetProgramBinary() {
    }

    public static void glGetProgramBinary(int n, IntBuffer intBuffer, IntBuffer intBuffer2, ByteBuffer byteBuffer) {
        GL41.glGetProgramBinary(n, intBuffer, intBuffer2, byteBuffer);
    }

    public static void glProgramBinary(int n, int n2, ByteBuffer byteBuffer) {
        GL41.glProgramBinary(n, n2, byteBuffer);
    }

    public static void glProgramParameteri(int n, int n2, int n3) {
        GL41.glProgramParameteri(n, n2, n3);
    }
}
