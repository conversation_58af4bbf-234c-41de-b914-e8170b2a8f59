/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import java.nio.LongBuffer;
import org.lwjgl.BufferChecks;
import org.lwjgl.MemoryUtil;
import org.lwjgl.PointerWrapper;
import org.lwjgl.opengl.APIUtil;
import org.lwjgl.opengl.CallbackUtil;
import org.lwjgl.opengl.ContextCapabilities;
import org.lwjgl.opengl.GLChecks;
import org.lwjgl.opengl.GLContext;
import org.lwjgl.opengl.KHRDebugCallback;

public final class GL43 {
    public static final int GL_NUM_SHADING_LANGUAGE_VERSIONS = 33513;
    public static final int GL_VERTEX_ATTRIB_ARRAY_LONG = 34638;
    public static final int GL_COMPRESSED_RGB8_ETC2 = 37492;
    public static final int GL_COMPRESSED_SRGB8_ETC2 = 37493;
    public static final int GL_COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 37494;
    public static final int GL_COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2 = 37495;
    public static final int GL_COMPRESSED_RGBA8_ETC2_EAC = 37496;
    public static final int GL_COMPRESSED_SRGB8_ALPHA8_ETC2_EAC = 37497;
    public static final int GL_COMPRESSED_R11_EAC = 37488;
    public static final int GL_COMPRESSED_SIGNED_R11_EAC = 37489;
    public static final int GL_COMPRESSED_RG11_EAC = 37490;
    public static final int GL_COMPRESSED_SIGNED_RG11_EAC = 37491;
    public static final int GL_PRIMITIVE_RESTART_FIXED_INDEX = 36201;
    public static final int GL_ANY_SAMPLES_PASSED_CONSERVATIVE = 36202;
    public static final int GL_MAX_ELEMENT_INDEX = 36203;
    public static final int GL_COMPUTE_SHADER = 37305;
    public static final int GL_MAX_COMPUTE_UNIFORM_BLOCKS = 37307;
    public static final int GL_MAX_COMPUTE_TEXTURE_IMAGE_UNITS = 37308;
    public static final int GL_MAX_COMPUTE_IMAGE_UNIFORMS = 37309;
    public static final int GL_MAX_COMPUTE_SHARED_MEMORY_SIZE = 33378;
    public static final int GL_MAX_COMPUTE_UNIFORM_COMPONENTS = 33379;
    public static final int GL_MAX_COMPUTE_ATOMIC_COUNTER_BUFFERS = 33380;
    public static final int GL_MAX_COMPUTE_ATOMIC_COUNTERS = 33381;
    public static final int GL_MAX_COMBINED_COMPUTE_UNIFORM_COMPONENTS = 33382;
    public static final int GL_MAX_COMPUTE_WORK_GROUP_INVOCATIONS = 37099;
    public static final int GL_MAX_COMPUTE_WORK_GROUP_COUNT = 37310;
    public static final int GL_MAX_COMPUTE_WORK_GROUP_SIZE = 37311;
    public static final int GL_COMPUTE_WORK_GROUP_SIZE = 33383;
    public static final int GL_UNIFORM_BLOCK_REFERENCED_BY_COMPUTE_SHADER = 37100;
    public static final int GL_ATOMIC_COUNTER_BUFFER_REFERENCED_BY_COMPUTE_SHADER = 37101;
    public static final int GL_DISPATCH_INDIRECT_BUFFER = 37102;
    public static final int GL_DISPATCH_INDIRECT_BUFFER_BINDING = 37103;
    public static final int GL_COMPUTE_SHADER_BIT = 32;
    public static final int GL_DEBUG_OUTPUT = 37600;
    public static final int GL_DEBUG_OUTPUT_SYNCHRONOUS = 33346;
    public static final int GL_CONTEXT_FLAG_DEBUG_BIT = 2;
    public static final int GL_MAX_DEBUG_MESSAGE_LENGTH = 37187;
    public static final int GL_MAX_DEBUG_LOGGED_MESSAGES = 37188;
    public static final int GL_DEBUG_LOGGED_MESSAGES = 37189;
    public static final int GL_DEBUG_NEXT_LOGGED_MESSAGE_LENGTH = 33347;
    public static final int GL_MAX_DEBUG_GROUP_STACK_DEPTH = 33388;
    public static final int GL_DEBUG_GROUP_STACK_DEPTH = 33389;
    public static final int GL_MAX_LABEL_LENGTH = 33512;
    public static final int GL_DEBUG_CALLBACK_FUNCTION = 33348;
    public static final int GL_DEBUG_CALLBACK_USER_PARAM = 33349;
    public static final int GL_DEBUG_SOURCE_API = 33350;
    public static final int GL_DEBUG_SOURCE_WINDOW_SYSTEM = 33351;
    public static final int GL_DEBUG_SOURCE_SHADER_COMPILER = 33352;
    public static final int GL_DEBUG_SOURCE_THIRD_PARTY = 33353;
    public static final int GL_DEBUG_SOURCE_APPLICATION = 33354;
    public static final int GL_DEBUG_SOURCE_OTHER = 33355;
    public static final int GL_DEBUG_TYPE_ERROR = 33356;
    public static final int GL_DEBUG_TYPE_DEPRECATED_BEHAVIOR = 33357;
    public static final int GL_DEBUG_TYPE_UNDEFINED_BEHAVIOR = 33358;
    public static final int GL_DEBUG_TYPE_PORTABILITY = 33359;
    public static final int GL_DEBUG_TYPE_PERFORMANCE = 33360;
    public static final int GL_DEBUG_TYPE_OTHER = 33361;
    public static final int GL_DEBUG_TYPE_MARKER = 33384;
    public static final int GL_DEBUG_TYPE_PUSH_GROUP = 33385;
    public static final int GL_DEBUG_TYPE_POP_GROUP = 33386;
    public static final int GL_DEBUG_SEVERITY_HIGH = 37190;
    public static final int GL_DEBUG_SEVERITY_MEDIUM = 37191;
    public static final int GL_DEBUG_SEVERITY_LOW = 37192;
    public static final int GL_DEBUG_SEVERITY_NOTIFICATION = 33387;
    public static final int GL_BUFFER = 33504;
    public static final int GL_SHADER = 33505;
    public static final int GL_PROGRAM = 33506;
    public static final int GL_QUERY = 33507;
    public static final int GL_PROGRAM_PIPELINE = 33508;
    public static final int GL_SAMPLER = 33510;
    public static final int GL_DISPLAY_LIST = 33511;
    public static final int GL_MAX_UNIFORM_LOCATIONS = 33390;
    public static final int GL_FRAMEBUFFER_DEFAULT_WIDTH = 37648;
    public static final int GL_FRAMEBUFFER_DEFAULT_HEIGHT = 37649;
    public static final int GL_FRAMEBUFFER_DEFAULT_LAYERS = 37650;
    public static final int GL_FRAMEBUFFER_DEFAULT_SAMPLES = 37651;
    public static final int GL_FRAMEBUFFER_DEFAULT_FIXED_SAMPLE_LOCATIONS = 37652;
    public static final int GL_MAX_FRAMEBUFFER_WIDTH = 37653;
    public static final int GL_MAX_FRAMEBUFFER_HEIGHT = 37654;
    public static final int GL_MAX_FRAMEBUFFER_LAYERS = 37655;
    public static final int GL_MAX_FRAMEBUFFER_SAMPLES = 37656;
    public static final int GL_INTERNALFORMAT_SUPPORTED = 33391;
    public static final int GL_INTERNALFORMAT_PREFERRED = 33392;
    public static final int GL_INTERNALFORMAT_RED_SIZE = 33393;
    public static final int GL_INTERNALFORMAT_GREEN_SIZE = 33394;
    public static final int GL_INTERNALFORMAT_BLUE_SIZE = 33395;
    public static final int GL_INTERNALFORMAT_ALPHA_SIZE = 33396;
    public static final int GL_INTERNALFORMAT_DEPTH_SIZE = 33397;
    public static final int GL_INTERNALFORMAT_STENCIL_SIZE = 33398;
    public static final int GL_INTERNALFORMAT_SHARED_SIZE = 33399;
    public static final int GL_INTERNALFORMAT_RED_TYPE = 33400;
    public static final int GL_INTERNALFORMAT_GREEN_TYPE = 33401;
    public static final int GL_INTERNALFORMAT_BLUE_TYPE = 33402;
    public static final int GL_INTERNALFORMAT_ALPHA_TYPE = 33403;
    public static final int GL_INTERNALFORMAT_DEPTH_TYPE = 33404;
    public static final int GL_INTERNALFORMAT_STENCIL_TYPE = 33405;
    public static final int GL_MAX_WIDTH = 33406;
    public static final int GL_MAX_HEIGHT = 33407;
    public static final int GL_MAX_DEPTH = 33408;
    public static final int GL_MAX_LAYERS = 33409;
    public static final int GL_MAX_COMBINED_DIMENSIONS = 33410;
    public static final int GL_COLOR_COMPONENTS = 33411;
    public static final int GL_DEPTH_COMPONENTS = 33412;
    public static final int GL_STENCIL_COMPONENTS = 33413;
    public static final int GL_COLOR_RENDERABLE = 33414;
    public static final int GL_DEPTH_RENDERABLE = 33415;
    public static final int GL_STENCIL_RENDERABLE = 33416;
    public static final int GL_FRAMEBUFFER_RENDERABLE = 33417;
    public static final int GL_FRAMEBUFFER_RENDERABLE_LAYERED = 33418;
    public static final int GL_FRAMEBUFFER_BLEND = 33419;
    public static final int GL_READ_PIXELS = 33420;
    public static final int GL_READ_PIXELS_FORMAT = 33421;
    public static final int GL_READ_PIXELS_TYPE = 33422;
    public static final int GL_TEXTURE_IMAGE_FORMAT = 33423;
    public static final int GL_TEXTURE_IMAGE_TYPE = 33424;
    public static final int GL_GET_TEXTURE_IMAGE_FORMAT = 33425;
    public static final int GL_GET_TEXTURE_IMAGE_TYPE = 33426;
    public static final int GL_MIPMAP = 33427;
    public static final int GL_MANUAL_GENERATE_MIPMAP = 33428;
    public static final int GL_AUTO_GENERATE_MIPMAP = 33429;
    public static final int GL_COLOR_ENCODING = 33430;
    public static final int GL_SRGB_READ = 33431;
    public static final int GL_SRGB_WRITE = 33432;
    public static final int GL_SRGB_DECODE_ARB = 33433;
    public static final int GL_FILTER = 33434;
    public static final int GL_VERTEX_TEXTURE = 33435;
    public static final int GL_TESS_CONTROL_TEXTURE = 33436;
    public static final int GL_TESS_EVALUATION_TEXTURE = 33437;
    public static final int GL_GEOMETRY_TEXTURE = 33438;
    public static final int GL_FRAGMENT_TEXTURE = 33439;
    public static final int GL_COMPUTE_TEXTURE = 33440;
    public static final int GL_TEXTURE_SHADOW = 33441;
    public static final int GL_TEXTURE_GATHER = 33442;
    public static final int GL_TEXTURE_GATHER_SHADOW = 33443;
    public static final int GL_SHADER_IMAGE_LOAD = 33444;
    public static final int GL_SHADER_IMAGE_STORE = 33445;
    public static final int GL_SHADER_IMAGE_ATOMIC = 33446;
    public static final int GL_IMAGE_TEXEL_SIZE = 33447;
    public static final int GL_IMAGE_COMPATIBILITY_CLASS = 33448;
    public static final int GL_IMAGE_PIXEL_FORMAT = 33449;
    public static final int GL_IMAGE_PIXEL_TYPE = 33450;
    public static final int GL_SIMULTANEOUS_TEXTURE_AND_DEPTH_TEST = 33452;
    public static final int GL_SIMULTANEOUS_TEXTURE_AND_STENCIL_TEST = 33453;
    public static final int GL_SIMULTANEOUS_TEXTURE_AND_DEPTH_WRITE = 33454;
    public static final int GL_SIMULTANEOUS_TEXTURE_AND_STENCIL_WRITE = 33455;
    public static final int GL_TEXTURE_COMPRESSED_BLOCK_WIDTH = 33457;
    public static final int GL_TEXTURE_COMPRESSED_BLOCK_HEIGHT = 33458;
    public static final int GL_TEXTURE_COMPRESSED_BLOCK_SIZE = 33459;
    public static final int GL_CLEAR_BUFFER = 33460;
    public static final int GL_TEXTURE_VIEW = 33461;
    public static final int GL_VIEW_COMPATIBILITY_CLASS = 33462;
    public static final int GL_FULL_SUPPORT = 33463;
    public static final int GL_CAVEAT_SUPPORT = 33464;
    public static final int GL_IMAGE_CLASS_4_X_32 = 33465;
    public static final int GL_IMAGE_CLASS_2_X_32 = 33466;
    public static final int GL_IMAGE_CLASS_1_X_32 = 33467;
    public static final int GL_IMAGE_CLASS_4_X_16 = 33468;
    public static final int GL_IMAGE_CLASS_2_X_16 = 33469;
    public static final int GL_IMAGE_CLASS_1_X_16 = 33470;
    public static final int GL_IMAGE_CLASS_4_X_8 = 33471;
    public static final int GL_IMAGE_CLASS_2_X_8 = 33472;
    public static final int GL_IMAGE_CLASS_1_X_8 = 33473;
    public static final int GL_IMAGE_CLASS_11_11_10 = 33474;
    public static final int GL_IMAGE_CLASS_10_10_10_2 = 33475;
    public static final int GL_VIEW_CLASS_128_BITS = 33476;
    public static final int GL_VIEW_CLASS_96_BITS = 33477;
    public static final int GL_VIEW_CLASS_64_BITS = 33478;
    public static final int GL_VIEW_CLASS_48_BITS = 33479;
    public static final int GL_VIEW_CLASS_32_BITS = 33480;
    public static final int GL_VIEW_CLASS_24_BITS = 33481;
    public static final int GL_VIEW_CLASS_16_BITS = 33482;
    public static final int GL_VIEW_CLASS_8_BITS = 33483;
    public static final int GL_VIEW_CLASS_S3TC_DXT1_RGB = 33484;
    public static final int GL_VIEW_CLASS_S3TC_DXT1_RGBA = 33485;
    public static final int GL_VIEW_CLASS_S3TC_DXT3_RGBA = 33486;
    public static final int GL_VIEW_CLASS_S3TC_DXT5_RGBA = 33487;
    public static final int GL_VIEW_CLASS_RGTC1_RED = 33488;
    public static final int GL_VIEW_CLASS_RGTC2_RG = 33489;
    public static final int GL_VIEW_CLASS_BPTC_UNORM = 33490;
    public static final int GL_VIEW_CLASS_BPTC_FLOAT = 33491;
    public static final int GL_UNIFORM = 37601;
    public static final int GL_UNIFORM_BLOCK = 37602;
    public static final int GL_PROGRAM_INPUT = 37603;
    public static final int GL_PROGRAM_OUTPUT = 37604;
    public static final int GL_BUFFER_VARIABLE = 37605;
    public static final int GL_SHADER_STORAGE_BLOCK = 37606;
    public static final int GL_VERTEX_SUBROUTINE = 37608;
    public static final int GL_TESS_CONTROL_SUBROUTINE = 37609;
    public static final int GL_TESS_EVALUATION_SUBROUTINE = 37610;
    public static final int GL_GEOMETRY_SUBROUTINE = 37611;
    public static final int GL_FRAGMENT_SUBROUTINE = 37612;
    public static final int GL_COMPUTE_SUBROUTINE = 37613;
    public static final int GL_VERTEX_SUBROUTINE_UNIFORM = 37614;
    public static final int GL_TESS_CONTROL_SUBROUTINE_UNIFORM = 37615;
    public static final int GL_TESS_EVALUATION_SUBROUTINE_UNIFORM = 37616;
    public static final int GL_GEOMETRY_SUBROUTINE_UNIFORM = 37617;
    public static final int GL_FRAGMENT_SUBROUTINE_UNIFORM = 37618;
    public static final int GL_COMPUTE_SUBROUTINE_UNIFORM = 37619;
    public static final int GL_TRANSFORM_FEEDBACK_VARYING = 37620;
    public static final int GL_ACTIVE_RESOURCES = 37621;
    public static final int GL_MAX_NAME_LENGTH = 37622;
    public static final int GL_MAX_NUM_ACTIVE_VARIABLES = 37623;
    public static final int GL_MAX_NUM_COMPATIBLE_SUBROUTINES = 37624;
    public static final int GL_NAME_LENGTH = 37625;
    public static final int GL_TYPE = 37626;
    public static final int GL_ARRAY_SIZE = 37627;
    public static final int GL_OFFSET = 37628;
    public static final int GL_BLOCK_INDEX = 37629;
    public static final int GL_ARRAY_STRIDE = 37630;
    public static final int GL_MATRIX_STRIDE = 37631;
    public static final int GL_IS_ROW_MAJOR = 37632;
    public static final int GL_ATOMIC_COUNTER_BUFFER_INDEX = 37633;
    public static final int GL_BUFFER_BINDING = 37634;
    public static final int GL_BUFFER_DATA_SIZE = 37635;
    public static final int GL_NUM_ACTIVE_VARIABLES = 37636;
    public static final int GL_ACTIVE_VARIABLES = 37637;
    public static final int GL_REFERENCED_BY_VERTEX_SHADER = 37638;
    public static final int GL_REFERENCED_BY_TESS_CONTROL_SHADER = 37639;
    public static final int GL_REFERENCED_BY_TESS_EVALUATION_SHADER = 37640;
    public static final int GL_REFERENCED_BY_GEOMETRY_SHADER = 37641;
    public static final int GL_REFERENCED_BY_FRAGMENT_SHADER = 37642;
    public static final int GL_REFERENCED_BY_COMPUTE_SHADER = 37643;
    public static final int GL_TOP_LEVEL_ARRAY_SIZE = 37644;
    public static final int GL_TOP_LEVEL_ARRAY_STRIDE = 37645;
    public static final int GL_LOCATION = 37646;
    public static final int GL_LOCATION_INDEX = 37647;
    public static final int GL_IS_PER_PATCH = 37607;
    public static final int GL_SHADER_STORAGE_BUFFER = 37074;
    public static final int GL_SHADER_STORAGE_BUFFER_BINDING = 37075;
    public static final int GL_SHADER_STORAGE_BUFFER_START = 37076;
    public static final int GL_SHADER_STORAGE_BUFFER_SIZE = 37077;
    public static final int GL_MAX_VERTEX_SHADER_STORAGE_BLOCKS = 37078;
    public static final int GL_MAX_GEOMETRY_SHADER_STORAGE_BLOCKS = 37079;
    public static final int GL_MAX_TESS_CONTROL_SHADER_STORAGE_BLOCKS = 37080;
    public static final int GL_MAX_TESS_EVALUATION_SHADER_STORAGE_BLOCKS = 37081;
    public static final int GL_MAX_FRAGMENT_SHADER_STORAGE_BLOCKS = 37082;
    public static final int GL_MAX_COMPUTE_SHADER_STORAGE_BLOCKS = 37083;
    public static final int GL_MAX_COMBINED_SHADER_STORAGE_BLOCKS = 37084;
    public static final int GL_MAX_SHADER_STORAGE_BUFFER_BINDINGS = 37085;
    public static final int GL_MAX_SHADER_STORAGE_BLOCK_SIZE = 37086;
    public static final int GL_SHADER_STORAGE_BUFFER_OFFSET_ALIGNMENT = 37087;
    public static final int GL_SHADER_STORAGE_BARRIER_BIT = 8192;
    public static final int GL_MAX_COMBINED_SHADER_OUTPUT_RESOURCES = 36665;
    public static final int GL_DEPTH_STENCIL_TEXTURE_MODE = 37098;
    public static final int GL_TEXTURE_BUFFER_OFFSET = 37277;
    public static final int GL_TEXTURE_BUFFER_SIZE = 37278;
    public static final int GL_TEXTURE_BUFFER_OFFSET_ALIGNMENT = 37279;
    public static final int GL_TEXTURE_VIEW_MIN_LEVEL = 33499;
    public static final int GL_TEXTURE_VIEW_NUM_LEVELS = 33500;
    public static final int GL_TEXTURE_VIEW_MIN_LAYER = 33501;
    public static final int GL_TEXTURE_VIEW_NUM_LAYERS = 33502;
    public static final int GL_TEXTURE_IMMUTABLE_LEVELS = 33503;
    public static final int GL_VERTEX_ATTRIB_BINDING = 33492;
    public static final int GL_VERTEX_ATTRIB_RELATIVE_OFFSET = 33493;
    public static final int GL_VERTEX_BINDING_DIVISOR = 33494;
    public static final int GL_VERTEX_BINDING_OFFSET = 33495;
    public static final int GL_VERTEX_BINDING_STRIDE = 33496;
    public static final int GL_MAX_VERTEX_ATTRIB_RELATIVE_OFFSET = 33497;
    public static final int GL_MAX_VERTEX_ATTRIB_BINDINGS = 33498;

    private GL43() {
    }

    public static void glClearBufferData(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glClearBufferData;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(byteBuffer, 1);
        GL43.nglClearBufferData(n, n2, n3, n4, MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglClearBufferData(int var0, int var1, int var2, int var3, long var4, long var6);

    public static void glClearBufferSubData(int n, int n2, long l, long l2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glClearBufferSubData;
        BufferChecks.checkFunctionAddress(l3);
        BufferChecks.checkBuffer(byteBuffer, 1);
        GL43.nglClearBufferSubData(n, n2, l, l2, n3, n4, MemoryUtil.getAddress(byteBuffer), l3);
    }

    static native void nglClearBufferSubData(int var0, int var1, long var2, long var4, int var6, int var7, long var8, long var10);

    public static void glDispatchCompute(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDispatchCompute;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglDispatchCompute(n, n2, n3, l);
    }

    static native void nglDispatchCompute(int var0, int var1, int var2, long var3);

    public static void glDispatchComputeIndirect(long l) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glDispatchComputeIndirect;
        BufferChecks.checkFunctionAddress(l2);
        GL43.nglDispatchComputeIndirect(l, l2);
    }

    static native void nglDispatchComputeIndirect(long var0, long var2);

    public static void glCopyImageSubData(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, int n11, int n12, int n13, int n14, int n15) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glCopyImageSubData;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglCopyImageSubData(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, n11, n12, n13, n14, n15, l);
    }

    static native void nglCopyImageSubData(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, int var8, int var9, int var10, int var11, int var12, int var13, int var14, long var15);

    public static void glDebugMessageControl(int n, int n2, int n3, IntBuffer intBuffer, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDebugMessageControl;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkDirect(intBuffer);
        }
        GL43.nglDebugMessageControl(n, n2, n3, intBuffer == null ? 0 : intBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), bl, l);
    }

    static native void nglDebugMessageControl(int var0, int var1, int var2, int var3, long var4, boolean var6, long var7);

    public static void glDebugMessageInsert(int n, int n2, int n3, int n4, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDebugMessageInsert;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        GL43.nglDebugMessageInsert(n, n2, n3, n4, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglDebugMessageInsert(int var0, int var1, int var2, int var3, int var4, long var5, long var7);

    public static void glDebugMessageInsert(int n, int n2, int n3, int n4, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDebugMessageInsert;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglDebugMessageInsert(n, n2, n3, n4, charSequence.length(), APIUtil.getBuffer(contextCapabilities, charSequence), l);
    }

    public static void glDebugMessageCallback(KHRDebugCallback kHRDebugCallback) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glDebugMessageCallback;
        BufferChecks.checkFunctionAddress(l);
        long l2 = kHRDebugCallback == null ? 0L : CallbackUtil.createGlobalRef(kHRDebugCallback.getHandler());
        CallbackUtil.registerContextCallbackKHR(l2);
        GL43.nglDebugMessageCallback(kHRDebugCallback == null ? 0L : kHRDebugCallback.getPointer(), l2, l);
    }

    static native void nglDebugMessageCallback(long var0, long var2, long var4);

    public static int glGetDebugMessageLog(int n, IntBuffer intBuffer, IntBuffer intBuffer2, IntBuffer intBuffer3, IntBuffer intBuffer4, IntBuffer intBuffer5, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetDebugMessageLog;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, n);
        }
        if (intBuffer2 != null) {
            BufferChecks.checkBuffer(intBuffer2, n);
        }
        if (intBuffer3 != null) {
            BufferChecks.checkBuffer(intBuffer3, n);
        }
        if (intBuffer4 != null) {
            BufferChecks.checkBuffer(intBuffer4, n);
        }
        if (intBuffer5 != null) {
            BufferChecks.checkBuffer(intBuffer5, n);
        }
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        n = GL43.nglGetDebugMessageLog(n, byteBuffer == null ? 0 : byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddressSafe(intBuffer2), MemoryUtil.getAddressSafe(intBuffer3), MemoryUtil.getAddressSafe(intBuffer4), MemoryUtil.getAddressSafe(intBuffer5), MemoryUtil.getAddressSafe(byteBuffer), l);
        return n;
    }

    static native int nglGetDebugMessageLog(int var0, int var1, long var2, long var4, long var6, long var8, long var10, long var12, long var14);

    public static void glPushDebugGroup(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPushDebugGroup;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        GL43.nglPushDebugGroup(n, n2, byteBuffer.remaining(), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglPushDebugGroup(int var0, int var1, int var2, long var3, long var5);

    public static void glPushDebugGroup(int n, int n2, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPushDebugGroup;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglPushDebugGroup(n, n2, charSequence.length(), APIUtil.getBuffer(contextCapabilities, charSequence), l);
    }

    public static void glPopDebugGroup() {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glPopDebugGroup;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglPopDebugGroup(l);
    }

    static native void nglPopDebugGroup(long var0);

    public static void glObjectLabel(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glObjectLabel;
        BufferChecks.checkFunctionAddress(l);
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        GL43.nglObjectLabel(n, n2, byteBuffer == null ? 0 : byteBuffer.remaining(), MemoryUtil.getAddressSafe(byteBuffer), l);
    }

    static native void nglObjectLabel(int var0, int var1, int var2, long var3, long var5);

    public static void glObjectLabel(int n, int n2, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glObjectLabel;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglObjectLabel(n, n2, charSequence.length(), APIUtil.getBuffer(contextCapabilities, charSequence), l);
    }

    public static void glGetObjectLabel(int n, int n2, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetObjectLabel;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(byteBuffer);
        GL43.nglGetObjectLabel(n, n2, byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetObjectLabel(int var0, int var1, int var2, long var3, long var5, long var7);

    public static String glGetObjectLabel(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetObjectLabel;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n3);
        GL43.nglGetObjectLabel(n, n2, n3, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static void glObjectPtrLabel(PointerWrapper pointerWrapper, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glObjectPtrLabel;
        BufferChecks.checkFunctionAddress(l);
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        GL43.nglObjectPtrLabel(pointerWrapper.getPointer(), byteBuffer == null ? 0 : byteBuffer.remaining(), MemoryUtil.getAddressSafe(byteBuffer), l);
    }

    static native void nglObjectPtrLabel(long var0, int var2, long var3, long var5);

    public static void glObjectPtrLabel(PointerWrapper pointerWrapper, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glObjectPtrLabel;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglObjectPtrLabel(pointerWrapper.getPointer(), charSequence.length(), APIUtil.getBuffer(contextCapabilities, charSequence), l);
    }

    public static void glGetObjectPtrLabel(PointerWrapper pointerWrapper, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetObjectPtrLabel;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        BufferChecks.checkDirect(byteBuffer);
        GL43.nglGetObjectPtrLabel(pointerWrapper.getPointer(), byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
    }

    static native void nglGetObjectPtrLabel(long var0, int var2, long var3, long var5, long var7);

    public static String glGetObjectPtrLabel(PointerWrapper pointerWrapper, int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetObjectPtrLabel;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n);
        GL43.nglGetObjectPtrLabel(pointerWrapper.getPointer(), n, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static void glFramebufferParameteri(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glFramebufferParameteri;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglFramebufferParameteri(n, n2, n3, l);
    }

    static native void nglFramebufferParameteri(int var0, int var1, int var2, long var3);

    public static void glGetFramebufferParameter(int n, int n2, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetFramebufferParameteriv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL43.nglGetFramebufferParameteriv(n, n2, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetFramebufferParameteriv(int var0, int var1, long var2, long var4);

    public static int glGetFramebufferParameteri(int n, int n2) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetFramebufferParameteriv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL43.nglGetFramebufferParameteriv(n, n2, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static void glGetInternalformat(int n, int n2, int n3, LongBuffer longBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetInternalformati64v;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(longBuffer);
        GL43.nglGetInternalformati64v(n, n2, n3, longBuffer.remaining(), MemoryUtil.getAddress(longBuffer), l);
    }

    static native void nglGetInternalformati64v(int var0, int var1, int var2, int var3, long var4, long var6);

    public static long glGetInternalformati64(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetInternalformati64v;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferLong((ContextCapabilities)object);
        GL43.nglGetInternalformati64v(n, n2, n3, 1, MemoryUtil.getAddress((LongBuffer)object), l);
        return ((LongBuffer)object).get(0);
    }

    public static void glInvalidateTexSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInvalidateTexSubImage;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglInvalidateTexSubImage(n, n2, n3, n4, n5, n6, n7, n8, l);
    }

    static native void nglInvalidateTexSubImage(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8);

    public static void glInvalidateTexImage(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInvalidateTexImage;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglInvalidateTexImage(n, n2, l);
    }

    static native void nglInvalidateTexImage(int var0, int var1, long var2);

    public static void glInvalidateBufferSubData(int n, long l, long l2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glInvalidateBufferSubData;
        BufferChecks.checkFunctionAddress(l3);
        GL43.nglInvalidateBufferSubData(n, l, l2, l3);
    }

    static native void nglInvalidateBufferSubData(int var0, long var1, long var3, long var5);

    public static void glInvalidateBufferData(int n) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInvalidateBufferData;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglInvalidateBufferData(n, l);
    }

    static native void nglInvalidateBufferData(int var0, long var1);

    public static void glInvalidateFramebuffer(int n, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInvalidateFramebuffer;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL43.nglInvalidateFramebuffer(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglInvalidateFramebuffer(int var0, int var1, long var2, long var4);

    public static void glInvalidateSubFramebuffer(int n, IntBuffer intBuffer, int n2, int n3, int n4, int n5) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glInvalidateSubFramebuffer;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        GL43.nglInvalidateSubFramebuffer(n, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), n2, n3, n4, n5, l);
    }

    static native void nglInvalidateSubFramebuffer(int var0, int var1, long var2, int var4, int var5, int var6, int var7, long var8);

    public static void glMultiDrawArraysIndirect(int n, ByteBuffer byteBuffer, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiDrawArraysIndirect;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureIndirectBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, (n3 == 0 ? 16 : n3) * n2);
        GL43.nglMultiDrawArraysIndirect(n, MemoryUtil.getAddress(byteBuffer), n2, n3, l);
    }

    static native void nglMultiDrawArraysIndirect(int var0, long var1, int var3, int var4, long var5);

    public static void glMultiDrawArraysIndirect(int n, long l, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMultiDrawArraysIndirect;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureIndirectBOenabled(contextCapabilities);
        GL43.nglMultiDrawArraysIndirectBO(n, l, n2, n3, l2);
    }

    static native void nglMultiDrawArraysIndirectBO(int var0, long var1, int var3, int var4, long var5);

    public static void glMultiDrawArraysIndirect(int n, IntBuffer intBuffer, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiDrawArraysIndirect;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureIndirectBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(intBuffer, (n3 == 0 ? 4 : n3 >> 2) * n2);
        GL43.nglMultiDrawArraysIndirect(n, MemoryUtil.getAddress(intBuffer), n2, n3, l);
    }

    public static void glMultiDrawElementsIndirect(int n, int n2, ByteBuffer byteBuffer, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiDrawElementsIndirect;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureIndirectBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(byteBuffer, (n4 == 0 ? 20 : n4) * n3);
        GL43.nglMultiDrawElementsIndirect(n, n2, MemoryUtil.getAddress(byteBuffer), n3, n4, l);
    }

    static native void nglMultiDrawElementsIndirect(int var0, int var1, long var2, int var4, int var5, long var6);

    public static void glMultiDrawElementsIndirect(int n, int n2, long l, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glMultiDrawElementsIndirect;
        BufferChecks.checkFunctionAddress(l2);
        GLChecks.ensureIndirectBOenabled(contextCapabilities);
        GL43.nglMultiDrawElementsIndirectBO(n, n2, l, n3, n4, l2);
    }

    static native void nglMultiDrawElementsIndirectBO(int var0, int var1, long var2, int var4, int var5, long var6);

    public static void glMultiDrawElementsIndirect(int n, int n2, IntBuffer intBuffer, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glMultiDrawElementsIndirect;
        BufferChecks.checkFunctionAddress(l);
        GLChecks.ensureIndirectBOdisabled(contextCapabilities);
        BufferChecks.checkBuffer(intBuffer, (n4 == 0 ? 5 : n4 >> 2) * n3);
        GL43.nglMultiDrawElementsIndirect(n, n2, MemoryUtil.getAddress(intBuffer), n3, n4, l);
    }

    public static void glGetProgramInterface(int n, int n2, int n3, IntBuffer intBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramInterfaceiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkBuffer(intBuffer, 1);
        GL43.nglGetProgramInterfaceiv(n, n2, n3, MemoryUtil.getAddress(intBuffer), l);
    }

    static native void nglGetProgramInterfaceiv(int var0, int var1, int var2, long var3, long var5);

    public static int glGetProgramInterfacei(int n, int n2, int n3) {
        Object object = GLContext.getCapabilities();
        long l = ((ContextCapabilities)object).glGetProgramInterfaceiv;
        BufferChecks.checkFunctionAddress(l);
        object = APIUtil.getBufferInt((ContextCapabilities)object);
        GL43.nglGetProgramInterfaceiv(n, n2, n3, MemoryUtil.getAddress((IntBuffer)object), l);
        return ((IntBuffer)object).get(0);
    }

    public static int glGetProgramResourceIndex(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramResourceIndex;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = GL43.nglGetProgramResourceIndex(n, n2, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglGetProgramResourceIndex(int var0, int var1, long var2, long var4);

    public static int glGetProgramResourceIndex(int n, int n2, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramResourceIndex;
        BufferChecks.checkFunctionAddress(l);
        n = GL43.nglGetProgramResourceIndex(n, n2, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }

    public static void glGetProgramResourceName(int n, int n2, int n3, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramResourceName;
        BufferChecks.checkFunctionAddress(l);
        if (intBuffer != null) {
            BufferChecks.checkBuffer(intBuffer, 1);
        }
        if (byteBuffer != null) {
            BufferChecks.checkDirect(byteBuffer);
        }
        GL43.nglGetProgramResourceName(n, n2, n3, byteBuffer == null ? 0 : byteBuffer.remaining(), MemoryUtil.getAddressSafe(intBuffer), MemoryUtil.getAddressSafe(byteBuffer), l);
    }

    static native void nglGetProgramResourceName(int var0, int var1, int var2, int var3, long var4, long var6, long var8);

    public static String glGetProgramResourceName(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramResourceName;
        BufferChecks.checkFunctionAddress(l);
        IntBuffer intBuffer = APIUtil.getLengths(contextCapabilities);
        ByteBuffer byteBuffer = APIUtil.getBufferByte(contextCapabilities, n4);
        GL43.nglGetProgramResourceName(n, n2, n3, n4, MemoryUtil.getAddress0(intBuffer), MemoryUtil.getAddress(byteBuffer), l);
        byteBuffer.limit(intBuffer.get(0));
        return APIUtil.getString(contextCapabilities, byteBuffer);
    }

    public static void glGetProgramResource(int n, int n2, int n3, IntBuffer intBuffer, IntBuffer intBuffer2, IntBuffer intBuffer3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramResourceiv;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(intBuffer);
        if (intBuffer2 != null) {
            BufferChecks.checkBuffer(intBuffer2, 1);
        }
        BufferChecks.checkDirect(intBuffer3);
        GL43.nglGetProgramResourceiv(n, n2, n3, intBuffer.remaining(), MemoryUtil.getAddress(intBuffer), intBuffer3.remaining(), MemoryUtil.getAddressSafe(intBuffer2), MemoryUtil.getAddress(intBuffer3), l);
    }

    static native void nglGetProgramResourceiv(int var0, int var1, int var2, int var3, long var4, int var6, long var7, long var9, long var11);

    public static int glGetProgramResourceLocation(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramResourceLocation;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = GL43.nglGetProgramResourceLocation(n, n2, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglGetProgramResourceLocation(int var0, int var1, long var2, long var4);

    public static int glGetProgramResourceLocation(int n, int n2, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramResourceLocation;
        BufferChecks.checkFunctionAddress(l);
        n = GL43.nglGetProgramResourceLocation(n, n2, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }

    public static int glGetProgramResourceLocationIndex(int n, int n2, ByteBuffer byteBuffer) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramResourceLocationIndex;
        BufferChecks.checkFunctionAddress(l);
        BufferChecks.checkDirect(byteBuffer);
        BufferChecks.checkNullTerminated(byteBuffer);
        n = GL43.nglGetProgramResourceLocationIndex(n, n2, MemoryUtil.getAddress(byteBuffer), l);
        return n;
    }

    static native int nglGetProgramResourceLocationIndex(int var0, int var1, long var2, long var4);

    public static int glGetProgramResourceLocationIndex(int n, int n2, CharSequence charSequence) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glGetProgramResourceLocationIndex;
        BufferChecks.checkFunctionAddress(l);
        n = GL43.nglGetProgramResourceLocationIndex(n, n2, APIUtil.getBufferNT(contextCapabilities, charSequence), l);
        return n;
    }

    public static void glShaderStorageBlockBinding(int n, int n2, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glShaderStorageBlockBinding;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglShaderStorageBlockBinding(n, n2, n3, l);
    }

    static native void nglShaderStorageBlockBinding(int var0, int var1, int var2, long var3);

    public static void glTexBufferRange(int n, int n2, int n3, long l, long l2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l3 = contextCapabilities.glTexBufferRange;
        BufferChecks.checkFunctionAddress(l3);
        GL43.nglTexBufferRange(n, n2, n3, l, l2, l3);
    }

    static native void nglTexBufferRange(int var0, int var1, int var2, long var3, long var5, long var7);

    public static void glTexStorage2DMultisample(int n, int n2, int n3, int n4, int n5, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexStorage2DMultisample;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglTexStorage2DMultisample(n, n2, n3, n4, n5, bl, l);
    }

    static native void nglTexStorage2DMultisample(int var0, int var1, int var2, int var3, int var4, boolean var5, long var6);

    public static void glTexStorage3DMultisample(int n, int n2, int n3, int n4, int n5, int n6, boolean bl) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTexStorage3DMultisample;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglTexStorage3DMultisample(n, n2, n3, n4, n5, n6, bl, l);
    }

    static native void nglTexStorage3DMultisample(int var0, int var1, int var2, int var3, int var4, int var5, boolean var6, long var7);

    public static void glTextureView(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glTextureView;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglTextureView(n, n2, n3, n4, n5, n6, n7, n8, l);
    }

    static native void nglTextureView(int var0, int var1, int var2, int var3, int var4, int var5, int var6, int var7, long var8);

    public static void glBindVertexBuffer(int n, int n2, long l, int n3) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l2 = contextCapabilities.glBindVertexBuffer;
        BufferChecks.checkFunctionAddress(l2);
        GL43.nglBindVertexBuffer(n, n2, l, n3, l2);
    }

    static native void nglBindVertexBuffer(int var0, int var1, long var2, int var4, long var5);

    public static void glVertexAttribFormat(int n, int n2, int n3, boolean bl, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribFormat;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglVertexAttribFormat(n, n2, n3, bl, n4, l);
    }

    static native void nglVertexAttribFormat(int var0, int var1, int var2, boolean var3, int var4, long var5);

    public static void glVertexAttribIFormat(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribIFormat;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglVertexAttribIFormat(n, n2, n3, n4, l);
    }

    static native void nglVertexAttribIFormat(int var0, int var1, int var2, int var3, long var4);

    public static void glVertexAttribLFormat(int n, int n2, int n3, int n4) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribLFormat;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglVertexAttribLFormat(n, n2, n3, n4, l);
    }

    static native void nglVertexAttribLFormat(int var0, int var1, int var2, int var3, long var4);

    public static void glVertexAttribBinding(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexAttribBinding;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglVertexAttribBinding(n, n2, l);
    }

    static native void nglVertexAttribBinding(int var0, int var1, long var2);

    public static void glVertexBindingDivisor(int n, int n2) {
        ContextCapabilities contextCapabilities = GLContext.getCapabilities();
        long l = contextCapabilities.glVertexBindingDivisor;
        BufferChecks.checkFunctionAddress(l);
        GL43.nglVertexBindingDivisor(n, n2, l);
    }

    static native void nglVertexBindingDivisor(int var0, int var1, long var2);
}
