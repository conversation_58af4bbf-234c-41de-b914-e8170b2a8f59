/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import org.lwjgl.BufferUtils;
import org.lwjgl.opengl.EventQueue;
import org.lwjgl.opengl.LinuxDisplay;
import org.lwjgl.opengl.LinuxEvent;

final class LinuxMouse {
    private static final int POINTER_WARP_BORDER = 10;
    private static final int WHEEL_SCALE = 120;
    private int button_count;
    private static final int Button1 = 1;
    private static final int Button2 = 2;
    private static final int Button3 = 3;
    private static final int Button4 = 4;
    private static final int Button5 = 5;
    private static final int Button6 = 6;
    private static final int Button7 = 7;
    private static final int Button8 = 8;
    private static final int Button9 = 9;
    private static final int ButtonPress = 4;
    private static final int ButtonRelease = 5;
    private final long display;
    private final long window;
    private final long input_window;
    private final long warp_atom;
    private final IntBuffer query_pointer_buffer = BufferUtils.createIntBuffer(4);
    private final ByteBuffer event_buffer = ByteBuffer.allocate(22);
    private int last_x;
    private int last_y;
    private int accum_dx;
    private int accum_dy;
    private int accum_dz;
    private byte[] buttons;
    private EventQueue event_queue;
    private long last_event_nanos;

    LinuxMouse(long l, long l2, long l3) {
        this.display = l;
        this.window = l2;
        this.input_window = l3;
        this.warp_atom = LinuxDisplay.nInternAtom(l, "_LWJGL", false);
        this.button_count = LinuxMouse.nGetButtonCount(l);
        this.buttons = new byte[this.button_count];
        this.reset(false, false);
    }

    private void reset(boolean bl, boolean bl2) {
        this.event_queue = new EventQueue(this.event_buffer.capacity());
        LinuxMouse linuxMouse = this;
        linuxMouse.accum_dy = 0;
        linuxMouse.accum_dx = 0;
        long l = LinuxMouse.nQueryPointer(this.display, this.window, this.query_pointer_buffer);
        int n = this.query_pointer_buffer.get(0);
        int n2 = this.query_pointer_buffer.get(1);
        int n3 = this.query_pointer_buffer.get(2);
        int n4 = this.query_pointer_buffer.get(3);
        this.last_x = n3;
        this.last_y = this.transformY(n4);
        this.doHandlePointerMotion(bl, bl2, l, n, n2, n3, n4, this.last_event_nanos);
    }

    public final void read(ByteBuffer byteBuffer) {
        this.event_queue.copyEvents(byteBuffer);
    }

    public final void poll(boolean n, IntBuffer intBuffer, ByteBuffer byteBuffer) {
        if (n != 0) {
            intBuffer.put(0, this.accum_dx);
            intBuffer.put(1, this.accum_dy);
        } else {
            intBuffer.put(0, this.last_x);
            intBuffer.put(1, this.last_y);
        }
        intBuffer.put(2, this.accum_dz);
        LinuxMouse linuxMouse = this;
        this.accum_dz = 0;
        linuxMouse.accum_dy = 0;
        linuxMouse.accum_dx = 0;
        for (n = 0; n < this.buttons.length; ++n) {
            byteBuffer.put(n, this.buttons[n]);
        }
    }

    private void putMouseEventWithCoords(byte by, byte by2, int n, int n2, int n3, long l) {
        this.event_buffer.clear();
        this.event_buffer.put(by).put(by2).putInt(n).putInt(n2).putInt(n3).putLong(l);
        this.event_buffer.flip();
        this.event_queue.putEvent(this.event_buffer);
        this.last_event_nanos = l;
    }

    private void setCursorPos(boolean bl, int n, int n2, long l) {
        n2 = this.transformY(n2);
        int n3 = n - this.last_x;
        int n4 = n2 - this.last_y;
        if (n3 != 0 || n4 != 0) {
            this.accum_dx += n3;
            this.accum_dy += n4;
            this.last_x = n;
            this.last_y = n2;
            if (bl) {
                this.putMouseEventWithCoords((byte)-1, (byte)0, n3, n4, 0, l);
                return;
            }
            this.putMouseEventWithCoords((byte)-1, (byte)0, n, n2, 0, l);
        }
    }

    private void doWarpPointer(int n, int n2) {
        LinuxMouse.nSendWarpEvent(this.display, this.input_window, this.warp_atom, n, n2);
        LinuxMouse.nWarpCursor(this.display, this.window, n, n2);
    }

    private static native void nSendWarpEvent(long var0, long var2, long var4, int var6, int var7);

    /*
     * WARNING - void declaration
     */
    private void doHandlePointerMotion(boolean bl, boolean bl2, long l, int n, int n2, int n3, int n4, long l2) {
        void var6_12;
        void var5_11;
        void var3_9;
        void var2_5;
        void var9_15;
        int n5;
        int n6;
        this.setCursorPos(bl, n6, n5, (long)var9_15);
        if (var2_5 == false) {
            return;
        }
        int n7 = LinuxMouse.nGetWindowHeight(this.display, (long)var3_9);
        int n8 = LinuxMouse.nGetWindowWidth(this.display, (long)var3_9);
        int n9 = LinuxMouse.nGetWindowHeight(this.display, this.window);
        int n10 = LinuxMouse.nGetWindowWidth(this.display, this.window);
        n6 = var5_11 - n6;
        n5 = var6_12 - n5;
        n10 = n6 + n10;
        n9 = n5 + n9;
        n6 = Math.max(0, n6);
        n5 = Math.max(0, n5);
        int n11 = Math.min(n8, n10);
        int n12 = Math.min(n7, n9);
        n9 = var5_11 < n6 + 10 || var6_12 < n5 + 10 || var5_11 > n11 - 10 || var6_12 > n12 - 10 ? 1 : 0;
        if (n9 != 0) {
            int n13 = (n11 - n6) / 2;
            int n14 = (n12 - n5) / 2;
            this.doWarpPointer(n13, n14);
        }
    }

    public final void changeGrabbed(boolean bl, boolean bl2) {
        this.reset(bl, bl2);
    }

    public final int getButtonCount() {
        return this.buttons.length;
    }

    private int transformY(int n) {
        return LinuxMouse.nGetWindowHeight(this.display, this.window) - 1 - n;
    }

    private static native int nGetWindowHeight(long var0, long var2);

    private static native int nGetWindowWidth(long var0, long var2);

    private static native int nGetButtonCount(long var0);

    private static native long nQueryPointer(long var0, long var2, IntBuffer var4);

    public final void setCursorPosition(int n, int n2) {
        LinuxMouse.nWarpCursor(this.display, this.window, n, this.transformY(n2));
    }

    private static native void nWarpCursor(long var0, long var2, int var4, int var5);

    private void handlePointerMotion(boolean bl, boolean bl2, long l, long l2, int n, int n2, int n3, int n4) {
        this.doHandlePointerMotion(bl, bl2, l2, n, n2, n3, n4, l * 1000000L);
    }

    private void handleButton(boolean bl, int n, byte by, long l) {
        switch (n) {
            case 1: {
                n = 0;
                break;
            }
            case 2: {
                n = 2;
                break;
            }
            case 3: {
                n = 1;
                break;
            }
            case 6: {
                n = 5;
                break;
            }
            case 7: {
                n = 6;
                break;
            }
            case 8: {
                n = 3;
                break;
            }
            case 9: {
                n = 4;
                break;
            }
            default: {
                if (n > 9 && n <= this.button_count) {
                    n = (byte)(n - 1);
                    break;
                }
                return;
            }
        }
        this.buttons[n] = by;
        this.putMouseEvent(bl, (byte)n, by, 0, l);
    }

    private void putMouseEvent(boolean bl, byte by, byte by2, int n, long l) {
        if (bl) {
            this.putMouseEventWithCoords(by, by2, 0, 0, n, l);
            return;
        }
        this.putMouseEventWithCoords(by, by2, this.last_x, this.last_y, n, l);
    }

    private void handleButtonPress(boolean bl, byte by, long l) {
        switch (by) {
            case 4: {
                this.putMouseEvent(bl, (byte)-1, (byte)0, 120, l);
                this.accum_dz += 120;
                return;
            }
            case 5: {
                this.putMouseEvent(bl, (byte)-1, (byte)0, -120, l);
                this.accum_dz += -120;
                return;
            }
        }
        this.handleButton(bl, by, (byte)1, l);
    }

    private void handleButtonEvent(boolean bl, long l, int n, byte by) {
        long l2 = l * 1000000L;
        switch (n) {
            case 5: {
                this.handleButton(bl, by, (byte)0, l2);
                return;
            }
            case 4: {
                this.handleButtonPress(bl, by, l2);
            }
        }
    }

    private void resetCursor(int n, int n2) {
        this.last_x = n;
        this.last_y = this.transformY(n2);
    }

    private void handleWarpEvent(int n, int n2) {
        this.resetCursor(n, n2);
    }

    public final boolean filterEvent(boolean bl, boolean bl2, LinuxEvent linuxEvent) {
        switch (linuxEvent.getType()) {
            case 33: {
                if (linuxEvent.getClientMessageType() != this.warp_atom) break;
                this.handleWarpEvent(linuxEvent.getClientData(0), linuxEvent.getClientData(1));
                return true;
            }
            case 4: 
            case 5: {
                this.handleButtonEvent(bl, linuxEvent.getButtonTime(), linuxEvent.getButtonType(), (byte)linuxEvent.getButtonButton());
                return true;
            }
            case 6: {
                this.handlePointerMotion(bl, bl2, linuxEvent.getButtonTime(), linuxEvent.getButtonRoot(), linuxEvent.getButtonXRoot(), linuxEvent.getButtonYRoot(), linuxEvent.getButtonX(), linuxEvent.getButtonY());
                return true;
            }
        }
        return false;
    }
}
