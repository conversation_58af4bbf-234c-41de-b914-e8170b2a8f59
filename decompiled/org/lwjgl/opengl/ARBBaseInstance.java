/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.opengl.GL42;

public final class ARBBaseInstance {
    private ARBBaseInstance() {
    }

    public static void glDrawArraysInstancedBaseInstance(int n, int n2, int n3, int n4, int n5) {
        GL42.glDrawArraysInstancedBaseInstance(n, n2, n3, n4, n5);
    }

    public static void glDrawElementsInstancedBaseInstance(int n, ByteBuffer byteBuffer, int n2, int n3) {
        GL42.glDrawElementsInstancedBaseInstance(n, byteBuffer, n2, n3);
    }

    public static void glDrawElementsInstancedBaseInstance(int n, IntBuffer intBuffer, int n2, int n3) {
        GL42.glDrawElementsInstancedBaseInstance(n, intBuffer, n2, n3);
    }

    public static void glDrawElementsInstancedBaseInstance(int n, ShortBuffer shortBuffer, int n2, int n3) {
        GL42.glDrawElementsInstancedBaseInstance(n, shortBuffer, n2, n3);
    }

    public static void glDrawElementsInstancedBaseInstance(int n, int n2, int n3, long l, int n4, int n5) {
        GL42.glDrawElementsInstancedBaseInstance(n, n2, n3, l, n4, n5);
    }

    public static void glDrawElementsInstancedBaseVertexBaseInstance(int n, ByteBuffer byteBuffer, int n2, int n3, int n4) {
        GL42.glDrawElementsInstancedBaseVertexBaseInstance(n, byteBuffer, n2, n3, n4);
    }

    public static void glDrawElementsInstancedBaseVertexBaseInstance(int n, IntBuffer intBuffer, int n2, int n3, int n4) {
        GL42.glDrawElementsInstancedBaseVertexBaseInstance(n, intBuffer, n2, n3, n4);
    }

    public static void glDrawElementsInstancedBaseVertexBaseInstance(int n, ShortBuffer shortBuffer, int n2, int n3, int n4) {
        GL42.glDrawElementsInstancedBaseVertexBaseInstance(n, shortBuffer, n2, n3, n4);
    }

    public static void glDrawElementsInstancedBaseVertexBaseInstance(int n, int n2, int n3, long l, int n4, int n5, int n6) {
        GL42.glDrawElementsInstancedBaseVertexBaseInstance(n, n2, n3, l, n4, n5, n6);
    }
}
