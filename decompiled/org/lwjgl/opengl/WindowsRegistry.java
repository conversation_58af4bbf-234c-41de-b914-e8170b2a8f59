/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import org.lwjgl.Sys;

final class WindowsRegistry {
    static final int HKEY_CLASSES_ROOT = 1;
    static final int HKEY_CURRENT_USER = 2;
    static final int HKEY_LOCAL_MACHINE = 3;
    static final int HKEY_USERS = 4;

    WindowsRegistry() {
    }

    static String queryRegistrationKey(int n, String string, String string2) {
        switch (n) {
            case 1: 
            case 2: 
            case 3: 
            case 4: {
                break;
            }
            default: {
                throw new IllegalArgumentException("Invalid enum: " + n);
            }
        }
        return WindowsRegistry.nQueryRegistrationKey(n, string, string2);
    }

    private static native String nQueryRegistrationKey(int var0, String var1, String var2);

    static {
        Sys.initialize();
    }
}
