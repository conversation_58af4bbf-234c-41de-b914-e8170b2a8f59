/*
 * Decompiled with CFR 0.152.
 */
package org.lwjgl.opengl;

import java.nio.ByteBuffer;
import java.nio.DoubleBuffer;
import java.nio.FloatBuffer;
import java.nio.IntBuffer;
import java.nio.ShortBuffer;
import org.lwjgl.opengl.GL45;

public final class ARBGetTextureSubImage {
    private ARBGetTextureSubImage() {
    }

    public static void glGetTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, ByteBuffer byteBuffer) {
        GL45.glGetTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, byteBuffer);
    }

    public static void glGetTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, DoubleBuffer doubleBuffer) {
        GL45.glGetTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, doubleBuffer);
    }

    public static void glGetTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, FloatBuffer floatBuffer) {
        GL45.glGetTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, floatBuffer);
    }

    public static void glGetTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, IntBuffer intBuffer) {
        GL45.glGetTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, intBuffer);
    }

    public static void glGetTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, ShortBuffer shortBuffer) {
        GL45.glGetTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, shortBuffer);
    }

    public static void glGetTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, int n10, int n11, long l) {
        GL45.glGetTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, n10, n11, l);
    }

    public static void glGetCompressedTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, ByteBuffer byteBuffer) {
        GL45.glGetCompressedTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, byteBuffer);
    }

    public static void glGetCompressedTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, DoubleBuffer doubleBuffer) {
        GL45.glGetCompressedTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, doubleBuffer);
    }

    public static void glGetCompressedTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, FloatBuffer floatBuffer) {
        GL45.glGetCompressedTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, floatBuffer);
    }

    public static void glGetCompressedTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, IntBuffer intBuffer) {
        GL45.glGetCompressedTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, intBuffer);
    }

    public static void glGetCompressedTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, ShortBuffer shortBuffer) {
        GL45.glGetCompressedTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, shortBuffer);
    }

    public static void glGetCompressedTextureSubImage(int n, int n2, int n3, int n4, int n5, int n6, int n7, int n8, int n9, long l) {
        GL45.glGetCompressedTextureSubImage(n, n2, n3, n4, n5, n6, n7, n8, n9, l);
    }
}
