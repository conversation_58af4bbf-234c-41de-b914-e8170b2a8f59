#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

clear

echo -e "${CYAN}"
echo "╔══════════════════════════════════════╗"
echo "║       WheelBot Agent Setup           ║"
echo "║         macOS Version                ║"
echo "╚══════════════════════════════════════╝"
echo -e "${NC}"

print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This setup script is for macOS only"
    exit 1
fi

print_header "🔍 System Check"

# Check macOS version
MACOS_VERSION=$(sw_vers -productVersion)
MACOS_MAJOR=$(echo $MACOS_VERSION | cut -d. -f1)
MACOS_MINOR=$(echo $MACOS_VERSION | cut -d. -f2)

print_info "macOS Version: $MACOS_VERSION"

if [ "$MACOS_MAJOR" -lt 10 ] || ([ "$MACOS_MAJOR" -eq 10 ] && [ "$MACOS_MINOR" -lt 14 ]); then
    print_warning "macOS 10.14 or higher is recommended"
fi

# Check architecture
ARCH=$(uname -m)
print_info "Architecture: $ARCH"

if [[ "$ARCH" == "arm64" ]]; then
    print_info "Apple Silicon detected"
else
    print_info "Intel Mac detected"
fi

echo ""

# Check Java installation
print_header "☕ Java Check"

if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | head -n 1)
    print_status "Java found: $JAVA_VERSION"
    
    # Check Java version
    JAVA_MAJOR=$(java -version 2>&1 | grep -oE 'version "([0-9]+)' | cut -d'"' -f2)
    if [ "$JAVA_MAJOR" -ge 8 ]; then
        print_status "Java version is compatible"
    else
        print_warning "Java 8 or higher is recommended"
    fi
else
    print_error "Java not found"
    echo ""
    echo "To install Java on macOS:"
    echo "1. Download from: https://www.oracle.com/java/technologies/downloads/"
    echo "2. Or use Homebrew: brew install openjdk"
    echo "3. For Apple Silicon: brew install openjdk@17"
    echo ""
    read -p "Do you want to continue without Java? (y/n): " continue_without_java
    if [[ ! "$continue_without_java" =~ ^[Yy] ]]; then
        exit 1
    fi
fi

if command -v javac &> /dev/null; then
    print_status "Java compiler (javac) found"
else
    print_error "Java compiler (javac) not found"
    echo "Please install JDK (not just JRE)"
fi

echo ""

# Check for required files
print_header "📁 File Check"

required_files=("WheelBotAgent.java" "NetworkHook.java" "WheelBotLauncher.java" "HookVerifier.java" "MANIFEST.MF")
missing_files=()

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_status "$file found"
    else
        print_error "$file missing"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    print_error "Missing required files. Please ensure all source files are present."
    exit 1
fi

# Check for game JAR
if [ -f "core.jar" ]; then
    print_status "Game JAR (core.jar) found"
    
    # Verify MANIFEST.MF in game JAR
    MAIN_CLASS=$(unzip -p core.jar META-INF/MANIFEST.MF 2>/dev/null | grep "Main-Class:" | cut -d' ' -f2 | tr -d '\r\n')
    if [ -n "$MAIN_CLASS" ]; then
        print_status "Main-Class in game JAR: $MAIN_CLASS"
    else
        print_warning "Could not read Main-Class from game JAR"
    fi
else
    print_warning "Game JAR (core.jar) not found"
    echo "Please place the game JAR file as 'core.jar' in this directory"
fi

echo ""

# Make scripts executable
print_header "🔧 Setting up scripts"

scripts=("build-and-run.sh" "launcher-macos.sh" "setup-macos.sh")

for script in "${scripts[@]}"; do
    if [ -f "$script" ]; then
        chmod +x "$script"
        print_status "Made $script executable"
    fi
done

echo ""

# Create desktop shortcuts (optional)
print_header "🖥️  Desktop Integration"

read -p "Create desktop shortcut? (y/n): " create_shortcut

if [[ "$create_shortcut" =~ ^[Yy] ]]; then
    DESKTOP_DIR="$HOME/Desktop"
    CURRENT_DIR=$(pwd)
    
    # Create .command file for macOS
    SHORTCUT_FILE="$DESKTOP_DIR/WheelBot Agent.command"
    
    cat > "$SHORTCUT_FILE" << EOF
#!/bin/bash
cd "$CURRENT_DIR"
./launcher-macos.sh
EOF
    
    chmod +x "$SHORTCUT_FILE"
    print_status "Desktop shortcut created: WheelBot Agent.command"
fi

echo ""

# Build agent
print_header "🔨 Building Agent"

read -p "Build agent now? (y/n): " build_now

if [[ "$build_now" =~ ^[Yy] ]]; then
    print_info "Compiling Java files..."
    
    # Clean previous build
    rm -f *.class wheel-bot-agent.jar
    
    # Compile
    javac -cp . *.java
    
    if [ $? -eq 0 ]; then
        print_status "Compilation successful"
        
        # Create JAR
        print_info "Creating JAR..."
        jar cfm wheel-bot-agent.jar MANIFEST.MF *.class
        
        if [ $? -eq 0 ]; then
            print_status "Agent JAR created successfully"
        else
            print_error "JAR creation failed"
        fi
    else
        print_error "Compilation failed"
    fi
fi

echo ""

# Security settings info
print_header "🔒 Security Information"

echo "On macOS, you may encounter security warnings when running the agent."
echo ""
echo "If you see 'cannot be opened because it is from an unidentified developer':"
echo "1. Go to System Preferences > Security & Privacy"
echo "2. Click 'Allow Anyway' for wheel-bot-agent.jar"
echo "3. Or run: sudo spctl --master-disable (not recommended)"
echo ""
echo "For Gatekeeper issues:"
echo "1. Right-click the JAR file > Open"
echo "2. Click 'Open' in the dialog"
echo "3. Or run: xattr -d com.apple.quarantine wheel-bot-agent.jar"
echo ""

# Final instructions
print_header "🎉 Setup Complete!"

echo "Setup completed successfully!"
echo ""
echo "Next steps:"
echo "1. Place your game JAR as 'core.jar' in this directory (if not already done)"
echo "2. Run the launcher: ./launcher-macos.sh"
echo "3. Or use the desktop shortcut if created"
echo ""
echo "Quick start commands:"
echo "  ./launcher-macos.sh           - Interactive launcher"
echo "  ./launcher-macos.sh --auto    - Launch with auto mode"
echo "  ./launcher-macos.sh --manual  - Launch with manual mode"
echo "  ./launcher-macos.sh --debug   - Launch with debug mode"
echo "  ./build-and-run.sh           - Build and run directly"
echo ""

# Check for Homebrew (optional)
if command -v brew &> /dev/null; then
    print_info "Homebrew detected - you can install Java with: brew install openjdk"
else
    print_info "Consider installing Homebrew for easier Java management: https://brew.sh"
fi

echo ""
print_status "Setup completed! You can now run the WheelBot Agent."

read -p "Press Enter to exit..."
