package com.langla.bot;

import java.lang.instrument.Instrumentation;
import java.lang.instrument.ClassFileTransformer;
import java.security.ProtectionDomain;
import java.lang.reflect.Method;
import java.lang.reflect.Field;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.Scanner;

/**
 * WheelBot Agent - Hook chức năng quay bách bảo rương
 * Dựa trên phân tích code từ kb.java, io.java, aw.java
 */
public class WheelBotAgent {
    private static Instrumentation instrumentation;
    private static boolean initialized = false;
    private static boolean autoMode = true;
    private static boolean isProcessing = false;
    private static int spinDelay = 3000;
    private static boolean debugMode = false;
    
    // Cached objects từ game
    private static Object gameInstance;
    private static Object wheelInstance;
    private static Class<?> kbClass; // Class kb.java - main wheel UI
    private static Class<?> akClass; // Class ak.java - network message
    private static Class<?> awClass; // Class aw.java - utilities
    
    private static ScheduledExecutorService scheduler;
    
    public static void premain(String agentArgs, Instrumentation inst) {
        instrumentation = inst;
        initialize(agentArgs);
    }
    
    public static void agentmain(String agentArgs, Instrumentation inst) {
        instrumentation = inst;
        initialize(agentArgs);
    }
    
    private static void initialize(String args) {
        if (initialized) return;
        
        System.out.println("[WheelBot] Khởi tạo agent...");
        
        try {
            parseArguments(args);
            setupClassTransformer();
            startAutomationEngine();
            setupConsoleHandler();
            
            initialized = true;
            System.out.println("[WheelBot] Agent khởi tạo thành công!");
            System.out.println("[WheelBot] Commands: start, stop, status, spin, debug, help, exit");
            
        } catch (Exception e) {
            System.err.println("[WheelBot] Lỗi khởi tạo: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void parseArguments(String args) {
        if (args == null || args.isEmpty()) return;
        
        String[] params = args.split(",");
        for (String param : params) {
            String[] kv = param.split("=");
            if (kv.length == 2) {
                switch (kv[0].trim()) {
                    case "debug":
                        debugMode = Boolean.parseBoolean(kv[1]);
                        break;
                    case "delay":
                        spinDelay = Integer.parseInt(kv[1]);
                        break;
                    case "auto":
                        autoMode = Boolean.parseBoolean(kv[1]);
                        break;
                }
            }
        }
        
        if (debugMode) {
            System.out.println("[WheelBot] Debug mode: " + debugMode);
            System.out.println("[WheelBot] Spin delay: " + spinDelay + "ms");
            System.out.println("[WheelBot] Auto mode: " + autoMode);
        }
    }
    
    private static void setupClassTransformer() {
        instrumentation.addTransformer(new ClassFileTransformer() {
            @Override
            public byte[] transform(ClassLoader loader, String className, 
                                  Class<?> classBeingRedefined, 
                                  ProtectionDomain protectionDomain, 
                                  byte[] classfileBuffer) {
                
                // Hook vào class kb (wheel UI)
                if (className != null && className.contains("com/donglh/narutoninjasaga/d/kb")) {
                    try {
                        kbClass = Class.forName("com.donglh.narutoninjasaga.d.kb");
                        if (debugMode) {
                            System.out.println("[WheelBot] Hooked kb class (Wheel UI)");
                        }
                    } catch (Exception e) {
                        if (debugMode) System.err.println("[WheelBot] Hook kb error: " + e.getMessage());
                    }
                }
                
                // Hook vào class ak (network message)
                if (className != null && className.contains("com/donglh/narutoninjasaga/e/ak")) {
                    try {
                        akClass = Class.forName("com.donglh.narutoninjasaga.e.ak");
                        if (debugMode) {
                            System.out.println("[WheelBot] Hooked ak class (Network)");
                        }
                    } catch (Exception e) {
                        if (debugMode) System.err.println("[WheelBot] Hook ak error: " + e.getMessage());
                    }
                }
                
                // Hook vào class aw (utilities)
                if (className != null && className.contains("com/donglh/narutoninjasaga/e/aw")) {
                    try {
                        awClass = Class.forName("com.donglh.narutoninjasaga.e.aw");
                        if (debugMode) {
                            System.out.println("[WheelBot] Hooked aw class (Utils)");
                        }
                    } catch (Exception e) {
                        if (debugMode) System.err.println("[WheelBot] Hook aw error: " + e.getMessage());
                    }
                }
                
                return classfileBuffer;
            }
        }, true);
    }
    
    private static void startAutomationEngine() {
        scheduler = Executors.newScheduledThreadPool(2);
        
        // Kiểm tra điều kiện quay mỗi 10 giây
        scheduler.scheduleWithFixedDelay(() -> {
            if (autoMode && !isProcessing) {
                try {
                    checkAndExecuteAutoSpin();
                } catch (Exception e) {
                    if (debugMode) {
                        System.err.println("[WheelBot] Auto check error: " + e.getMessage());
                    }
                }
            }
        }, 10, 10, TimeUnit.SECONDS);
        
        if (debugMode) {
            System.out.println("[WheelBot] Automation engine started");
        }
    }
    
    private static void checkAndExecuteAutoSpin() {
        try {
            // Tìm instance của game đang chạy
            if (gameInstance == null) {
                findGameInstance();
            }
            
            if (gameInstance == null) return;
            
            // Kiểm tra xem có đang ở giao diện wheel không
            if (isWheelInterfaceActive()) {
                executeAutoSpin();
            }
            
        } catch (Exception e) {
            if (debugMode) {
                System.err.println("[WheelBot] Check condition error: " + e.getMessage());
            }
        }
    }
    
    private static void findGameInstance() {
        try {
            // Tìm instance của game thông qua static methods
            if (kbClass != null) {
                // Tìm static instance hoặc current instance
                Field[] fields = kbClass.getDeclaredFields();
                for (Field field : fields) {
                    if (field.getType() == kbClass && java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
                        field.setAccessible(true);
                        gameInstance = field.get(null);
                        if (gameInstance != null) {
                            if (debugMode) {
                                System.out.println("[WheelBot] Found game instance");
                            }
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            if (debugMode) {
                System.err.println("[WheelBot] Find game instance error: " + e.getMessage());
            }
        }
    }
    
    private static boolean isWheelInterfaceActive() {
        try {
            if (wheelInstance == null && kbClass != null) {
                // Tìm current wheel instance
                wheelInstance = findCurrentWheelInstance();
            }
            
            return wheelInstance != null;
        } catch (Exception e) {
            return false;
        }
    }
    
    private static Object findCurrentWheelInstance() {
        try {
            // Tìm instance hiện tại của wheel UI
            if (kbClass != null) {
                // Kiểm tra các static fields hoặc singleton pattern
                Method[] methods = kbClass.getDeclaredMethods();
                for (Method method : methods) {
                    if (method.getReturnType() == kbClass && 
                        java.lang.reflect.Modifier.isStatic(method.getModifiers()) &&
                        method.getParameterCount() == 0) {
                        method.setAccessible(true);
                        return method.invoke(null);
                    }
                }
            }
        } catch (Exception e) {
            if (debugMode) {
                System.err.println("[WheelBot] Find wheel instance error: " + e.getMessage());
            }
        }
        return null;
    }
    
    private static void executeAutoSpin() {
        if (isProcessing) return;
        
        isProcessing = true;
        System.out.println("[WheelBot] Bắt đầu auto spin...");
        
        scheduler.schedule(() -> {
            try {
                // Gửi message quay
                sendSpinMessage();
                
                // Đợi kết quả và claim reward
                scheduler.schedule(() -> {
                    try {
                        claimRewards();
                        System.out.println("[WheelBot] Hoàn thành auto spin");
                    } catch (Exception e) {
                        System.err.println("[WheelBot] Claim error: " + e.getMessage());
                    } finally {
                        isProcessing = false;
                    }
                }, getRandomDelay(2000, 4000), TimeUnit.MILLISECONDS);
                
            } catch (Exception e) {
                System.err.println("[WheelBot] Spin error: " + e.getMessage());
                isProcessing = false;
            }
        }, getRandomDelay(1000, 2000), TimeUnit.MILLISECONDS);
    }
    
    private static void sendSpinMessage() throws Exception {
        if (akClass == null) {
            throw new Exception("Network class not found");
        }
        
        // Tạo message quay rương (dựa trên phân tích kb.java)
        Object message = createNetworkMessage((byte) -123); // Message type cho quay
        
        if (message != null) {
            // Gửi message
            Method sendMethod = findMethod(akClass, "l"); // Method l() để send
            if (sendMethod != null) {
                sendMethod.setAccessible(true);
                sendMethod.invoke(message);
                System.out.println("[WheelBot] Đã gửi message quay");
            }
        }
    }
    
    private static Object createNetworkMessage(byte messageType) throws Exception {
        if (akClass == null) return null;
        
        // Tạo message instance
        java.lang.reflect.Constructor<?> constructor = akClass.getDeclaredConstructor(byte.class);
        constructor.setAccessible(true);
        return constructor.newInstance(messageType);
    }
    
    private static void claimRewards() throws Exception {
        // Claim các reward sau khi quay
        System.out.println("[WheelBot] Claiming rewards...");
        
        // Có thể cần gửi thêm message để claim
        // Dựa trên phân tích, có thể cần call method e() trong kb.java
        if (wheelInstance != null) {
            Method claimMethod = findMethod(kbClass, "e");
            if (claimMethod != null) {
                claimMethod.setAccessible(true);
                claimMethod.invoke(wheelInstance);
            }
        }
    }
    
    private static Method findMethod(Class<?> clazz, String methodName) {
        try {
            Method[] methods = clazz.getDeclaredMethods();
            for (Method method : methods) {
                if (method.getName().equals(methodName)) {
                    return method;
                }
            }
        } catch (Exception e) {
            if (debugMode) {
                System.err.println("[WheelBot] Find method error: " + e.getMessage());
            }
        }
        return null;
    }
    
    private static long getRandomDelay(long min, long max) {
        return min + (long) (Math.random() * (max - min));
    }
    
    private static void setupConsoleHandler() {
        Thread consoleThread = new Thread(() -> {
            Scanner scanner = new Scanner(System.in);
            while (true) {
                try {
                    String command = scanner.nextLine().trim().toLowerCase();
                    handleConsoleCommand(command);
                } catch (Exception e) {
                    // Ignore scanner exceptions
                }
            }
        });
        consoleThread.setDaemon(true);
        consoleThread.start();
    }
    
    private static void handleConsoleCommand(String command) {
        switch (command) {
            case "start":
                autoMode = true;
                System.out.println("[WheelBot] Auto mode BẬT");
                break;
            case "stop":
                autoMode = false;
                System.out.println("[WheelBot] Auto mode TẮT");
                break;
            case "status":
                System.out.println("[WheelBot] Auto: " + autoMode + ", Processing: " + isProcessing);
                System.out.println("[WheelBot] Delay: " + spinDelay + "ms, Debug: " + debugMode);
                break;
            case "spin":
                if (!isProcessing) {
                    executeAutoSpin();
                } else {
                    System.out.println("[WheelBot] Đang xử lý, vui lòng đợi...");
                }
                break;
            case "debug":
                debugMode = !debugMode;
                System.out.println("[WheelBot] Debug mode: " + debugMode);
                break;
            case "help":
                System.out.println("[WheelBot] Commands:");
                System.out.println("  start  - Bật auto mode");
                System.out.println("  stop   - Tắt auto mode");
                System.out.println("  status - Xem trạng thái");
                System.out.println("  spin   - Quay thủ công");
                System.out.println("  debug  - Toggle debug mode");
                System.out.println("  help   - Hiện menu này");
                System.out.println("  exit   - Thoát");
                break;
            case "exit":
                System.out.println("[WheelBot] Đang thoát...");
                if (scheduler != null) {
                    scheduler.shutdown();
                }
                System.exit(0);
                break;
            default:
                if (!command.isEmpty()) {
                    System.out.println("[WheelBot] Lệnh không hợp lệ. Gõ 'help' để xem danh sách lệnh.");
                }
                break;
        }
    }
}
