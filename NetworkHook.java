package com.langla.bot;

import java.lang.reflect.Method;
import java.lang.reflect.Field;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * NetworkHook - Xử lý hook network layer và message processing
 * Dựa trên phân tích ak.java và message protocol
 */
public class NetworkHook {
    private static final Map<String, Object> messageCache = new ConcurrentHashMap<>();
    private static final Map<String, Method> methodCache = new ConcurrentHashMap<>();
    private static boolean hookInstalled = false;
    private static boolean debugMode = false;
    
    // Message types dựa trên phân tích
    private static final byte MSG_WHEEL_OPEN = -123;
    private static final byte MSG_WHEEL_SPIN = -124;
    private static final byte MSG_WHEEL_RESULT = -125;
    private static final byte MSG_WHEEL_CLAIM = -126;
    
    public static void setDebugMode(boolean debug) {
        debugMode = debug;
    }
    
    /**
     * Cài đặt hook cho network layer
     */
    public static void installHooks() {
        if (hookInstalled) return;
        
        try {
            hookMessageClass();
            hookWheelClass();
            hookInstalled = true;
            
            if (debugMode) {
                System.out.println("[NetworkHook] Network hooks installed successfully");
            }
        } catch (Exception e) {
            System.err.println("[NetworkHook] Failed to install hooks: " + e.getMessage());
        }
    }
    
    /**
     * Hook vào class ak.java (message class)
     */
    private static void hookMessageClass() throws Exception {
        Class<?> akClass = findClass("com.donglh.narutoninjasaga.e.ak");
        if (akClass == null) return;
        
        // Cache các method quan trọng
        cacheMethod(akClass, "l", "sendMessage"); // Method gửi message
        cacheMethod(akClass, "k", "sendAndWait"); // Method gửi và đợi
        cacheMethod(akClass, "a", "writeData"); // Method ghi data
        cacheMethod(akClass, "c", "cleanup"); // Method cleanup
        
        if (debugMode) {
            System.out.println("[NetworkHook] Message class hooked: " + akClass.getName());
        }
    }
    
    /**
     * Hook vào class kb.java (wheel UI class)
     */
    private static void hookWheelClass() throws Exception {
        Class<?> kbClass = findClass("com.donglh.narutoninjasaga.d.kb");
        if (kbClass == null) return;
        
        // Cache các method quan trọng của wheel
        cacheMethod(kbClass, "a", "handleMessage"); // Method xử lý message
        cacheMethod(kbClass, "e", "executeAction"); // Method thực hiện action
        cacheMethod(kbClass, "b", "updateUI"); // Method update UI
        cacheMethod(kbClass, "p", "processResult"); // Method xử lý kết quả
        
        if (debugMode) {
            System.out.println("[NetworkHook] Wheel class hooked: " + kbClass.getName());
        }
    }
    
    /**
     * Tìm class theo tên
     */
    private static Class<?> findClass(String className) {
        try {
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            if (debugMode) {
                System.err.println("[NetworkHook] Class not found: " + className);
            }
            return null;
        }
    }
    
    /**
     * Cache method để sử dụng sau
     */
    private static void cacheMethod(Class<?> clazz, String methodName, String alias) {
        try {
            Method[] methods = clazz.getDeclaredMethods();
            for (Method method : methods) {
                if (method.getName().equals(methodName)) {
                    method.setAccessible(true);
                    methodCache.put(alias, method);
                    methodCache.put(clazz.getName() + "." + methodName, method);
                    break;
                }
            }
        } catch (Exception e) {
            if (debugMode) {
                System.err.println("[NetworkHook] Failed to cache method " + methodName + ": " + e.getMessage());
            }
        }
    }
    
    /**
     * Tạo message mới
     */
    public static Object createMessage(byte messageType) {
        try {
            Class<?> akClass = findClass("com.donglh.narutoninjasaga.e.ak");
            if (akClass == null) return null;
            
            // Tạo instance mới
            java.lang.reflect.Constructor<?> constructor = akClass.getDeclaredConstructor(byte.class);
            constructor.setAccessible(true);
            Object message = constructor.newInstance(messageType);
            
            if (debugMode) {
                System.out.println("[NetworkHook] Created message type: " + messageType);
            }
            
            return message;
        } catch (Exception e) {
            System.err.println("[NetworkHook] Failed to create message: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Gửi message wheel spin
     */
    public static boolean sendWheelSpinMessage() {
        try {
            Object message = createMessage(MSG_WHEEL_SPIN);
            if (message == null) return false;
            
            // Ghi data cho message (nếu cần)
            writeMessageData(message, (byte) 0); // Spin type
            
            // Gửi message
            return sendMessage(message);
        } catch (Exception e) {
            System.err.println("[NetworkHook] Failed to send wheel spin: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Gửi message claim reward
     */
    public static boolean sendClaimRewardMessage(int rewardType) {
        try {
            Object message = createMessage(MSG_WHEEL_CLAIM);
            if (message == null) return false;
            
            // Ghi data cho message
            writeMessageData(message, (byte) rewardType);
            
            // Gửi message
            return sendMessage(message);
        } catch (Exception e) {
            System.err.println("[NetworkHook] Failed to send claim reward: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Ghi data vào message
     */
    private static void writeMessageData(Object message, byte data) throws Exception {
        Method writeMethod = methodCache.get("writeData");
        if (writeMethod != null) {
            writeMethod.invoke(message, data);
        } else {
            // Fallback: tìm method write khác
            Method[] methods = message.getClass().getDeclaredMethods();
            for (Method method : methods) {
                if (method.getName().equals("a") && method.getParameterCount() == 1) {
                    method.setAccessible(true);
                    method.invoke(message, data);
                    break;
                }
            }
        }
    }
    
    /**
     * Gửi message
     */
    private static boolean sendMessage(Object message) throws Exception {
        Method sendMethod = methodCache.get("sendMessage");
        if (sendMethod != null) {
            sendMethod.invoke(message);
            if (debugMode) {
                System.out.println("[NetworkHook] Message sent successfully");
            }
            return true;
        } else {
            // Fallback: tìm method send khác
            Method[] methods = message.getClass().getDeclaredMethods();
            for (Method method : methods) {
                if (method.getName().equals("l") && method.getParameterCount() == 0) {
                    method.setAccessible(true);
                    method.invoke(message);
                    if (debugMode) {
                        System.out.println("[NetworkHook] Message sent via fallback");
                    }
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * Kiểm tra xem có đang ở giao diện wheel không
     */
    public static boolean isWheelInterfaceActive() {
        try {
            Class<?> kbClass = findClass("com.donglh.narutoninjasaga.d.kb");
            if (kbClass == null) return false;
            
            // Tìm instance hiện tại
            Object wheelInstance = findWheelInstance(kbClass);
            if (wheelInstance == null) return false;
            
            // Kiểm tra trạng thái active
            return isInstanceActive(wheelInstance);
            
        } catch (Exception e) {
            if (debugMode) {
                System.err.println("[NetworkHook] Check wheel interface error: " + e.getMessage());
            }
            return false;
        }
    }
    
    /**
     * Tìm instance của wheel UI
     */
    private static Object findWheelInstance(Class<?> kbClass) throws Exception {
        // Tìm static instance
        Field[] fields = kbClass.getDeclaredFields();
        for (Field field : fields) {
            if (field.getType() == kbClass && java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
                field.setAccessible(true);
                Object instance = field.get(null);
                if (instance != null) {
                    return instance;
                }
            }
        }
        
        // Tìm thông qua singleton pattern
        Method[] methods = kbClass.getDeclaredMethods();
        for (Method method : methods) {
            if (method.getReturnType() == kbClass && 
                java.lang.reflect.Modifier.isStatic(method.getModifiers()) &&
                method.getParameterCount() == 0) {
                method.setAccessible(true);
                return method.invoke(null);
            }
        }
        
        return null;
    }
    
    /**
     * Kiểm tra instance có active không
     */
    private static boolean isInstanceActive(Object instance) throws Exception {
        // Kiểm tra field boolean để xác định trạng thái
        Field[] fields = instance.getClass().getDeclaredFields();
        for (Field field : fields) {
            if (field.getType() == boolean.class) {
                field.setAccessible(true);
                Boolean value = (Boolean) field.get(instance);
                if (value != null && value) {
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * Thực hiện action trên wheel UI
     */
    public static boolean executeWheelAction(String actionName) {
        try {
            Class<?> kbClass = findClass("com.donglh.narutoninjasaga.d.kb");
            if (kbClass == null) return false;
            
            Object wheelInstance = findWheelInstance(kbClass);
            if (wheelInstance == null) return false;
            
            Method actionMethod = methodCache.get("executeAction");
            if (actionMethod == null) {
                // Tìm method e() - dựa trên phân tích
                actionMethod = findMethodByName(kbClass, "e");
            }
            
            if (actionMethod != null) {
                actionMethod.setAccessible(true);
                actionMethod.invoke(wheelInstance);
                
                if (debugMode) {
                    System.out.println("[NetworkHook] Executed wheel action: " + actionName);
                }
                return true;
            }
            
        } catch (Exception e) {
            System.err.println("[NetworkHook] Failed to execute wheel action: " + e.getMessage());
        }
        return false;
    }
    
    /**
     * Tìm method theo tên
     */
    private static Method findMethodByName(Class<?> clazz, String methodName) {
        try {
            Method[] methods = clazz.getDeclaredMethods();
            for (Method method : methods) {
                if (method.getName().equals(methodName)) {
                    return method;
                }
            }
        } catch (Exception e) {
            if (debugMode) {
                System.err.println("[NetworkHook] Find method error: " + e.getMessage());
            }
        }
        return null;
    }
    
    /**
     * Lấy thông tin trạng thái wheel
     */
    public static String getWheelStatus() {
        try {
            if (!isWheelInterfaceActive()) {
                return "Wheel interface not active";
            }
            
            Class<?> kbClass = findClass("com.donglh.narutoninjasaga.d.kb");
            Object wheelInstance = findWheelInstance(kbClass);
            
            if (wheelInstance != null) {
                // Đọc các field để lấy thông tin trạng thái
                StringBuilder status = new StringBuilder();
                status.append("Wheel Status: Active");
                
                // Thêm thông tin chi tiết nếu cần
                Field[] fields = wheelInstance.getClass().getDeclaredFields();
                for (Field field : fields) {
                    if (field.getType() == int.class || field.getType() == boolean.class) {
                        field.setAccessible(true);
                        Object value = field.get(wheelInstance);
                        status.append(", ").append(field.getName()).append(": ").append(value);
                    }
                }
                
                return status.toString();
            }
            
        } catch (Exception e) {
            if (debugMode) {
                System.err.println("[NetworkHook] Get wheel status error: " + e.getMessage());
            }
        }
        
        return "Wheel status unknown";
    }
    
    /**
     * Cleanup resources
     */
    public static void cleanup() {
        messageCache.clear();
        methodCache.clear();
        hookInstalled = false;
        
        if (debugMode) {
            System.out.println("[NetworkHook] Cleanup completed");
        }
    }
}
